use std::fs;
use std::path::{Path, PathBuf};
use walkdir::{WalkDir, DirEntry};
use crate::cli::{CliError, CliResult};

#[derive(Debug, Clone)]
pub struct DirectoryTraversalOptions {
    pub recursive: bool,
    pub max_depth: Option<usize>,
    pub follow_links: bool,
    pub skip_hidden: bool,
}

impl Default for DirectoryTraversalOptions {
    fn default() -> Self {
        Self {
            recursive: true,
            max_depth: None,
            follow_links: false,
            skip_hidden: false,
        }
    }
}

#[derive(Debug)]
pub struct DirectoryEntry {
    pub path: PathBuf,
    pub relative_path: PathBuf,
    pub is_file: bool,
    pub size: u64,
    pub modified: Option<std::time::SystemTime>,
}

#[derive(Clone)]
pub struct DirectoryTraverser {
    options: DirectoryTraversalOptions,
}

impl DirectoryTraverser {
    pub fn new(options: DirectoryTraversalOptions) -> Self {
        Self { options }
    }

    pub fn traverse(&self, root: &Path) -> CliResult<Vec<DirectoryEntry>> {
        if !root.exists() {
            return Err(CliError::FileNotFound(root.to_path_buf()));
        }

        if !root.is_dir() {
            return Err(CliError::Config(format!(
                "Path is not a directory: {}",
                root.display()
            )));
        }

        let mut entries = Vec::new();
        let walker = self.build_walker(root);

        for entry in walker {
            match entry {
                Ok(dir_entry) => {
                    if let Some(entry) = self.process_entry(&dir_entry, root)? {
                        entries.push(entry);
                    }
                }
                Err(e) => {
                    // Log error but continue traversal
                    eprintln!("Warning: Error accessing path: {}", e);
                }
            }
        }

        Ok(entries)
    }

    pub fn traverse_files(&self, root: &Path) -> CliResult<Vec<PathBuf>> {
        let entries = self.traverse(root)?;
        Ok(entries
            .into_iter()
            .filter(|e| e.is_file)
            .map(|e| e.path)
            .collect())
    }

    pub fn traverse_with_filter<F>(&self, root: &Path, filter: F) -> CliResult<Vec<DirectoryEntry>>
    where
        F: Fn(&DirectoryEntry) -> bool,
    {
        let entries = self.traverse(root)?;
        Ok(entries.into_iter().filter(filter).collect())
    }

    fn build_walker(&self, root: &Path) -> WalkDir {
        let mut walker = WalkDir::new(root)
            .follow_links(self.options.follow_links);

        if !self.options.recursive {
            walker = walker.max_depth(1);
        } else if let Some(max_depth) = self.options.max_depth {
            walker = walker.max_depth(max_depth);
        }

        walker
    }

    fn process_entry(&self, entry: &DirEntry, root: &Path) -> CliResult<Option<DirectoryEntry>> {
        let path = entry.path();
        
        // Skip hidden files if requested
        if self.options.skip_hidden {
            if let Some(name) = path.file_name() {
                if name.to_string_lossy().starts_with('.') {
                    return Ok(None);
                }
            }
        }

        let metadata = entry.metadata()?;
        let relative_path = path.strip_prefix(root)
            .unwrap_or(path)
            .to_path_buf();

        Ok(Some(DirectoryEntry {
            path: path.to_path_buf(),
            relative_path,
            is_file: metadata.is_file(),
            size: metadata.len(),
            modified: metadata.modified().ok(),
        }))
    }

    pub fn calculate_relative_output_path(
        &self,
        input_file: &Path,
        input_root: &Path,
        output_root: &Path,
        new_extension: Option<&str>,
    ) -> CliResult<PathBuf> {
        let relative_path = input_file
            .strip_prefix(input_root)
            .map_err(|_| CliError::Config(format!(
                "Failed to calculate relative path for: {}",
                input_file.display()
            )))?;

        let mut output_path = output_root.join(relative_path);

        // Change extension if provided
        if let Some(ext) = new_extension {
            output_path.set_extension(ext);
        }

        Ok(output_path)
    }

    pub fn ensure_parent_dir(path: &Path) -> CliResult<()> {
        if let Some(parent) = path.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent)?;
            }
        }
        Ok(())
    }
}

#[derive(Debug, Default)]
pub struct DirectoryStats {
    pub total_files: usize,
    pub total_directories: usize,
    pub total_size: u64,
    pub file_types: std::collections::HashMap<String, usize>,
}

impl DirectoryStats {
    pub fn from_entries(entries: &[DirectoryEntry]) -> Self {
        let mut stats = Self::default();
        let mut file_types = std::collections::HashMap::new();

        for entry in entries {
            if entry.is_file {
                stats.total_files += 1;
                stats.total_size += entry.size;

                if let Some(ext) = entry.path.extension() {
                    let ext_str = ext.to_string_lossy().to_lowercase();
                    *file_types.entry(ext_str).or_insert(0) += 1;
                }
            } else {
                stats.total_directories += 1;
            }
        }

        stats.file_types = file_types;
        stats
    }

    pub fn print_summary(&self) {
        println!("\n📊 Directory Statistics:");
        println!("   Total files: {}", self.total_files);
        println!("   Total directories: {}", self.total_directories);
        println!("   Total size: {}", format_bytes(self.total_size));

        if !self.file_types.is_empty() {
            println!("\n   File types:");
            let mut types: Vec<_> = self.file_types.iter().collect();
            types.sort_by(|a, b| b.1.cmp(a.1));

            for (ext, count) in types.iter().take(10) {
                println!("     .{}: {}", ext, count);
            }

            if types.len() > 10 {
                println!("     ... and {} more types", types.len() - 10);
            }
        }
    }
}

fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_directory_traversal() {
        let temp_dir = TempDir::new().unwrap();
        let root = temp_dir.path();

        // Create test structure
        fs::create_dir_all(root.join("subdir")).unwrap();
        fs::write(root.join("file1.txt"), "content").unwrap();
        fs::write(root.join("subdir/file2.txt"), "content").unwrap();

        let traverser = DirectoryTraverser::new(DirectoryTraversalOptions::default());
        let entries = traverser.traverse(root).unwrap();

        assert!(entries.len() >= 3); // root, file1.txt, subdir, subdir/file2.txt
    }

    #[test]
    fn test_non_recursive_traversal() {
        let temp_dir = TempDir::new().unwrap();
        let root = temp_dir.path();

        fs::create_dir_all(root.join("subdir")).unwrap();
        fs::write(root.join("file1.txt"), "content").unwrap();
        fs::write(root.join("subdir/file2.txt"), "content").unwrap();

        let mut options = DirectoryTraversalOptions::default();
        options.recursive = false;

        let traverser = DirectoryTraverser::new(options);
        let files = traverser.traverse_files(root).unwrap();

        assert_eq!(files.len(), 1);
        assert!(files[0].ends_with("file1.txt"));
    }
}