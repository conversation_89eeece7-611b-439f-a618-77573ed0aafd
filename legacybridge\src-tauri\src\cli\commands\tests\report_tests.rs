use crate::cli::app::ReportArgs;
use crate::cli::commands::report::{
    execute_report_command, Report, ReportData, ReportSummary,
    ConversionReport, AnalysisReport, PerformanceReport,
    UsageReport, ErrorReport, FormatStats, OperationStats
};
use std::path::PathBuf;
use tempfile::TempDir;
use chrono::Utc;

#[tokio::test]
async fn test_conversion_report_generation() {
    let temp_dir = TempDir::new().unwrap();
    let output_path = temp_dir.path().join("conversion_report.html");
    
    let args = ReportArgs {
        report_type: "conversion".to_string(),
        input: None,
        output: Some(output_path.clone()),
        format: "html".to_string(),
        from: None,
        to: None,
        detailed: false,
        charts: false,
        group_by: None,
        status: None,
        system_info: false,
        template: None,
    };
    
    let result = execute_report_command(args).await;
    assert!(result.is_ok());
    assert!(output_path.exists());
}

#[tokio::test]
async fn test_analysis_report_generation() {
    let args = ReportArgs {
        report_type: "analysis".to_string(),
        input: None,
        output: None,
        format: "json".to_string(),
        from: None,
        to: None,
        detailed: true,
        charts: false,
        group_by: None,
        status: None,
        system_info: false,
        template: None,
    };
    
    let result = execute_report_command(args).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_performance_report_generation() {
    let args = ReportArgs {
        report_type: "performance".to_string(),
        input: None,
        output: None,
        format: "markdown".to_string(),
        from: None,
        to: None,
        detailed: false,
        charts: false,
        group_by: None,
        status: None,
        system_info: true,
        template: None,
    };
    
    let result = execute_report_command(args).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_usage_report_generation() {
    let temp_dir = TempDir::new().unwrap();
    let output_path = temp_dir.path().join("usage_report.csv");
    
    let args = ReportArgs {
        report_type: "usage".to_string(),
        input: None,
        output: Some(output_path.clone()),
        format: "csv".to_string(),
        from: Some("2024-01-01".to_string()),
        to: Some("2024-12-31".to_string()),
        detailed: false,
        charts: false,
        group_by: None,
        status: None,
        system_info: false,
        template: None,
    };
    
    let result = execute_report_command(args).await;
    assert!(result.is_ok());
    assert!(output_path.exists());
}

#[tokio::test]
async fn test_error_report_generation() {
    let args = ReportArgs {
        report_type: "errors".to_string(),
        input: None,
        output: None,
        format: "json".to_string(),
        from: None,
        to: None,
        detailed: true,
        charts: false,
        group_by: Some("type".to_string()),
        status: Some("error".to_string()),
        system_info: false,
        template: None,
    };
    
    let result = execute_report_command(args).await;
    assert!(result.is_ok());
}

#[test]
fn test_report_date_parsing() {
    use crate::cli::commands::report::parse_report_period;
    
    let period = parse_report_period(
        &Some("2024-01-01".to_string()),
        &Some("2024-12-31".to_string())
    );
    
    assert!(period.is_ok());
    let period = period.unwrap();
    assert!(period.from.is_some());
    assert!(period.to.is_some());
}

#[test]
fn test_conversion_report_structure() {
    let report = Report {
        report_type: "conversion".to_string(),
        title: "Test Conversion Report".to_string(),
        generated_at: Utc::now(),
        period: crate::cli::commands::report::ReportPeriod {
            from: None,
            to: None,
        },
        data: ReportData::Conversion(ConversionReport {
            total_files: 100,
            successful_conversions: 95,
            failed_conversions: 5,
            formats_converted: {
                let mut map = std::collections::HashMap::new();
                map.insert("doc".to_string(), FormatStats {
                    count: 50,
                    success_rate: 0.96,
                    average_size: 1024 * 100,
                    total_size: 1024 * 100 * 50,
                });
                map
            },
            output_formats: {
                let mut map = std::collections::HashMap::new();
                map.insert("md".to_string(), 95);
                map
            },
            total_size_processed: 1024 * 1024 * 50,
            average_processing_time_ms: 150,
        }),
        summary: ReportSummary {
            highlights: vec!["95% success rate".to_string()],
            recommendations: vec!["Review failed conversions".to_string()],
            trends: vec!["Processing time improved".to_string()],
        },
    };
    
    assert_eq!(report.report_type, "conversion");
    if let ReportData::Conversion(data) = &report.data {
        assert_eq!(data.total_files, 100);
        assert_eq!(data.successful_conversions, 95);
    } else {
        panic!("Expected ConversionReport data");
    }
}

#[test]
fn test_report_format_rendering() {
    use crate::cli::commands::report::render_markdown_report;
    
    let report = Report {
        report_type: "test".to_string(),
        title: "Test Report".to_string(),
        generated_at: Utc::now(),
        period: crate::cli::commands::report::ReportPeriod {
            from: None,
            to: None,
        },
        data: ReportData::Usage(UsageReport {
            unique_users: 10,
            total_operations: 100,
            operations_by_command: std::collections::HashMap::new(),
            peak_usage_times: vec!["09:00-10:00".to_string()],
            most_used_formats: vec![("pdf".to_string(), 50)],
            storage_used_mb: 1024,
        }),
        summary: ReportSummary {
            highlights: vec!["Test highlight".to_string()],
            recommendations: vec![],
            trends: vec![],
        },
    };
    
    let result = render_markdown_report(&report, false, false);
    assert!(result.is_ok());
    
    let markdown = result.unwrap();
    assert!(markdown.contains("# Test Report"));
    assert!(markdown.contains("Test highlight"));
}

#[test]
fn test_performance_report_metrics() {
    let mut operations = std::collections::HashMap::new();
    operations.insert("convert".to_string(), OperationStats {
        count: 1000,
        average_time_ms: 250,
        min_time_ms: 50,
        max_time_ms: 2000,
    });
    
    let perf_report = PerformanceReport {
        operations_performed: 1000,
        average_response_time_ms: 250,
        percentile_95_ms: 450,
        percentile_99_ms: 1500,
        throughput_mb_per_sec: 10.5,
        cpu_usage_percent: 35.0,
        memory_usage_mb: 512,
        performance_by_operation: operations,
    };
    
    assert_eq!(perf_report.operations_performed, 1000);
    assert_eq!(perf_report.average_response_time_ms, 250);
    assert!((perf_report.throughput_mb_per_sec - 10.5).abs() < 0.01);
}

#[test]
fn test_csv_report_export() {
    use crate::cli::commands::report::render_csv_report;
    
    let report = Report {
        report_type: "conversion".to_string(),
        title: "CSV Export Test".to_string(),
        generated_at: Utc::now(),
        period: crate::cli::commands::report::ReportPeriod {
            from: None,
            to: None,
        },
        data: ReportData::Conversion(ConversionReport {
            total_files: 10,
            successful_conversions: 9,
            failed_conversions: 1,
            formats_converted: std::collections::HashMap::new(),
            output_formats: std::collections::HashMap::new(),
            total_size_processed: 1024 * 1024,
            average_processing_time_ms: 100,
        }),
        summary: ReportSummary {
            highlights: vec![],
            recommendations: vec![],
            trends: vec![],
        },
    };
    
    let result = render_csv_report(&report);
    assert!(result.is_ok());
    
    let csv = result.unwrap();
    assert!(csv.contains("Metric,Value"));
    assert!(csv.contains("Total Files,10"));
    assert!(csv.contains("Successful Conversions,9"));
}