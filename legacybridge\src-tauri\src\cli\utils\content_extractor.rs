use crate::conversion::{ConversionEngine, ConversionOptions};
use crate::formats::{FormatDetector, FileFormat};
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ExtractedContent {
    pub text: String,
    pub metadata: DocumentMetadata,
    pub format: FileFormat,
    pub extraction_time: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct DocumentMetadata {
    pub title: Option<String>,
    pub author: Option<String>,
    pub creation_date: Option<DateTime<Utc>>,
    pub modification_date: Option<DateTime<Utc>>,
    pub word_count: usize,
    pub character_count: usize,
    pub line_count: usize,
    pub page_count: Option<usize>,
    pub language: Option<String>,
    pub keywords: Vec<String>,
    pub custom: HashMap<String, serde_json::Value>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ExtractionOptions {
    pub include_formatting: bool,
    pub extract_metadata: bool,
    pub max_length: Option<usize>,
    pub preserve_structure: bool,
}

impl Default for ExtractionOptions {
    fn default() -> Self {
        Self {
            include_formatting: false,
            extract_metadata: true,
            max_length: None,
            preserve_structure: true,
        }
    }
}

pub struct ContentExtractor {
    engine: ConversionEngine,
    detector: FormatDetector,
}

impl ContentExtractor {
    pub fn new() -> Self {
        Self {
            engine: ConversionEngine::new(),
            detector: FormatDetector::new(),
        }
    }
    
    /// Extract content from a file
    pub async fn extract_from_file(
        &self,
        path: &Path,
        options: &ExtractionOptions,
    ) -> Result<ExtractedContent, Box<dyn std::error::Error>> {
        let data = fs::read(path).await?;
        let format = self.detector.detect_from_bytes(&data)?;
        
        self.extract_from_bytes(&data, format, options).await
    }
    
    /// Extract content from raw bytes
    pub async fn extract_from_bytes(
        &self,
        data: &[u8],
        format: FileFormat,
        options: &ExtractionOptions,
    ) -> Result<ExtractedContent, Box<dyn std::error::Error>> {
        let extraction_time = Utc::now();
        
        // Convert to markdown for text extraction
        let conversion_options = ConversionOptions {
            preserve_formatting: options.include_formatting,
            extract_metadata: options.extract_metadata,
            ..Default::default()
        };
        
        let markdown_bytes = self.engine.convert(
            data,
            &format.extension,
            "md",
            conversion_options
        ).await?;
        
        let mut text = String::from_utf8_lossy(&markdown_bytes).to_string();
        
        // Apply max length if specified
        if let Some(max_len) = options.max_length {
            text.truncate(max_len);
        }
        
        // Extract metadata
        let metadata = if options.extract_metadata {
            self.extract_metadata(&text, &format, data).await?
        } else {
            DocumentMetadata::default()
        };
        
        Ok(ExtractedContent {
            text,
            metadata,
            format,
            extraction_time,
        })
    }
    
    /// Extract metadata from document
    async fn extract_metadata(
        &self,
        text: &str,
        format: &FileFormat,
        _raw_data: &[u8],
    ) -> Result<DocumentMetadata, Box<dyn std::error::Error>> {
        let lines: Vec<&str> = text.lines().collect();
        let word_count = text.split_whitespace().count();
        let character_count = text.chars().count();
        let line_count = lines.len();
        
        // Try to extract title from first heading or line
        let title = lines.first()
            .and_then(|line| {
                if line.starts_with("# ") {
                    Some(line.trim_start_matches("# ").to_string())
                } else if !line.is_empty() {
                    Some(line.to_string())
                } else {
                    None
                }
            });
        
        // Extract keywords from headings
        let keywords: Vec<String> = lines.iter()
            .filter(|line| line.starts_with('#'))
            .map(|line| line.trim_start_matches('#').trim().to_string())
            .filter(|s| !s.is_empty())
            .collect();
        
        // Page count estimation based on format
        let page_count = match format.extension.as_str() {
            "doc" | "wpd" | "rtf" => Some((word_count / 250).max(1)), // ~250 words per page
            "wk1" | "wks" | "dbf" => Some((line_count / 50).max(1)), // ~50 rows per page
            _ => None,
        };
        
        Ok(DocumentMetadata {
            title,
            author: None, // TODO: Extract from format-specific metadata
            creation_date: None,
            modification_date: None,
            word_count,
            character_count,
            line_count,
            page_count,
            language: None, // TODO: Detect language
            keywords,
            custom: HashMap::new(),
        })
    }
    
    /// Extract plain text without formatting
    pub async fn extract_plain_text(
        &self,
        path: &Path,
    ) -> Result<String, Box<dyn std::error::Error>> {
        let options = ExtractionOptions {
            include_formatting: false,
            extract_metadata: false,
            ..Default::default()
        };
        
        let content = self.extract_from_file(path, &options).await?;
        Ok(content.text)
    }
    
    /// Extract metadata only
    pub async fn extract_metadata_only(
        &self,
        path: &Path,
    ) -> Result<DocumentMetadata, Box<dyn std::error::Error>> {
        let options = ExtractionOptions {
            extract_metadata: true,
            ..Default::default()
        };
        
        let content = self.extract_from_file(path, &options).await?;
        Ok(content.metadata)
    }
}

impl Default for ContentExtractor {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::io::Write;
    
    #[tokio::test]
    async fn test_extract_text_from_rtf() {
        let extractor = ContentExtractor::new();
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test.rtf");
        
        let rtf_content = r"{\rtf1\ansi\deff0 {\fonttbl{\f0 Times New Roman;}} \f0\fs24 Hello World\par}";
        let mut file = std::fs::File::create(&file_path).unwrap();
        file.write_all(rtf_content.as_bytes()).unwrap();
        
        let options = ExtractionOptions::default();
        let result = extractor.extract_from_file(&file_path, &options).await;
        
        assert!(result.is_ok());
        let content = result.unwrap();
        assert!(content.text.contains("Hello World"));
        assert_eq!(content.format.extension, "rtf");
    }
    
    #[tokio::test]
    async fn test_metadata_extraction() {
        let extractor = ContentExtractor::new();
        let text = "# Document Title\n\nThis is a test document with some content.\n\n## Section 1\n\nMore content here.";
        let format = FileFormat {
            name: "Markdown".to_string(),
            extension: "md".to_string(),
            mime_type: "text/markdown".to_string(),
            category: crate::formats::FormatCategory::Document,
            confidence: 1.0,
        };
        
        let metadata = extractor.extract_metadata(text, &format, b"").await.unwrap();
        
        assert_eq!(metadata.title, Some("Document Title".to_string()));
        assert_eq!(metadata.word_count, 12);
        assert_eq!(metadata.line_count, 5);
        assert!(metadata.keywords.contains(&"Document Title".to_string()));
        assert!(metadata.keywords.contains(&"Section 1".to_string()));
    }
    
    #[test]
    fn test_extraction_options_default() {
        let options = ExtractionOptions::default();
        assert!(!options.include_formatting);
        assert!(options.extract_metadata);
        assert!(options.max_length.is_none());
        assert!(options.preserve_structure);
    }
}