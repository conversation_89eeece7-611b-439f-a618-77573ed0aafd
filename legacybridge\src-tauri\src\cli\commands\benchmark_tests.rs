use super::*;
use crate::cli::app::{Cli, BenchmarkArgs, BenchmarkType as CLIBenchmarkType};
use tempfile::TempDir;
use std::path::PathBuf;

#[cfg(test)]
mod benchmark_command_tests {
    use super::*;

    fn create_benchmark_args() -> (BenchmarkArgs, Cli) {
        let args = BenchmarkArgs {
            benchmarks: vec![CLIBenchmarkType::Conversion],
            input: None,
            iterations: 5,
            warmup: 2,
            output: None,
            baseline: None,
            memory: false,
            cpu: false,
            parallel: false,
            size_scaling: false,
            list: false,
            data_dir: None,
            generate_data: false,
            save_baseline: false,
        };
        
        let global_args = Cli {
            command: crate::cli::app::Commands::Benchmark(args.clone()),
            verbose: 0,
            format: crate::cli::app::OutputFormat::Table,
            config: None,
            quiet: false,
            color: None,
            workdir: None,
        };
        
        (args, global_args)
    }

    #[tokio::test]
    async fn test_list_benchmarks() {
        let (mut args, global_args) = create_benchmark_args();
        args.list = true;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_generate_benchmark_data() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.generate_data = true;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
        
        // Verify test data was generated
        assert!(temp_dir.path().join("samples").exists());
    }

    #[tokio::test]
    async fn test_run_conversion_benchmark() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.benchmarks = vec![CLIBenchmarkType::Conversion];
        args.generate_data = true;
        args.iterations = 2;
        args.warmup = 1;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_run_all_benchmarks() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.benchmarks = vec![CLIBenchmarkType::All];
        args.generate_data = true;
        args.iterations = 1;
        args.warmup = 1;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_memory_benchmark() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.benchmarks = vec![CLIBenchmarkType::Memory];
        args.generate_data = true;
        args.memory = true;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_throughput_benchmark() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.benchmarks = vec![CLIBenchmarkType::Throughput];
        args.generate_data = true;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_latency_benchmark() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.benchmarks = vec![CLIBenchmarkType::Latency];
        args.generate_data = true;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_size_scaling() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.benchmarks = vec![CLIBenchmarkType::Conversion];
        args.size_scaling = true;
        args.generate_data = true;
        args.iterations = 1;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_save_baseline() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.save_baseline = true;
        args.benchmarks = vec![CLIBenchmarkType::Conversion];
        args.generate_data = true;
        args.iterations = 2;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
        
        // Verify baseline was saved
        assert!(temp_dir.path().join("baselines").exists());
    }

    #[tokio::test]
    async fn test_compare_with_baseline() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.benchmarks = vec![CLIBenchmarkType::Conversion];
        args.generate_data = true;
        args.iterations = 2;
        
        // First run to establish baseline
        args.save_baseline = true;
        let _ = handle_benchmark_command(args.clone(), &global_args).await;
        
        // Second run to compare with baseline
        args.save_baseline = false;
        args.baseline = Some(temp_dir.path().to_path_buf());
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_export_results() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.output = Some(temp_dir.path().join("results.json"));
        args.benchmarks = vec![CLIBenchmarkType::Conversion];
        args.generate_data = true;
        args.iterations = 2;
        
        let result = handle_benchmark_command(args.clone(), &global_args).await;
        assert!(result.is_ok());
        
        // Verify export file was created
        if let Some(output_path) = args.output {
            assert!(output_path.exists());
        }
    }

    #[tokio::test]
    async fn test_multiple_benchmark_types() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.benchmarks = vec![
            CLIBenchmarkType::Conversion,
            CLIBenchmarkType::Memory,
            CLIBenchmarkType::Throughput,
        ];
        args.generate_data = true;
        args.iterations = 1;
        args.warmup = 1;
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_create_conversion_benchmark() {
        let (args, _) = create_benchmark_args();
        let config = create_conversion_benchmark(&args);
        
        assert_eq!(config.name, "conversion_speed");
        assert!(matches!(config.benchmark_type, BenchmarkType::ConversionSpeed { .. }));
        assert_eq!(config.warmup_iterations, 2);
        assert_eq!(config.measurement_iterations, 5);
    }

    #[test]
    fn test_determine_export_format() {
        assert_eq!(determine_export_format(&PathBuf::from("results.json")), "json");
        assert_eq!(determine_export_format(&PathBuf::from("results.csv")), "csv");
        assert_eq!(determine_export_format(&PathBuf::from("results.html")), "html");
        assert_eq!(determine_export_format(&PathBuf::from("results.txt")), "json"); // default
    }

    #[tokio::test]
    async fn test_verbose_output() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, mut global_args) = create_benchmark_args();
        args.data_dir = Some(temp_dir.path().to_path_buf());
        args.benchmarks = vec![CLIBenchmarkType::Conversion];
        args.generate_data = true;
        args.iterations = 1;
        global_args.verbose = 2; // Enable verbose output
        
        let result = handle_benchmark_command(args, &global_args).await;
        assert!(result.is_ok());
    }
}