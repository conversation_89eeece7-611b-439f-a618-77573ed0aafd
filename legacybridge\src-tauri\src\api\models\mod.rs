use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Conversion request model
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ConvertRequest {
    /// Base64 encoded file content or file path
    pub input: String,
    /// Input format (e.g., "doc", "wpd", "dbf")
    pub input_format: Option<String>,
    /// Desired output format (e.g., "md", "pdf", "html")
    pub output_format: String,
    /// Conversion options
    #[serde(default)]
    pub options: ConversionOptions,
}

/// Conversion options
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct ConversionOptions {
    /// Preserve formatting
    #[serde(default = "default_true")]
    pub preserve_formatting: bool,
    /// Extract metadata
    #[serde(default = "default_true")]
    pub extract_metadata: bool,
    /// Quality setting (0-100)
    #[serde(default = "default_quality")]
    pub quality: u8,
    /// Custom options
    #[serde(default)]
    pub custom: HashMap<String, serde_json::Value>,
}

/// Conversion response model
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ConvertResponse {
    /// Conversion success status
    pub success: bool,
    /// Base64 encoded output content
    pub output: Option<String>,
    /// Output format
    pub output_format: String,
    /// File metadata
    pub metadata: Option<FileMetadata>,
    /// Error message if conversion failed
    pub error: Option<String>,
    /// Conversion statistics
    pub stats: ConversionStats,
}

/// File metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileMetadata {
    pub title: Option<String>,
    pub author: Option<String>,
    pub created: Option<String>,
    pub modified: Option<String>,
    pub pages: Option<u32>,
    pub size: u64,
    pub custom: HashMap<String, String>,
}

/// Conversion statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionStats {
    pub processing_time_ms: u64,
    pub input_size: u64,
    pub output_size: u64,
    pub compression_ratio: f32,
}

/// Format detection request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectRequest {
    /// Base64 encoded file content or file path
    pub input: String,
    /// Include detailed analysis
    #[serde(default)]
    pub detailed: bool,
}

/// Format detection response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectResponse {
    /// Detected format
    pub format: String,
    /// Confidence score (0.0 - 1.0)
    pub confidence: f32,
    /// Format version if detected
    pub version: Option<String>,
    /// Alternative format suggestions
    pub alternatives: Vec<FormatSuggestion>,
    /// Detailed analysis if requested
    pub analysis: Option<FormatAnalysis>,
}

/// Format suggestion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatSuggestion {
    pub format: String,
    pub confidence: f32,
    pub reason: String,
}

/// Detailed format analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatAnalysis {
    pub magic_bytes: String,
    pub header_hex: String,
    pub readable_percentage: f32,
    pub structure_hints: Vec<String>,
    pub encoding: Option<String>,
}

/// Supported formats response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatsResponse {
    pub input_formats: Vec<FormatInfo>,
    pub output_formats: Vec<FormatInfo>,
}

/// Format information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatInfo {
    pub id: String,
    pub name: String,
    pub extensions: Vec<String>,
    pub mime_types: Vec<String>,
    pub description: String,
    pub capabilities: FormatCapabilities,
}

/// Format capabilities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatCapabilities {
    pub can_read: bool,
    pub can_write: bool,
    pub preserves_formatting: bool,
    pub supports_metadata: bool,
    pub supports_images: bool,
}

/// File upload request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadRequest {
    pub filename: String,
    pub content_type: String,
    pub size: u64,
}

/// File upload response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadResponse {
    pub upload_id: String,
    pub filename: String,
    pub size: u64,
    pub expires_at: u64,
}

/// Batch conversion request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchConvertRequest {
    /// Files to convert
    pub files: Vec<FileData>,
    /// Desired output format for all files
    pub output_format: String,
    /// Conversion options (applied to all files)
    pub options: Option<ConversionOptions>,
}

/// File data for batch processing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileData {
    /// File name
    pub name: String,
    /// Base64 encoded content
    pub content: String,
    /// Detected format (optional)
    pub detected_format: Option<String>,
}

/// Batch conversion response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchConvertResponse {
    /// Job ID for tracking
    pub job_id: String,
    /// Current job status
    pub status: JobStatus,
    /// Response message
    pub message: String,
    /// Total number of files
    pub total_files: usize,
    /// WebSocket URL for real-time updates
    pub ws_url: Option<String>,
}

/// Job status enum
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum JobStatus {
    Pending,
    Processing,
    Completed,
    Failed,
    Cancelled,
}

/// Error response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub details: Option<String>,
}

/// API error response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiError {
    pub error: String,
    pub message: String,
    pub details: Option<HashMap<String, serde_json::Value>>,
    pub request_id: String,
}

impl ApiError {
    pub fn new(error: impl Into<String>, message: impl Into<String>) -> Self {
        Self {
            error: error.into(),
            message: message.into(),
            details: None,
            request_id: uuid::Uuid::new_v4().to_string(),
        }
    }
    
    pub fn with_details(mut self, details: HashMap<String, serde_json::Value>) -> Self {
        self.details = Some(details);
        self
    }
}

// Helper functions
fn default_true() -> bool {
    true
}

fn default_quality() -> u8 {
    85
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_convert_request_deserialization() {
        let json = r#"{
            "input": "base64content",
            "output_format": "md"
        }"#;
        
        let request: ConvertRequest = serde_json::from_str(json).unwrap();
        assert_eq!(request.output_format, "md");
        assert!(request.options.preserve_formatting);
        assert_eq!(request.options.quality, 85);
    }
    
    #[test]
    fn test_api_error_creation() {
        let error = ApiError::new("validation_error", "Invalid input format");
        assert_eq!(error.error, "validation_error");
        assert_eq!(error.message, "Invalid input format");
        assert!(error.details.is_none());
        assert!(!error.request_id.is_empty());
    }
}