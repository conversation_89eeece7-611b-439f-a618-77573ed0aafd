use axum::{
    body::Body,
    extract::Request,
    http::{header, StatusCode},
    response::{IntoResponse, Response},
};
use std::collections::HashMap;
use std::future::Future;
use std::net::IpAddr;
use std::pin::Pin;
use std::sync::{Arc, Mutex};
use std::task::{Context, Poll};
use std::time::{Duration, Instant};
use tower::{Layer, Service};
use tracing::warn;

/// Rate limiting layer
#[derive(Clone)]
pub struct RateLimitLayer {
    rate_limit: u32, // requests per minute
    store: Arc<Mutex<RateLimitStore>>,
}

impl RateLimitLayer {
    pub fn new(rate_limit: u32) -> Self {
        Self {
            rate_limit,
            store: Arc::new(Mutex::new(RateLimitStore::new())),
        }
    }
}

impl<S> Layer<S> for RateLimitLayer {
    type Service = RateLimitMiddleware<S>;

    fn layer(&self, inner: S) -> Self::Service {
        RateLimitMiddleware {
            inner,
            rate_limit: self.rate_limit,
            store: self.store.clone(),
        }
    }
}

/// Rate limit middleware service
#[derive(Clone)]
pub struct RateLimitMiddleware<S> {
    inner: S,
    rate_limit: u32,
    store: Arc<Mutex<RateLimitStore>>,
}

/// Simple in-memory rate limit store
struct RateLimitStore {
    requests: HashMap<IpAddr, Vec<Instant>>,
}

impl RateLimitStore {
    fn new() -> Self {
        Self {
            requests: HashMap::new(),
        }
    }
    
    fn check_and_update(&mut self, ip: IpAddr, limit: u32) -> bool {
        let now = Instant::now();
        let window = Duration::from_secs(60); // 1 minute window
        
        // Get or create request history for this IP
        let requests = self.requests.entry(ip).or_insert_with(Vec::new);
        
        // Remove old requests outside the window
        requests.retain(|&req_time| now.duration_since(req_time) < window);
        
        // Check if under limit
        if requests.len() < limit as usize {
            requests.push(now);
            true
        } else {
            false
        }
    }
}

impl<S> Service<Request> for RateLimitMiddleware<S>
where
    S: Service<Request, Response = Response> + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>> + Send>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request) -> Self::Future {
        let rate_limit = self.rate_limit;
        let store = self.store.clone();
        
        // Extract information from request before moving it
        let client_ip = extract_client_ip(&request);
        let path = request.uri().path().to_string();
        
        let future = self.inner.call(request);
        
        Box::pin(async move {
            // Skip rate limiting for health and info endpoints
            if path == "/health" || path == "/info" || path == "/docs" || path == "/metrics" {
                return future.await;
            }
            
            // Check rate limit
            let allowed = match client_ip {
                Some(ip) => {
                    let mut store = store.lock().unwrap();
                    store.check_and_update(ip, rate_limit)
                }
                None => {
                    // If we can't determine IP, allow the request but log warning
                    warn!("Could not determine client IP for rate limiting");
                    true
                }
            };
            
            if allowed {
                future.await
            } else {
                Ok(rate_limit_exceeded_response())
            }
        })
    }
}

/// Extract client IP from request
fn extract_client_ip(request: &Request) -> Option<IpAddr> {
    // Try X-Forwarded-For header first (for proxied requests)
    if let Some(forwarded) = request.headers().get("X-Forwarded-For") {
        if let Ok(forwarded_str) = forwarded.to_str() {
            // Take the first IP in the chain
            if let Some(ip_str) = forwarded_str.split(',').next() {
                if let Ok(ip) = ip_str.trim().parse::<IpAddr>() {
                    return Some(ip);
                }
            }
        }
    }
    
    // Try X-Real-IP header
    if let Some(real_ip) = request.headers().get("X-Real-IP") {
        if let Ok(ip_str) = real_ip.to_str() {
            if let Ok(ip) = ip_str.parse::<IpAddr>() {
                return Some(ip);
            }
        }
    }
    
    // In a real deployment, we would get the IP from the connection info
    // For now, return a default for testing
    Some("127.0.0.1".parse().unwrap())
}

/// Create a rate limit exceeded response
fn rate_limit_exceeded_response() -> Response {
    let body = serde_json::json!({
        "error": "rate_limit_exceeded",
        "message": "Too many requests. Please try again later.",
        "request_id": uuid::Uuid::new_v4().to_string(),
    });
    
    (
        StatusCode::TOO_MANY_REQUESTS,
        [
            (header::CONTENT_TYPE, "application/json"),
            (header::RETRY_AFTER, "60"),
        ],
        body.to_string(),
    ).into_response()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_rate_limit_store() {
        let mut store = RateLimitStore::new();
        let ip: IpAddr = "127.0.0.1".parse().unwrap();
        
        // First 10 requests should be allowed (assuming limit of 10)
        for _ in 0..10 {
            assert!(store.check_and_update(ip, 10));
        }
        
        // 11th request should be denied
        assert!(!store.check_and_update(ip, 10));
    }
    
    #[test]
    fn test_extract_client_ip() {
        let request = Request::builder()
            .header("X-Forwarded-For", "***********, ********")
            .body(Body::empty())
            .unwrap();
        
        assert_eq!(
            extract_client_ip(&request),
            Some("***********".parse().unwrap())
        );
        
        let request = Request::builder()
            .header("X-Real-IP", "***********")
            .body(Body::empty())
            .unwrap();
        
        assert_eq!(
            extract_client_ip(&request),
            Some("***********".parse().unwrap())
        );
    }
}