//! Enhanced rate limiting middleware with multiple strategies
//!
//! This module provides advanced rate limiting capabilities including:
//! - Multiple rate limiting strategies (fixed window, sliding window, token bucket)
//! - Per-endpoint rate limits
//! - User-based rate limiting (when authenticated)
//! - Distributed rate limiting support (Redis backend)

use axum::{
    body::Body,
    extract::Request,
    http::{header, StatusCode},
    response::{IntoResponse, Response},
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::future::Future;
use std::net::IpAddr;
use std::pin::Pin;
use std::sync::Arc;
use std::task::{Context, Poll};
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tower::{Layer, Service};
use tracing::{debug, warn};

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Default rate limit (requests per window)
    pub default_limit: u32,
    /// Window duration in seconds
    pub window_seconds: u64,
    /// Rate limiting strategy
    pub strategy: RateLimitStrategy,
    /// Per-endpoint limits (path -> limit)
    pub endpoint_limits: HashMap<String, u32>,
    /// Whitelisted IPs that bypass rate limiting
    pub whitelist: Vec<IpAddr>,
    /// Enable header information in responses
    pub include_headers: bool,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            default_limit: 60,
            window_seconds: 60,
            strategy: RateLimitStrategy::SlidingWindow,
            endpoint_limits: HashMap::new(),
            whitelist: vec![],
            include_headers: true,
        }
    }
}

/// Rate limiting strategy
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum RateLimitStrategy {
    /// Fixed window (resets at window boundaries)
    FixedWindow,
    /// Sliding window (continuous)
    SlidingWindow,
    /// Token bucket algorithm
    TokenBucket {
        capacity: u32,
        refill_rate: f64,
    },
}

/// Enhanced rate limiting layer
#[derive(Clone)]
pub struct EnhancedRateLimitLayer {
    config: RateLimitConfig,
    store: Arc<RwLock<RateLimitStore>>,
}

impl EnhancedRateLimitLayer {
    pub fn new(config: RateLimitConfig) -> Self {
        // Start cleanup task
        let store = Arc::new(RwLock::new(RateLimitStore::new()));
        let store_clone = store.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(300)); // 5 minutes
            loop {
                interval.tick().await;
                store_clone.write().await.cleanup();
            }
        });
        
        Self { config, store }
    }
}

impl<S> Layer<S> for EnhancedRateLimitLayer {
    type Service = EnhancedRateLimitMiddleware<S>;

    fn layer(&self, inner: S) -> Self::Service {
        EnhancedRateLimitMiddleware {
            inner,
            config: self.config.clone(),
            store: self.store.clone(),
        }
    }
}

/// Enhanced rate limit middleware service
#[derive(Clone)]
pub struct EnhancedRateLimitMiddleware<S> {
    inner: S,
    config: RateLimitConfig,
    store: Arc<RwLock<RateLimitStore>>,
}

/// Rate limit store with multiple strategies
struct RateLimitStore {
    /// Fixed/sliding window data
    windows: HashMap<String, WindowData>,
    /// Token bucket data
    buckets: HashMap<String, TokenBucket>,
}

#[derive(Debug)]
struct WindowData {
    requests: Vec<Instant>,
    window_start: Instant,
}

#[derive(Debug)]
struct TokenBucket {
    tokens: f64,
    last_refill: Instant,
}

impl RateLimitStore {
    fn new() -> Self {
        Self {
            windows: HashMap::new(),
            buckets: HashMap::new(),
        }
    }
    
    /// Check rate limit using configured strategy
    async fn check_rate_limit(
        &mut self,
        key: &str,
        limit: u32,
        window_duration: Duration,
        strategy: &RateLimitStrategy,
    ) -> RateLimitResult {
        match strategy {
            RateLimitStrategy::FixedWindow => {
                self.check_fixed_window(key, limit, window_duration)
            }
            RateLimitStrategy::SlidingWindow => {
                self.check_sliding_window(key, limit, window_duration)
            }
            RateLimitStrategy::TokenBucket { capacity, refill_rate } => {
                self.check_token_bucket(key, *capacity, *refill_rate)
            }
        }
    }
    
    fn check_fixed_window(
        &mut self,
        key: &str,
        limit: u32,
        window_duration: Duration,
    ) -> RateLimitResult {
        let now = Instant::now();
        let window_data = self.windows.entry(key.to_string()).or_insert_with(|| {
            WindowData {
                requests: Vec::new(),
                window_start: now,
            }
        });
        
        // Check if we need to reset the window
        if now.duration_since(window_data.window_start) >= window_duration {
            window_data.requests.clear();
            window_data.window_start = now;
        }
        
        let remaining = limit.saturating_sub(window_data.requests.len() as u32);
        let reset_at = window_data.window_start + window_duration;
        
        if window_data.requests.len() < limit as usize {
            window_data.requests.push(now);
            RateLimitResult {
                allowed: true,
                limit,
                remaining: remaining.saturating_sub(1),
                reset_at: reset_at.duration_since(now).as_secs(),
            }
        } else {
            RateLimitResult {
                allowed: false,
                limit,
                remaining: 0,
                reset_at: reset_at.duration_since(now).as_secs(),
            }
        }
    }
    
    fn check_sliding_window(
        &mut self,
        key: &str,
        limit: u32,
        window_duration: Duration,
    ) -> RateLimitResult {
        let now = Instant::now();
        let window_data = self.windows.entry(key.to_string()).or_insert_with(|| {
            WindowData {
                requests: Vec::new(),
                window_start: now - window_duration,
            }
        });
        
        // Remove old requests outside the sliding window
        window_data.requests.retain(|&req_time| {
            now.duration_since(req_time) < window_duration
        });
        
        let remaining = limit.saturating_sub(window_data.requests.len() as u32);
        
        if window_data.requests.len() < limit as usize {
            window_data.requests.push(now);
            RateLimitResult {
                allowed: true,
                limit,
                remaining: remaining.saturating_sub(1),
                reset_at: window_duration.as_secs(),
            }
        } else {
            // Find when the oldest request will expire
            let oldest = window_data.requests.first().copied().unwrap_or(now);
            let reset_at = (oldest + window_duration).duration_since(now).as_secs();
            
            RateLimitResult {
                allowed: false,
                limit,
                remaining: 0,
                reset_at,
            }
        }
    }
    
    fn check_token_bucket(
        &mut self,
        key: &str,
        capacity: u32,
        refill_rate: f64,
    ) -> RateLimitResult {
        let now = Instant::now();
        let bucket = self.buckets.entry(key.to_string()).or_insert_with(|| {
            TokenBucket {
                tokens: capacity as f64,
                last_refill: now,
            }
        });
        
        // Refill tokens based on elapsed time
        let elapsed = now.duration_since(bucket.last_refill).as_secs_f64();
        let tokens_to_add = elapsed * refill_rate;
        bucket.tokens = (bucket.tokens + tokens_to_add).min(capacity as f64);
        bucket.last_refill = now;
        
        if bucket.tokens >= 1.0 {
            bucket.tokens -= 1.0;
            RateLimitResult {
                allowed: true,
                limit: capacity,
                remaining: bucket.tokens as u32,
                reset_at: (1.0 / refill_rate) as u64,
            }
        } else {
            // Calculate when we'll have 1 token
            let tokens_needed = 1.0 - bucket.tokens;
            let reset_at = (tokens_needed / refill_rate) as u64;
            
            RateLimitResult {
                allowed: false,
                limit: capacity,
                remaining: 0,
                reset_at,
            }
        }
    }
    
    /// Clean up old entries
    fn cleanup(&mut self) {
        let now = Instant::now();
        let stale_duration = Duration::from_secs(3600); // 1 hour
        
        // Clean up window data
        self.windows.retain(|_, data| {
            !data.requests.is_empty() || now.duration_since(data.window_start) < stale_duration
        });
        
        // Clean up token buckets
        self.buckets.retain(|_, bucket| {
            now.duration_since(bucket.last_refill) < stale_duration
        });
    }
}

#[derive(Debug)]
struct RateLimitResult {
    allowed: bool,
    limit: u32,
    remaining: u32,
    reset_at: u64,
}

impl<S> Service<Request> for EnhancedRateLimitMiddleware<S>
where
    S: Service<Request, Response = Response> + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>> + Send>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request) -> Self::Future {
        let config = self.config.clone();
        let store = self.store.clone();
        
        // Extract information from request
        let client_ip = extract_client_ip(&request);
        let path = request.uri().path().to_string();
        let api_key = extract_api_key(&request);
        
        let future = self.inner.call(request);
        
        Box::pin(async move {
            // Skip rate limiting for certain endpoints
            if is_exempt_endpoint(&path) {
                return future.await;
            }
            
            // Check whitelist
            if let Some(ip) = client_ip {
                if config.whitelist.contains(&ip) {
                    debug!("IP {} is whitelisted, bypassing rate limit", ip);
                    return future.await;
                }
            }
            
            // Determine rate limit key and limit
            let key = determine_rate_limit_key(client_ip, api_key.as_deref());
            let limit = config.endpoint_limits
                .get(&path)
                .copied()
                .unwrap_or(config.default_limit);
            
            // Check rate limit
            let mut store = store.write().await;
            let result = store.check_rate_limit(
                &key,
                limit,
                Duration::from_secs(config.window_seconds),
                &config.strategy,
            ).await;
            
            if result.allowed {
                drop(store); // Release lock before awaiting
                let mut response = future.await?;
                
                // Add rate limit headers if configured
                if config.include_headers {
                    add_rate_limit_headers(&mut response, &result);
                }
                
                Ok(response)
            } else {
                Ok(rate_limit_exceeded_response(result, config.include_headers))
            }
        })
    }
}

/// Extract client IP from request
fn extract_client_ip(request: &Request) -> Option<IpAddr> {
    // Try standard headers in order of preference
    for header in &["X-Forwarded-For", "X-Real-IP", "CF-Connecting-IP"] {
        if let Some(value) = request.headers().get(*header) {
            if let Ok(value_str) = value.to_str() {
                // For X-Forwarded-For, take the first IP
                if let Some(ip_str) = value_str.split(',').next() {
                    if let Ok(ip) = ip_str.trim().parse::<IpAddr>() {
                        return Some(ip);
                    }
                }
            }
        }
    }
    
    // Fallback for testing
    Some("127.0.0.1".parse().unwrap())
}

/// Extract API key from request
fn extract_api_key(request: &Request) -> Option<String> {
    request.headers()
        .get("X-API-Key")
        .and_then(|value| value.to_str().ok())
        .map(|s| s.to_string())
}

/// Determine rate limit key based on available identifiers
fn determine_rate_limit_key(ip: Option<IpAddr>, api_key: Option<&str>) -> String {
    match (api_key, ip) {
        (Some(key), _) => format!("key:{}", key),
        (None, Some(ip)) => format!("ip:{}", ip),
        (None, None) => "anonymous".to_string(),
    }
}

/// Check if endpoint is exempt from rate limiting
fn is_exempt_endpoint(path: &str) -> bool {
    matches!(
        path,
        "/health" | "/info" | "/docs" | "/docs/openapi.json" | "/docs/redoc" | "/metrics"
    )
}

/// Add rate limit headers to response
fn add_rate_limit_headers(response: &mut Response, result: &RateLimitResult) {
    let headers = response.headers_mut();
    headers.insert("X-RateLimit-Limit", result.limit.to_string().parse().unwrap());
    headers.insert("X-RateLimit-Remaining", result.remaining.to_string().parse().unwrap());
    headers.insert("X-RateLimit-Reset", result.reset_at.to_string().parse().unwrap());
}

/// Create a rate limit exceeded response
fn rate_limit_exceeded_response(result: RateLimitResult, include_headers: bool) -> Response {
    let body = serde_json::json!({
        "error": "rate_limit_exceeded",
        "message": "Too many requests. Please try again later.",
        "retry_after": result.reset_at,
        "request_id": uuid::Uuid::new_v4().to_string(),
    });
    
    let mut response = (
        StatusCode::TOO_MANY_REQUESTS,
        [
            (header::CONTENT_TYPE, "application/json"),
            (header::RETRY_AFTER, &result.reset_at.to_string()),
        ],
        body.to_string(),
    ).into_response();
    
    if include_headers {
        add_rate_limit_headers(&mut response, &result);
    }
    
    response
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_sliding_window() {
        let mut store = RateLimitStore::new();
        let key = "test";
        let limit = 3;
        let window = Duration::from_secs(1);
        
        // First 3 requests should pass
        for i in 0..3 {
            let result = store.check_sliding_window(key, limit, window);
            assert!(result.allowed, "Request {} should be allowed", i + 1);
            assert_eq!(result.remaining, limit - i - 1);
        }
        
        // 4th request should be denied
        let result = store.check_sliding_window(key, limit, window);
        assert!(!result.allowed);
        assert_eq!(result.remaining, 0);
        
        // Wait for window to slide
        tokio::time::sleep(window).await;
        
        // Should be allowed again
        let result = store.check_sliding_window(key, limit, window);
        assert!(result.allowed);
    }
    
    #[test]
    fn test_token_bucket() {
        let mut store = RateLimitStore::new();
        let key = "test";
        let capacity = 5;
        let refill_rate = 1.0; // 1 token per second
        
        // Use all tokens
        for _ in 0..capacity {
            let result = store.check_token_bucket(key, capacity, refill_rate);
            assert!(result.allowed);
        }
        
        // Next request should be denied
        let result = store.check_token_bucket(key, capacity, refill_rate);
        assert!(!result.allowed);
        assert_eq!(result.remaining, 0);
    }
}