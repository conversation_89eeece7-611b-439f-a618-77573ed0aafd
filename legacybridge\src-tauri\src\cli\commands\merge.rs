use clap::Args;
use std::path::PathBuf;
use crate::cli::output::{OutputFormat, OutputFormatter};
use crate::conversion::{ConversionEngine, ConversionOptions};
use crate::formats::FormatDetector;
use serde::{Serialize, Deserialize};
use indicatif::{ProgressBar, ProgressStyle};
use tokio::fs;
use std::io::Write;

#[derive(Args, Debug)]
pub struct MergeCommand {
    /// Input files to merge (in order)
    #[arg(required = true)]
    pub input: Vec<String>,
    
    /// Output file path
    #[arg(short, long, required = true)]
    pub output: PathBuf,
    
    /// Output format (default: same as first input file)
    #[arg(short = 'f', long)]
    pub format: Option<String>,
    
    /// Add page breaks between merged files
    #[arg(short = 'b', long)]
    pub page_breaks: bool,
    
    /// Add file headers showing original filename
    #[arg(short = 'H', long)]
    pub headers: bool,
    
    /// Preserve formatting from source files
    #[arg(short = 'p', long)]
    pub preserve_formatting: bool,
    
    /// Force overwrite if output exists
    #[arg(long)]
    pub force: bool,
    
    /// Output format for results
    #[arg(long, value_enum, default_value = "table")]
    pub output_format: OutputFormat,
    
    /// Verbose output
    #[arg(short, long)]
    pub verbose: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MergeResult {
    pub file: String,
    pub format: String,
    pub size: u64,
    pub status: String,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MergeSummary {
    pub total_files: usize,
    pub merged: usize,
    pub failed: usize,
    pub output_file: String,
    pub output_size: u64,
    pub output_format: String,
    pub files: Vec<MergeResult>,
}

pub async fn execute(cmd: MergeCommand) -> Result<(), Box<dyn std::error::Error>> {
    // Expand input patterns
    let files = crate::cli::utils::expand_patterns(&cmd.input)?;
    
    if files.is_empty() {
        return Err("No files found matching the input patterns".into());
    }
    
    if files.len() < 2 && cmd.verbose {
        println!("Warning: Merging only one file");
    }
    
    // Check if output exists
    if cmd.output.exists() && !cmd.force {
        return Err(format!(
            "Output file '{}' already exists. Use --force to overwrite.",
            cmd.output.display()
        ).into());
    }
    
    // Create output directory if needed
    if let Some(parent) = cmd.output.parent() {
        fs::create_dir_all(parent).await?;
    }
    
    // Determine output format
    let detector = FormatDetector::new();
    let first_file_data = fs::read(&files[0]).await?;
    let first_format = detector.detect_from_bytes(&first_file_data)?;
    
    let output_format = cmd.format.as_deref()
        .unwrap_or(&first_format.extension);
    
    if cmd.verbose {
        println!("Merging {} files into {} format", files.len(), output_format);
    }
    
    // Process files
    let engine = ConversionEngine::new();
    let mut results = Vec::new();
    let mut merged_content = Vec::new();
    
    let pb = if !cmd.verbose {
        let pb = ProgressBar::new(files.len() as u64);
        pb.set_style(
            ProgressStyle::default_bar()
                .template("[{elapsed_precise}] {bar:40.cyan/blue} {pos}/{len} {msg}")?
                .progress_chars("=>-")
        );
        Some(pb)
    } else {
        None
    };
    
    for (idx, file) in files.iter().enumerate() {
        if let Some(ref pb) = pb {
            pb.set_message(format!("Processing {}", file.display()));
        } else if cmd.verbose {
            println!("Processing: {}", file.display());
        }
        
        let result = process_file_for_merge(
            file,
            &engine,
            &detector,
            output_format,
            &cmd,
            idx,
            &mut merged_content
        ).await;
        
        results.push(result);
        
        if let Some(ref pb) = pb {
            pb.inc(1);
        }
    }
    
    if let Some(pb) = pb {
        pb.finish_with_message("Merge complete");
    }
    
    // Write merged content
    let output_size = if !merged_content.is_empty() {
        fs::write(&cmd.output, &merged_content).await?;
        merged_content.len() as u64
    } else {
        return Err("No content to merge".into());
    };
    
    // Generate summary
    let summary = MergeSummary {
        total_files: files.len(),
        merged: results.iter().filter(|r| r.status == "success").count(),
        failed: results.iter().filter(|r| r.status == "failed").count(),
        output_file: cmd.output.display().to_string(),
        output_size,
        output_format: output_format.to_string(),
        files: results,
    };
    
    // Output results
    let formatter = OutputFormatter::new(cmd.output_format);
    formatter.print_merge_summary(&summary)?;
    
    Ok(())
}

async fn process_file_for_merge(
    file: &PathBuf,
    engine: &ConversionEngine,
    detector: &FormatDetector,
    output_format: &str,
    cmd: &MergeCommand,
    index: usize,
    merged_content: &mut Vec<u8>,
) -> MergeResult {
    match process_single_file(file, engine, detector, output_format, cmd, index).await {
        Ok((content, format, size)) => {
            // Add to merged content
            if index > 0 && cmd.page_breaks {
                // Add page break based on output format
                match output_format {
                    "md" => merged_content.extend_from_slice(b"\n\n---\n\n"),
                    "rtf" => merged_content.extend_from_slice(br"\page "),
                    _ => merged_content.extend_from_slice(b"\n\n"),
                }
            }
            
            if cmd.headers {
                // Add file header
                let header = match output_format {
                    "md" => format!("\n## Source: {}\n\n", file.display()),
                    "rtf" => format!(r"{{\b Source: {}\par}}", file.display()),
                    _ => format!("\n--- Source: {} ---\n", file.display()),
                };
                merged_content.extend_from_slice(header.as_bytes());
            }
            
            merged_content.extend_from_slice(&content);
            
            MergeResult {
                file: file.display().to_string(),
                format,
                size,
                status: "success".to_string(),
                error: None,
            }
        }
        Err(e) => MergeResult {
            file: file.display().to_string(),
            format: "unknown".to_string(),
            size: 0,
            status: "failed".to_string(),
            error: Some(e.to_string()),
        }
    }
}

async fn process_single_file(
    file: &PathBuf,
    engine: &ConversionEngine,
    detector: &FormatDetector,
    output_format: &str,
    cmd: &MergeCommand,
    _index: usize,
) -> Result<(Vec<u8>, String, u64), Box<dyn std::error::Error>> {
    let data = fs::read(file).await?;
    let size = data.len() as u64;
    let format = detector.detect_from_bytes(&data)?;
    
    // Convert to output format if different
    let converted = if format.extension != output_format {
        let options = ConversionOptions {
            preserve_formatting: cmd.preserve_formatting,
            ..Default::default()
        };
        
        engine.convert(
            &data,
            &format.extension,
            output_format,
            options
        ).await?
    } else {
        data
    };
    
    Ok((converted, format.extension, size))
}

impl OutputFormatter {
    pub fn print_merge_summary(&self, summary: &MergeSummary) -> Result<(), Box<dyn std::error::Error>> {
        match self.format {
            OutputFormat::Json => {
                println!("{}", serde_json::to_string_pretty(summary)?);
            }
            OutputFormat::Table => {
                use prettytable::{Table, row, format};
                
                let mut table = Table::new();
                table.set_format(*format::consts::FORMAT_NO_BORDER_LINE_SEPARATOR);
                
                // Summary
                println!("\nMerge Summary:");
                println!("─────────────");
                println!("Total files:    {}", summary.total_files);
                println!("Merged:         {}", summary.merged);
                println!("Failed:         {}", summary.failed);
                println!("Output file:    {}", summary.output_file);
                println!("Output size:    {} bytes", summary.output_size);
                println!("Output format:  {}", summary.output_format);
                
                if !summary.files.is_empty() {
                    println!("\nFile Details:");
                    table.add_row(row!["File", "Format", "Size", "Status"]);
                    
                    for file in &summary.files {
                        let status = if file.status == "success" { "✓" } else { "✗" };
                        table.add_row(row![
                            file.file,
                            file.format,
                            format!("{} bytes", file.size),
                            status
                        ]);
                    }
                    
                    table.printstd();
                    
                    // Print errors if any
                    let errors: Vec<_> = summary.files.iter()
                        .filter(|f| f.error.is_some())
                        .collect();
                    
                    if !errors.is_empty() {
                        println!("\nErrors:");
                        for file in errors {
                            println!("  {}: {}", file.file, file.error.as_ref().unwrap());
                        }
                    }
                }
            }
            _ => {
                // CSV and Plain formats
                println!("File,Format,Size,Status");
                for file in &summary.files {
                    println!("{},{},{},{}",
                        file.file,
                        file.format,
                        file.size,
                        file.status
                    );
                }
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::io::Write;
    
    #[tokio::test]
    async fn test_merge_command() {
        let temp_dir = TempDir::new().unwrap();
        
        // Create test files
        let file1 = temp_dir.path().join("test1.rtf");
        let file2 = temp_dir.path().join("test2.rtf");
        let output = temp_dir.path().join("merged.rtf");
        
        let rtf1 = r"{\rtf1\ansi\deff0 First document\par}";
        let rtf2 = r"{\rtf1\ansi\deff0 Second document\par}";
        
        std::fs::File::create(&file1).unwrap().write_all(rtf1.as_bytes()).unwrap();
        std::fs::File::create(&file2).unwrap().write_all(rtf2.as_bytes()).unwrap();
        
        let cmd = MergeCommand {
            input: vec![file1.display().to_string(), file2.display().to_string()],
            output,
            format: Some("rtf".to_string()),
            page_breaks: false,
            headers: false,
            preserve_formatting: true,
            force: false,
            output_format: OutputFormat::Json,
            verbose: false,
        };
        
        let result = execute(cmd).await;
        assert!(result.is_ok());
    }
}