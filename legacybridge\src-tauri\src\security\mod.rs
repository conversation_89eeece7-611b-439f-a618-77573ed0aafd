// Security module - Enhanced input validation and security hardening

pub mod validator;
pub mod limits;
pub mod sanitizer;
pub mod audit;

pub use validator::{
    EnhancedInputValidator,
    SecurityLimits,
    ValidationResult,
    ValidationError,
    SecurityThreat,
    ThreatType,
    ThreatSeverity,
    ValidationContext,
    FormatValidator,
    FormatValidationResult,
};

pub use limits::{
    SecurityLimitsEnforcer,
    SecurityError,
    OperationPermit,
};

pub use sanitizer::{
    ContentSanitizer,
    SanitizerConfig,
    SanitizationResult,
    SanitizationError,
    SanitizationRule,
};

pub use audit::{
    SecurityAuditLogger,
    AuditConfig,
    AuditEvent,
    AuditEventType,
    AuditEventLevel,
    AuditEventOutcome,
    ThreatInfo,
    AuditReport,
    AuditError,
};

/// Initialize the security module with default settings
pub fn init_security() -> Result<(EnhancedInputValidator, SecurityLimitsEnforcer), SecurityError> {
    let limits = SecurityLimits::default();
    let validator = EnhancedInputValidator::new(limits.clone());
    let enforcer = SecurityLimitsEnforcer::new(limits);
    
    Ok((validator, enforcer))
}

/// Perform comprehensive security validation on input
pub async fn validate_and_check_limits(
    validator: &EnhancedInputValidator,
    enforcer: &SecurityLimitsEnforcer,
    content: &[u8],
    format: &str,
    client_id: &str,
    context: &ValidationContext,
) -> Result<ValidationResult, SecurityError> {
    // Check rate limits and resource allocation
    let _permit = enforcer.check_limits(client_id, content.len()).await?;
    
    // Perform validation
    let result = validator.validate_input(content, format, context);
    
    // Check for critical threats
    if !result.is_valid {
        return Err(SecurityError::InvalidInput(
            format!("Validation failed: {:?}", result.errors)
        ));
    }
    
    if result.detected_threats.iter().any(|t| matches!(t.severity, ThreatSeverity::Critical)) {
        return Err(SecurityError::SecurityThreatDetected(
            "Critical security threat detected".to_string()
        ));
    }
    
    Ok(result)
}