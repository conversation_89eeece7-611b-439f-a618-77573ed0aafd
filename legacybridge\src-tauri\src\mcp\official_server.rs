// Official Rust MCP SDK Integration
// Comprehensive MCP server implementation using rust-mcp-sdk for LegacyBridge

#[cfg(feature = "mcp")]
use rust_mcp_sdk::{
    server::{ServerOptions, Server, Handler, InitializeResult},
    transport::{StdioTransport, TransportOptions},
    types::{Tool, Resource, Prompt, JSONRPCMessage, CallToolRequest, CallToolResult, 
            ListToolsRequest, ListResourcesRequest, ReadResourceRequest, 
            GetPromptRequest, ListPromptsRequest},
    errors::{McpError, SdkResult},
    macros::tool,
};

use crate::conversion::ConversionResult;
use crate::config::Config;
use crate::format_detection::FormatDetector;
use crate::legacy_formats::{LegacyConverter, ConversionOptions};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value as JsonValue};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;
use tracing::{info, error, warn, debug};

/// LegacyBridge MCP Server using official rust-mcp-sdk
pub struct LegacyBridgeMcpServerOfficial {
    config: Config,
    format_detector: FormatDetector,
    legacy_converter: LegacyConverter,
    active_jobs: Arc<RwLock<HashMap<String, ConversionJob>>>,
    stats: Arc<RwLock<ServerStats>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionJob {
    pub id: String,
    pub status: JobStatus,
    pub input_format: String,
    pub output_format: String,
    pub progress: f32,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub error: Option<String>,
    pub result: Option<ConversionResult>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum JobStatus {
    Pending,
    Processing,
    Completed,
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerStats {
    pub conversions_total: u64,
    pub conversions_successful: u64,
    pub conversions_failed: u64,
    pub uptime_seconds: u64,
    pub supported_formats: Vec<String>,
    pub legacy_formats_enabled: bool,
}

// Tool argument structures
#[derive(Debug, Deserialize)]
pub struct ConvertFileArgs {
    pub input_content: String,
    pub input_format: Option<String>,
    pub output_format: String,
    pub options: Option<JsonValue>,
}

#[derive(Debug, Deserialize)]
pub struct RtfToMarkdownArgs {
    pub rtf_content: String,
    pub preserve_formatting: Option<bool>,
    pub include_metadata: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct MarkdownToRtfArgs {
    pub markdown_content: String,
    pub template_style: Option<String>,
    pub font_settings: Option<JsonValue>,
}

#[derive(Debug, Deserialize)]
pub struct ConvertLegacyFormatArgs {
    pub input_content: String,
    pub format_type: String, // doc, wpd, dbf, wk1, ws
    pub output_format: String,
    pub options: Option<JsonValue>,
}

#[derive(Debug, Deserialize)]
pub struct DetectFormatArgs {
    pub file_content: String,
    pub filename: Option<String>,
    pub detailed_analysis: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct ValidateFileArgs {
    pub file_content: String,
    pub expected_format: Option<String>,
    pub strict_validation: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct BatchConvertArgs {
    pub files: Vec<BatchFileInput>,
    pub output_format: String,
    pub parallel: Option<bool>,
    pub options: Option<JsonValue>,
}

#[derive(Debug, Deserialize)]
pub struct BatchFileInput {
    pub content: String,
    pub filename: Option<String>,
    pub input_format: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct BuildDllArgs {
    pub target_language: String, // vb6, vfp9, etc.
    pub include_formats: Vec<String>,
    pub output_path: String,
    pub optimization_level: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct GetJobStatusArgs {
    pub job_id: String,
}

impl LegacyBridgeMcpServerOfficial {
    pub fn new(config: Config) -> Self {
        Self {
            format_detector: FormatDetector::new(),
            legacy_converter: LegacyConverter::new(&config),
            config,
            active_jobs: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(ServerStats {
                conversions_total: 0,
                conversions_successful: 0,
                conversions_failed: 0,
                uptime_seconds: 0,
                supported_formats: vec![
                    "rtf".to_string(), "md".to_string(), "txt".to_string(),
                    "html".to_string(), "xml".to_string(), "json".to_string(),
                    "doc".to_string(), "wpd".to_string(), "dbf".to_string(),
                    "wk1".to_string(), "ws".to_string()
                ],
                legacy_formats_enabled: true,
            })),
        }
    }
    
    /// Start the MCP server using stdio transport
    pub async fn run_stdio_server(self) -> SdkResult<()> {
        info!("Starting LegacyBridge MCP Server with official rust-mcp-sdk");
        
        let server_info = InitializeResult {
            protocol_version: "2024-11-05".to_string(),
            server_info: Some(rust_mcp_sdk::types::ServerInfo {
                name: "LegacyBridge MCP Server".to_string(),
                version: "2.0.0".to_string(),
            }),
            capabilities: rust_mcp_sdk::types::ServerCapabilities {
                tools: Some(rust_mcp_sdk::types::ToolsCapability {}),
                resources: Some(rust_mcp_sdk::types::ResourcesCapability {}),
                prompts: Some(rust_mcp_sdk::types::PromptsCapability {}),
                logging: None,
            },
        };
        
        let transport = StdioTransport::new(TransportOptions::default())?;
        let handler = McpHandler::new(self);
        
        let server = rust_mcp_sdk::server::create_server(server_info, transport, handler);
        server.start().await
    }
    
    /// Create job for async operations
    async fn create_job(&self, input_format: String, output_format: String) -> String {
        let job_id = Uuid::new_v4().to_string();
        let job = ConversionJob {
            id: job_id.clone(),
            status: JobStatus::Pending,
            input_format,
            output_format,
            progress: 0.0,
            started_at: chrono::Utc::now(),
            completed_at: None,
            error: None,
            result: None,
        };
        
        self.active_jobs.write().await.insert(job_id.clone(), job);
        job_id
    }
    
    /// Update job status
    async fn update_job(&self, job_id: &str, status: JobStatus, progress: f32, result: Option<ConversionResult>, error: Option<String>) {
        if let Some(job) = self.active_jobs.write().await.get_mut(job_id) {
            job.status = status;
            job.progress = progress;
            if let Some(result) = result {
                job.result = Some(result);
            }
            if let Some(error) = error {
                job.error = Some(error);
            }
            if matches!(job.status, JobStatus::Completed | JobStatus::Failed) {
                job.completed_at = Some(chrono::Utc::now());
            }
        }
    }
    
    /// Get job status
    async fn get_job(&self, job_id: &str) -> Option<ConversionJob> {
        self.active_jobs.read().await.get(job_id).cloned()
    }
}

/// MCP Handler implementation
pub struct McpHandler {
    server: LegacyBridgeMcpServerOfficial,
}

impl McpHandler {
    pub fn new(server: LegacyBridgeMcpServerOfficial) -> Self {
        Self { server }
    }
}

#[cfg(feature = "mcp")]
#[async_trait::async_trait]
impl Handler for McpHandler {
    async fn list_tools(&self, _request: ListToolsRequest) -> SdkResult<Vec<Tool>> {
        Ok(vec![
            Tool {
                name: "convert_file".to_string(),
                description: "Convert a single file between formats. Supports all legacy formats including DOC, WordPerfect, dBase, Lotus 1-2-3, and WordStar.".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "input_content": {
                            "type": "string",
                            "description": "Base64 encoded content of the input file"
                        },
                        "input_format": {
                            "type": "string",
                            "description": "Input format (optional, will be auto-detected)",
                            "enum": ["rtf", "md", "doc", "wpd", "dbf", "wk1", "ws", "txt", "html", "xml", "json"]
                        },
                        "output_format": {
                            "type": "string", 
                            "description": "Target output format",
                            "enum": ["rtf", "md", "txt", "html", "xml", "json", "csv"]
                        },
                        "options": {
                            "type": "object",
                            "description": "Optional conversion settings"
                        }
                    },
                    "required": ["input_content", "output_format"]
                }),
            },
            Tool {
                name: "rtf_to_markdown".to_string(),
                description: "Convert RTF content directly to Markdown with advanced formatting preservation".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "rtf_content": {
                            "type": "string",
                            "description": "RTF content to convert"
                        },
                        "preserve_formatting": {
                            "type": "boolean",
                            "description": "Whether to preserve complex formatting",
                            "default": true
                        },
                        "include_metadata": {
                            "type": "boolean", 
                            "description": "Include document metadata in output",
                            "default": false
                        }
                    },
                    "required": ["rtf_content"]
                }),
            },
            Tool {
                name: "markdown_to_rtf".to_string(),
                description: "Convert Markdown content to RTF with customizable styling".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "markdown_content": {
                            "type": "string",
                            "description": "Markdown content to convert"
                        },
                        "template_style": {
                            "type": "string",
                            "description": "RTF template style",
                            "enum": ["default", "professional", "academic", "minimal"],
                            "default": "default"
                        },
                        "font_settings": {
                            "type": "object",
                            "description": "Font customization options"
                        }
                    },
                    "required": ["markdown_content"]
                }),
            },
            Tool {
                name: "convert_legacy_format".to_string(),
                description: "Convert legacy formats (DOC, WordPerfect, dBase, Lotus 1-2-3, WordStar) to modern formats".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "input_content": {
                            "type": "string",
                            "description": "Base64 encoded legacy file content"
                        },
                        "format_type": {
                            "type": "string",
                            "description": "Legacy format type",
                            "enum": ["doc", "wpd", "dbf", "wk1", "ws"]
                        },
                        "output_format": {
                            "type": "string",
                            "description": "Target modern format",
                            "enum": ["md", "rtf", "txt", "html", "csv", "json"]
                        },
                        "options": {
                            "type": "object",
                            "description": "Legacy format specific options"
                        }
                    },
                    "required": ["input_content", "format_type", "output_format"]
                }),
            },
            Tool {
                name: "detect_file_format".to_string(),
                description: "Detect and analyze file format with detailed information".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "file_content": {
                            "type": "string",
                            "description": "Base64 encoded file content"
                        },
                        "filename": {
                            "type": "string",
                            "description": "Optional filename for additional context"
                        },
                        "detailed_analysis": {
                            "type": "boolean",
                            "description": "Perform detailed format analysis",
                            "default": false
                        }
                    },
                    "required": ["file_content"]
                }),
            },
            Tool {
                name: "validate_file".to_string(),
                description: "Validate file integrity and format compliance".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "file_content": {
                            "type": "string",
                            "description": "Base64 encoded file content"
                        },
                        "expected_format": {
                            "type": "string",
                            "description": "Expected file format"
                        },
                        "strict_validation": {
                            "type": "boolean",
                            "description": "Enable strict validation mode",
                            "default": false
                        }
                    },
                    "required": ["file_content"]
                }),
            },
            Tool {
                name: "batch_convert".to_string(),
                description: "Convert multiple files in batch with parallel processing".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "files": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "content": {"type": "string"},
                                    "filename": {"type": "string"},
                                    "input_format": {"type": "string"}
                                },
                                "required": ["content"]
                            }
                        },
                        "output_format": {
                            "type": "string",
                            "description": "Target format for all files"
                        },
                        "parallel": {
                            "type": "boolean",
                            "description": "Enable parallel processing",
                            "default": true
                        },
                        "options": {
                            "type": "object",
                            "description": "Batch conversion options"
                        }
                    },
                    "required": ["files", "output_format"]
                }),
            },
            Tool {
                name: "build_dll".to_string(),
                description: "Build legacy DLL for VB6/VFP9 integration".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "target_language": {
                            "type": "string",
                            "description": "Target legacy language",
                            "enum": ["vb6", "vfp9", "delphi", "c++"]
                        },
                        "include_formats": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Formats to include in DLL"
                        },
                        "output_path": {
                            "type": "string",
                            "description": "Output path for the DLL"
                        },
                        "optimization_level": {
                            "type": "string",
                            "enum": ["debug", "release", "size"],
                            "default": "release"
                        }
                    },
                    "required": ["target_language", "include_formats", "output_path"]
                }),
            },
            Tool {
                name: "get_job_status".to_string(),
                description: "Get status of an async conversion job".to_string(),
                input_schema: json!({
                    "type": "object",
                    "properties": {
                        "job_id": {
                            "type": "string",
                            "description": "Job ID to check status for"
                        }
                    },
                    "required": ["job_id"]
                }),
            },
        ])
    }
    
    async fn call_tool(&self, request: CallToolRequest) -> SdkResult<CallToolResult> {
        let tool_name = &request.params.name;
        let arguments = &request.params.arguments;
        
        debug!("Calling tool: {} with arguments", tool_name);
        
        match tool_name.as_str() {
            "convert_file" => {
                let args: ConvertFileArgs = serde_json::from_value(arguments.clone())
                    .map_err(|e| McpError::InvalidParams(format!("Invalid arguments: {}", e)))?;
                self.handle_convert_file(args).await
            },
            "rtf_to_markdown" => {
                let args: RtfToMarkdownArgs = serde_json::from_value(arguments.clone())
                    .map_err(|e| McpError::InvalidParams(format!("Invalid arguments: {}", e)))?;
                self.handle_rtf_to_markdown(args).await
            },
            "markdown_to_rtf" => {
                let args: MarkdownToRtfArgs = serde_json::from_value(arguments.clone())
                    .map_err(|e| McpError::InvalidParams(format!("Invalid arguments: {}", e)))?;
                self.handle_markdown_to_rtf(args).await
            },
            "convert_legacy_format" => {
                let args: ConvertLegacyFormatArgs = serde_json::from_value(arguments.clone())
                    .map_err(|e| McpError::InvalidParams(format!("Invalid arguments: {}", e)))?;
                self.handle_convert_legacy_format(args).await
            },
            "detect_file_format" => {
                let args: DetectFormatArgs = serde_json::from_value(arguments.clone())
                    .map_err(|e| McpError::InvalidParams(format!("Invalid arguments: {}", e)))?;
                self.handle_detect_format(args).await
            },
            "validate_file" => {
                let args: ValidateFileArgs = serde_json::from_value(arguments.clone())
                    .map_err(|e| McpError::InvalidParams(format!("Invalid arguments: {}", e)))?;
                self.handle_validate_file(args).await
            },
            "batch_convert" => {
                let args: BatchConvertArgs = serde_json::from_value(arguments.clone())
                    .map_err(|e| McpError::InvalidParams(format!("Invalid arguments: {}", e)))?;
                self.handle_batch_convert(args).await
            },
            "build_dll" => {
                let args: BuildDllArgs = serde_json::from_value(arguments.clone())
                    .map_err(|e| McpError::InvalidParams(format!("Invalid arguments: {}", e)))?;
                self.handle_build_dll(args).await
            },
            "get_job_status" => {
                let args: GetJobStatusArgs = serde_json::from_value(arguments.clone())
                    .map_err(|e| McpError::InvalidParams(format!("Invalid arguments: {}", e)))?;
                self.handle_get_job_status(args).await
            },
            _ => Err(McpError::MethodNotFound(format!("Unknown tool: {}", tool_name))),
        }
    }
    
    async fn list_resources(&self, _request: ListResourcesRequest) -> SdkResult<Vec<Resource>> {
        Ok(vec![
            Resource {
                uri: "formats://supported".to_string(),
                name: "Supported Formats".to_string(),
                description: Some("List all supported file formats with conversion capabilities".to_string()),
                mime_type: Some("application/json".to_string()),
            },
            Resource {
                uri: "formats://legacy".to_string(),
                name: "Legacy Formats".to_string(),
                description: Some("Detailed information about legacy format support (DOC, WordPerfect, dBase, etc.)".to_string()),
                mime_type: Some("application/json".to_string()),
            },
            Resource {
                uri: "stats://server".to_string(),
                name: "Server Statistics".to_string(),
                description: Some("LegacyBridge MCP server performance metrics and statistics".to_string()),
                mime_type: Some("application/json".to_string()),
            },
            Resource {
                uri: "jobs://active".to_string(),
                name: "Active Jobs".to_string(),
                description: Some("Currently active conversion jobs and their status".to_string()),
                mime_type: Some("application/json".to_string()),
            },
            Resource {
                uri: "config://server".to_string(),
                name: "Server Configuration".to_string(),
                description: Some("Current server configuration and feature flags".to_string()),
                mime_type: Some("application/json".to_string()),
            },
        ])
    }
    
    async fn read_resource(&self, request: ReadResourceRequest) -> SdkResult<rust_mcp_sdk::types::ReadResourceResult> {
        let uri = &request.params.uri;
        
        let content = match uri.as_str() {
            "formats://supported" => self.get_supported_formats().await,
            "formats://legacy" => self.get_legacy_formats_info().await,
            "stats://server" => self.get_server_stats().await,
            "jobs://active" => self.get_active_jobs().await,
            "config://server" => self.get_server_config().await,
            _ => return Err(McpError::InvalidParams(format!("Unknown resource: {}", uri))),
        };
        
        Ok(rust_mcp_sdk::types::ReadResourceResult {
            contents: vec![rust_mcp_sdk::types::ResourceContent {
                uri: uri.clone(),
                mime_type: Some("application/json".to_string()),
                text: Some(content.to_string()),
                blob: None,
            }],
        })
    }
    
    async fn list_prompts(&self, _request: ListPromptsRequest) -> SdkResult<Vec<Prompt>> {
        Ok(vec![
            Prompt {
                name: "convert_legacy_document".to_string(),
                description: Some("Generate a conversion prompt for legacy documents".to_string()),
                arguments: Some(vec![
                    rust_mcp_sdk::types::PromptArgument {
                        name: "format".to_string(),
                        description: Some("Legacy format to convert from".to_string()),
                        required: Some(true),
                    },
                    rust_mcp_sdk::types::PromptArgument {
                        name: "target".to_string(),
                        description: Some("Target modern format".to_string()),
                        required: Some(true),
                    },
                ]),
            },
            Prompt {
                name: "batch_conversion_strategy".to_string(),
                description: Some("Generate strategy for batch converting legacy files".to_string()),
                arguments: Some(vec![
                    rust_mcp_sdk::types::PromptArgument {
                        name: "file_count".to_string(),
                        description: Some("Number of files to convert".to_string()),
                        required: Some(true),
                    },
                    rust_mcp_sdk::types::PromptArgument {
                        name: "formats".to_string(),
                        description: Some("Comma-separated list of formats".to_string()),
                        required: Some(true),
                    },
                ]),
            },
        ])
    }
    
    async fn get_prompt(&self, request: GetPromptRequest) -> SdkResult<rust_mcp_sdk::types::GetPromptResult> {
        let prompt_name = &request.params.name;
        let arguments = request.params.arguments.as_ref().unwrap_or(&HashMap::new());
        
        let message = match prompt_name.as_str() {
            "convert_legacy_document" => {
                let format = arguments.get("format").unwrap_or(&JsonValue::String("unknown".to_string()));
                let target = arguments.get("target").unwrap_or(&JsonValue::String("markdown".to_string()));
                
                format!("You are converting a legacy {} document to {}. Here are the key considerations:\n\n1. Preserve document structure and formatting where possible\n2. Handle legacy-specific elements (embedded objects, macros, etc.)\n3. Convert character encodings appropriately\n4. Maintain data integrity throughout the process\n5. Generate metadata about the conversion process\n\nPlease ensure the output is clean, well-formatted, and maintains the original document's intent.", format, target)
            },
            "batch_conversion_strategy" => {
                let file_count = arguments.get("file_count").unwrap_or(&JsonValue::Number(serde_json::Number::from(1)));
                let formats = arguments.get("formats").unwrap_or(&JsonValue::String("mixed".to_string()));
                
                format!("You are planning a batch conversion of {} files with formats: {}. Consider these factors:\n\n1. Processing order (prioritize by complexity/size)\n2. Resource management (memory, CPU utilization)\n3. Error handling and recovery strategies\n4. Progress tracking and reporting\n5. Quality assurance checks\n6. Parallel processing opportunities\n\nProvide a comprehensive strategy that maximizes efficiency while ensuring quality.", file_count, formats)
            },
            _ => return Err(McpError::MethodNotFound(format!("Unknown prompt: {}", prompt_name))),
        };
        
        Ok(rust_mcp_sdk::types::GetPromptResult {
            description: Some(format!("Generated prompt for {}", prompt_name)),
            messages: vec![rust_mcp_sdk::types::PromptMessage {
                role: rust_mcp_sdk::types::Role::User,
                content: rust_mcp_sdk::types::TextContent {
                    type_: "text".to_string(),
                    text: message,
                },
            }],
        })
    }
}

impl McpHandler {
    // Tool handler implementations
    async fn handle_convert_file(&self, args: ConvertFileArgs) -> SdkResult<CallToolResult> {
        info!("Converting file: {} -> {}", args.input_format.as_deref().unwrap_or("auto"), args.output_format);
        
        // Decode base64 content
        let content = base64::decode(&args.input_content)
            .map_err(|e| McpError::InvalidParams(format!("Invalid base64 content: {}", e)))?;
        
        // Detect format if not provided
        let input_format = if let Some(format) = args.input_format {
            format
        } else {
            self.server.format_detector.detect_format(&content, None)
                .map_err(|e| McpError::InternalError(format!("Format detection failed: {}", e)))?
                .format_id
        };
        
        // Create conversion job
        let job_id = self.server.create_job(input_format.clone(), args.output_format.clone()).await;
        
        // Update stats
        {
            let mut stats = self.server.stats.write().await;
            stats.conversions_total += 1;
        }
        
        // Perform conversion
        match self.server.legacy_converter.convert(&content, &input_format, &args.output_format, args.options).await {
            Ok(result) => {
                self.server.update_job(&job_id, JobStatus::Completed, 100.0, Some(result.clone()), None).await;
                
                // Update success stats
                {
                    let mut stats = self.server.stats.write().await;
                    stats.conversions_successful += 1;
                }
                
                Ok(CallToolResult {
                    content: vec![rust_mcp_sdk::types::TextContent {
                        type_: "text".to_string(),
                        text: serde_json::to_string_pretty(&json!({
                            "success": true,
                            "job_id": job_id,
                            "input_format": input_format,
                            "output_format": args.output_format,
                            "content": result.content,
                            "metadata": result.metadata,
                            "conversion_time_ms": result.processing_time_ms
                        })).unwrap(),
                    }],
                    is_error: None,
                })
            },
            Err(e) => {
                let error_msg = format!("Conversion failed: {}", e);
                self.server.update_job(&job_id, JobStatus::Failed, 0.0, None, Some(error_msg.clone())).await;
                
                // Update failure stats
                {
                    let mut stats = self.server.stats.write().await;
                    stats.conversions_failed += 1;
                }
                
                Err(McpError::InternalError(error_msg))
            }
        }
    }
    
    async fn handle_rtf_to_markdown(&self, args: RtfToMarkdownArgs) -> SdkResult<CallToolResult> {
        info!("Converting RTF to Markdown");
        
        let options = ConversionOptions {
            preserve_formatting: args.preserve_formatting.unwrap_or(true),
            include_metadata: args.include_metadata.unwrap_or(false),
            ..Default::default()
        };
        
        match self.server.legacy_converter.rtf_to_markdown(&args.rtf_content, &options).await {
            Ok(result) => {
                Ok(CallToolResult {
                    content: vec![rust_mcp_sdk::types::TextContent {
                        type_: "text".to_string(),
                        text: serde_json::to_string_pretty(&json!({
                            "success": true,
                            "markdown_content": result.content,
                            "metadata": result.metadata,
                            "conversion_time_ms": result.processing_time_ms
                        })).unwrap(),
                    }],
                    is_error: None,
                })
            },
            Err(e) => Err(McpError::InternalError(format!("RTF to Markdown conversion failed: {}", e))),
        }
    }
    
    async fn handle_markdown_to_rtf(&self, args: MarkdownToRtfArgs) -> SdkResult<CallToolResult> {
        info!("Converting Markdown to RTF");
        
        let options = ConversionOptions {
            template_style: args.template_style.unwrap_or_else(|| "default".to_string()),
            font_settings: args.font_settings,
            ..Default::default()
        };
        
        match self.server.legacy_converter.markdown_to_rtf(&args.markdown_content, &options).await {
            Ok(result) => {
                Ok(CallToolResult {
                    content: vec![rust_mcp_sdk::types::TextContent {
                        type_: "text".to_string(),
                        text: serde_json::to_string_pretty(&json!({
                            "success": true,
                            "rtf_content": result.content,
                            "metadata": result.metadata,
                            "conversion_time_ms": result.processing_time_ms
                        })).unwrap(),
                    }],
                    is_error: None,
                })
            },
            Err(e) => Err(McpError::InternalError(format!("Markdown to RTF conversion failed: {}", e))),
        }
    }
    
    async fn handle_convert_legacy_format(&self, args: ConvertLegacyFormatArgs) -> SdkResult<CallToolResult> {
        info!("Converting legacy format: {} -> {}", args.format_type, args.output_format);
        
        // Decode base64 content
        let content = base64::decode(&args.input_content)
            .map_err(|e| McpError::InvalidParams(format!("Invalid base64 content: {}", e)))?;
        
        // Create job for tracking
        let job_id = self.server.create_job(args.format_type.clone(), args.output_format.clone()).await;
        
        // Perform legacy format conversion
        match self.server.legacy_converter.convert_legacy_format(&content, &args.format_type, &args.output_format, args.options).await {
            Ok(result) => {
                self.server.update_job(&job_id, JobStatus::Completed, 100.0, Some(result.clone()), None).await;
                
                Ok(CallToolResult {
                    content: vec![rust_mcp_sdk::types::TextContent {
                        type_: "text".to_string(),
                        text: serde_json::to_string_pretty(&json!({
                            "success": true,
                            "job_id": job_id,
                            "legacy_format": args.format_type,
                            "output_format": args.output_format,
                            "content": result.content,
                            "metadata": result.metadata,
                            "warnings": result.warnings,
                            "conversion_time_ms": result.processing_time_ms
                        })).unwrap(),
                    }],
                    is_error: None,
                })
            },
            Err(e) => {
                let error_msg = format!("Legacy format conversion failed: {}", e);
                self.server.update_job(&job_id, JobStatus::Failed, 0.0, None, Some(error_msg.clone())).await;
                Err(McpError::InternalError(error_msg))
            }
        }
    }
    
    async fn handle_detect_format(&self, args: DetectFormatArgs) -> SdkResult<CallToolResult> {
        info!("Detecting file format");
        
        // Decode base64 content
        let content = base64::decode(&args.file_content)
            .map_err(|e| McpError::InvalidParams(format!("Invalid base64 content: {}", e)))?;
        
        match self.server.format_detector.detect_format(&content, args.filename.as_deref()) {
            Ok(detection_result) => {
                let detailed_info = if args.detailed_analysis.unwrap_or(false) {
                    self.server.format_detector.analyze_format_details(&content, &detection_result)
                } else {
                    None
                };
                
                Ok(CallToolResult {
                    content: vec![rust_mcp_sdk::types::TextContent {
                        type_: "text".to_string(),
                        text: serde_json::to_string_pretty(&json!({
                            "success": true,
                            "detected_format": detection_result.format_id,
                            "confidence": detection_result.confidence,
                            "file_size": content.len(),
                            "magic_bytes": detection_result.magic_bytes,
                            "detailed_analysis": detailed_info,
                            "supported_conversions": detection_result.supported_conversions
                        })).unwrap(),
                    }],
                    is_error: None,
                })
            },
            Err(e) => Err(McpError::InternalError(format!("Format detection failed: {}", e))),
        }
    }
    
    async fn handle_validate_file(&self, args: ValidateFileArgs) -> SdkResult<CallToolResult> {
        info!("Validating file");
        
        // Decode base64 content
        let content = base64::decode(&args.file_content)
            .map_err(|e| McpError::InvalidParams(format!("Invalid base64 content: {}", e)))?;
        
        match self.server.format_detector.validate_file(&content, args.expected_format.as_deref(), args.strict_validation.unwrap_or(false)) {
            Ok(validation_result) => {
                Ok(CallToolResult {
                    content: vec![rust_mcp_sdk::types::TextContent {
                        type_: "text".to_string(),
                        text: serde_json::to_string_pretty(&json!({
                            "success": true,
                            "is_valid": validation_result.is_valid,
                            "detected_format": validation_result.detected_format,
                            "expected_format": args.expected_format,
                            "validation_errors": validation_result.errors,
                            "validation_warnings": validation_result.warnings,
                            "file_integrity": validation_result.integrity_check
                        })).unwrap(),
                    }],
                    is_error: None,
                })
            },
            Err(e) => Err(McpError::InternalError(format!("File validation failed: {}", e))),
        }
    }
    
    async fn handle_batch_convert(&self, args: BatchConvertArgs) -> SdkResult<CallToolResult> {
        info!("Starting batch conversion of {} files", args.files.len());
        
        let job_id = self.server.create_job("batch".to_string(), args.output_format.clone()).await;
        let mut results = Vec::new();
        let mut successful = 0;
        let mut failed = 0;
        
        // Process files (parallel if requested)
        if args.parallel.unwrap_or(true) && args.files.len() > 1 {
            // Parallel processing using rayon
            use rayon::prelude::*;
            
            let conversion_results: Vec<_> = args.files.par_iter().enumerate().map(|(index, file)| {
                let content = match base64::decode(&file.content) {
                    Ok(content) => content,
                    Err(e) => return (index, Err(format!("Invalid base64 content: {}", e))),
                };
                
                let input_format = if let Some(format) = &file.input_format {
                    format.clone()
                } else {
                    match self.server.format_detector.detect_format(&content, file.filename.as_deref()) {
                        Ok(detection) => detection.format_id,
                        Err(e) => return (index, Err(format!("Format detection failed: {}", e))),
                    }
                };
                
                // Note: This is a simplified version - in reality, you'd need to handle async properly in parallel context
                (index, Ok((input_format, content)))
            }).collect();
            
            for (index, result) in conversion_results {
                match result {
                    Ok((input_format, content)) => {
                        // Sequential conversion for now (could be optimized further)
                        match self.server.legacy_converter.convert(&content, &input_format, &args.output_format, args.options.clone()).await {
                            Ok(conversion_result) => {
                                results.push(json!({
                                    "index": index,
                                    "filename": args.files[index].filename,
                                    "success": true,
                                    "input_format": input_format,
                                    "content": conversion_result.content,
                                    "metadata": conversion_result.metadata
                                }));
                                successful += 1;
                            },
                            Err(e) => {
                                results.push(json!({
                                    "index": index,
                                    "filename": args.files[index].filename,
                                    "success": false,
                                    "error": format!("Conversion failed: {}", e)
                                }));
                                failed += 1;
                            }
                        }
                    },
                    Err(e) => {
                        results.push(json!({
                            "index": index,
                            "filename": args.files[index].filename,
                            "success": false,
                            "error": e
                        }));
                        failed += 1;
                    }
                }
            }
        } else {
            // Sequential processing
            for (index, file) in args.files.iter().enumerate() {
                let progress = (index as f32 / args.files.len() as f32) * 100.0;
                self.server.update_job(&job_id, JobStatus::Processing, progress, None, None).await;
                
                let content = match base64::decode(&file.content) {
                    Ok(content) => content,
                    Err(e) => {
                        results.push(json!({
                            "index": index,
                            "filename": file.filename,
                            "success": false,
                            "error": format!("Invalid base64 content: {}", e)
                        }));
                        failed += 1;
                        continue;
                    }
                };
                
                let input_format = if let Some(format) = &file.input_format {
                    format.clone()
                } else {
                    match self.server.format_detector.detect_format(&content, file.filename.as_deref()) {
                        Ok(detection) => detection.format_id,
                        Err(e) => {
                            results.push(json!({
                                "index": index,
                                "filename": file.filename,
                                "success": false,
                                "error": format!("Format detection failed: {}", e)
                            }));
                            failed += 1;
                            continue;
                        }
                    }
                };
                
                match self.server.legacy_converter.convert(&content, &input_format, &args.output_format, args.options.clone()).await {
                    Ok(conversion_result) => {
                        results.push(json!({
                            "index": index,
                            "filename": file.filename,
                            "success": true,
                            "input_format": input_format,
                            "content": conversion_result.content,
                            "metadata": conversion_result.metadata
                        }));
                        successful += 1;
                    },
                    Err(e) => {
                        results.push(json!({
                            "index": index,
                            "filename": file.filename,
                            "success": false,
                            "error": format!("Conversion failed: {}", e)
                        }));
                        failed += 1;
                    }
                }
            }
        }
        
        self.server.update_job(&job_id, JobStatus::Completed, 100.0, None, None).await;
        
        Ok(CallToolResult {
            content: vec![rust_mcp_sdk::types::TextContent {
                type_: "text".to_string(),
                text: serde_json::to_string_pretty(&json!({
                    "success": true,
                    "job_id": job_id,
                    "batch_summary": {
                        "total_files": args.files.len(),
                        "successful": successful,
                        "failed": failed,
                        "output_format": args.output_format
                    },
                    "results": results
                })).unwrap(),
            }],
            is_error: None,
        })
    }
    
    async fn handle_build_dll(&self, args: BuildDllArgs) -> SdkResult<CallToolResult> {
        info!("Building DLL for {} with formats: {:?}", args.target_language, args.include_formats);
        
        let job_id = self.server.create_job("dll_build".to_string(), args.target_language.clone()).await;
        
        // This would typically involve calling the DLL builder
        match self.server.legacy_converter.build_dll(&args.target_language, &args.include_formats, &args.output_path, args.optimization_level.as_deref()).await {
            Ok(build_result) => {
                self.server.update_job(&job_id, JobStatus::Completed, 100.0, None, None).await;
                
                Ok(CallToolResult {
                    content: vec![rust_mcp_sdk::types::TextContent {
                        type_: "text".to_string(),
                        text: serde_json::to_string_pretty(&json!({
                            "success": true,
                            "job_id": job_id,
                            "target_language": args.target_language,
                            "dll_path": build_result.dll_path,
                            "included_formats": args.include_formats,
                            "build_time_ms": build_result.build_time_ms,
                            "dll_size_bytes": build_result.dll_size_bytes,
                            "exported_functions": build_result.exported_functions
                        })).unwrap(),
                    }],
                    is_error: None,
                })
            },
            Err(e) => {
                let error_msg = format!("DLL build failed: {}", e);
                self.server.update_job(&job_id, JobStatus::Failed, 0.0, None, Some(error_msg.clone())).await;
                Err(McpError::InternalError(error_msg))
            }
        }
    }
    
    async fn handle_get_job_status(&self, args: GetJobStatusArgs) -> SdkResult<CallToolResult> {
        if let Some(job) = self.server.get_job(&args.job_id).await {
            Ok(CallToolResult {
                content: vec![rust_mcp_sdk::types::TextContent {
                    type_: "text".to_string(),
                    text: serde_json::to_string_pretty(&job).unwrap(),
                }],
                is_error: None,
            })
        } else {
            Err(McpError::InvalidParams(format!("Job not found: {}", args.job_id)))
        }
    }
    
    // Resource handler implementations
    async fn get_supported_formats(&self) -> JsonValue {
        json!({
            "formats": {
                "legacy": [
                    {
                        "id": "rtf",
                        "name": "Rich Text Format",
                        "extensions": [".rtf"],
                        "bidirectional": true,
                        "quality_rating": 5
                    },
                    {
                        "id": "doc",
                        "name": "Microsoft Word 97-2003",
                        "extensions": [".doc"],
                        "bidirectional": false,
                        "quality_rating": 4,
                        "feature_flag": "format-doc"
                    },
                    {
                        "id": "wpd",
                        "name": "WordPerfect Document",
                        "extensions": [".wpd", ".wp5", ".wp6"],
                        "bidirectional": false,
                        "quality_rating": 3,
                        "feature_flag": "format-wordperfect"
                    },
                    {
                        "id": "dbf",
                        "name": "dBase Database",
                        "extensions": [".dbf", ".db3", ".db4"],
                        "bidirectional": false,
                        "quality_rating": 4,
                        "feature_flag": "format-dbase"
                    },
                    {
                        "id": "wk1",
                        "name": "Lotus 1-2-3 Spreadsheet",
                        "extensions": [".wk1", ".wks", ".123"],
                        "bidirectional": false,
                        "quality_rating": 3,
                        "feature_flag": "format-lotus"
                    },
                    {
                        "id": "ws",
                        "name": "WordStar Document",
                        "extensions": [".ws", ".wsd"],
                        "bidirectional": false,
                        "quality_rating": 2,
                        "feature_flag": "format-wordstar"
                    }
                ],
                "modern": [
                    {
                        "id": "md",
                        "name": "Markdown",
                        "extensions": [".md", ".markdown"],
                        "bidirectional": true,
                        "quality_rating": 5
                    },
                    {
                        "id": "html",
                        "name": "HTML",
                        "extensions": [".html", ".htm"],
                        "bidirectional": true,
                        "quality_rating": 4
                    },
                    {
                        "id": "txt",
                        "name": "Plain Text",
                        "extensions": [".txt"],
                        "bidirectional": true,
                        "quality_rating": 5
                    },
                    {
                        "id": "json",
                        "name": "JSON",
                        "extensions": [".json"],
                        "bidirectional": true,
                        "quality_rating": 4
                    },
                    {
                        "id": "xml",
                        "name": "XML",
                        "extensions": [".xml"],
                        "bidirectional": true,
                        "quality_rating": 3
                    },
                    {
                        "id": "csv",
                        "name": "CSV",
                        "extensions": [".csv"],
                        "bidirectional": true,
                        "quality_rating": 5
                    }
                ]
            },
            "conversion_matrix": {
                "rtf": ["md", "html", "txt"],
                "doc": ["rtf", "md", "html", "txt"],
                "wpd": ["rtf", "md", "html", "txt"],
                "dbf": ["csv", "json", "md", "html"],
                "wk1": ["csv", "json", "md", "html"],
                "ws": ["txt", "md", "rtf"],
                "md": ["rtf", "html", "txt"],
                "html": ["md", "txt", "rtf"],
                "txt": ["md", "html", "rtf"],
                "json": ["xml", "csv", "md"],
                "xml": ["json", "md", "txt"],
                "csv": ["json", "md", "html"]
            }
        })
    }
    
    async fn get_legacy_formats_info(&self) -> JsonValue {
        json!({
            "legacy_formats": {
                "doc": {
                    "name": "Microsoft Word 97-2003",
                    "description": "Legacy binary Word format used in VB6/VFP9 applications",
                    "magic_bytes": ["D0CF11E0A1B11AE1"],
                    "conversion_capabilities": {
                        "preserves_formatting": true,
                        "supports_tables": true,
                        "supports_images": true,
                        "supports_metadata": true
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Complex embedded objects may not convert perfectly",
                        "Macros are stripped during conversion"
                    ],
                    "recommended_outputs": ["rtf", "md", "html"]
                },
                "wpd": {
                    "name": "WordPerfect Document",
                    "description": "Corel WordPerfect documents - popular in legacy business environments",
                    "magic_bytes": ["FF575043", "FF574458"],
                    "conversion_capabilities": {
                        "preserves_formatting": true,
                        "supports_tables": true,
                        "supports_images": false,
                        "supports_metadata": true
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Some WordPerfect-specific formatting may be lost",
                        "Image handling limited"
                    ],
                    "recommended_outputs": ["rtf", "md", "html", "txt"]
                },
                "dbf": {
                    "name": "dBase Database",
                    "description": "dBase database files - widely used in legacy data processing",
                    "magic_bytes": ["03", "04", "05"],
                    "conversion_capabilities": {
                        "preserves_formatting": false,
                        "supports_tables": true,
                        "supports_images": false,
                        "supports_metadata": true
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Memo fields may require special handling",
                        "Index files (.ndx, .cdx) not processed"
                    ],
                    "recommended_outputs": ["csv", "json", "md"]
                },
                "wk1": {
                    "name": "Lotus 1-2-3 Spreadsheet",
                    "description": "Lotus 1-2-3 spreadsheet files - common in legacy financial systems",
                    "magic_bytes": ["0000020006040400", "00001A0000100400"],
                    "conversion_capabilities": {
                        "preserves_formatting": false,
                        "supports_tables": true,
                        "supports_images": false,
                        "supports_metadata": false
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Formulas converted to values only",
                        "Macros are not supported",
                        "Complex formatting may be lost"
                    ],
                    "recommended_outputs": ["csv", "json", "md", "html"]
                },
                "ws": {
                    "name": "WordStar Document",
                    "description": "WordStar text processor files - early word processing standard",
                    "magic_bytes": ["1D7D"],
                    "conversion_capabilities": {
                        "preserves_formatting": false,
                        "supports_tables": false,
                        "supports_images": false,
                        "supports_metadata": false
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Limited formatting preservation",
                        "Text-only output recommended"
                    ],
                    "recommended_outputs": ["txt", "md", "rtf"]
                }
            },
            "feature_flags": {
                "format-doc": "Enables Microsoft Word DOC format support",
                "format-wordperfect": "Enables WordPerfect format support",
                "format-dbase": "Enables dBase database format support",
                "format-lotus": "Enables Lotus 1-2-3 spreadsheet support",
                "format-wordstar": "Enables WordStar document support"
            }
        })
    }
    
    async fn get_server_stats(&self) -> JsonValue {
        let stats = self.server.stats.read().await;
        json!({
            "server_info": {
                "name": "LegacyBridge MCP Server",
                "version": "2.0.0",
                "protocol_version": "2024-11-05",
                "sdk": "rust-mcp-sdk v0.2.0"
            },
            "statistics": {
                "conversions_total": stats.conversions_total,
                "conversions_successful": stats.conversions_successful,
                "conversions_failed": stats.conversions_failed,
                "success_rate": if stats.conversions_total > 0 { 
                    (stats.conversions_successful as f64 / stats.conversions_total as f64) * 100.0 
                } else { 0.0 },
                "uptime_seconds": stats.uptime_seconds
            },
            "capabilities": {
                "supported_formats": stats.supported_formats,
                "legacy_formats_enabled": stats.legacy_formats_enabled,
                "parallel_processing": true,
                "batch_conversion": true,
                "dll_building": true,
                "format_detection": true,
                "file_validation": true
            }
        })
    }
    
    async fn get_active_jobs(&self) -> JsonValue {
        let jobs = self.server.active_jobs.read().await;
        json!({
            "active_jobs": jobs.values().collect::<Vec<_>>(),
            "total_active": jobs.len()
        })
    }
    
    async fn get_server_config(&self) -> JsonValue {
        json!({
            "config": {
                "legacy_formats": {
                    "doc_enabled": self.server.config.features.format_doc,
                    "wordperfect_enabled": self.server.config.features.format_wordperfect,
                    "dbase_enabled": self.server.config.features.format_dbase,
                    "lotus_enabled": self.server.config.features.format_lotus,
                    "wordstar_enabled": self.server.config.features.format_wordstar
                },
                "processing": {
                    "max_file_size_mb": self.server.config.processing.max_file_size_mb,
                    "parallel_jobs": self.server.config.processing.parallel_jobs,
                    "timeout_seconds": self.server.config.processing.timeout_seconds
                },
                "mcp_server": {
                    "transport": "stdio",
                    "protocol_version": "2024-11-05"
                }
            }
        })
    }
}

// Re-export for easier imports
pub use LegacyBridgeMcpServerOfficial as OfficialMcpServer;