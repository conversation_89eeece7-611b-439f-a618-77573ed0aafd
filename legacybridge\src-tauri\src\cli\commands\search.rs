use crate::cli::app::SearchArgs;
use crate::cli::output::{OutputFormat, OutputFormatter};
use crate::cli::utils::ContentExtractor;
use crate::cli::CliError;
use crate::conversion::format_detector::FormatDetector;
use serde::{Serialize, Deserialize};
use std::path::{Path, PathBuf};
use tokio::fs;
use regex::Regex;
use indicatif::{ProgressBar, ProgressStyle};
use walkdir::WalkDir;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub file_path: String,
    pub file_name: String,
    pub format: String,
    pub matches: Vec<Match>,
    pub metadata: Option<SearchMetadata>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Match {
    pub line_number: Option<usize>,
    pub column: Option<usize>,
    pub context: String,
    pub match_type: MatchType,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum MatchType {
    Content,
    Metadata,
    Filename,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchMetadata {
    pub title: Option<String>,
    pub author: Option<String>,
    pub creation_date: Option<String>,
    pub modification_date: Option<String>,
    pub keywords: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchSummary {
    pub total_files_searched: usize,
    pub files_with_matches: usize,
    pub total_matches: usize,
    pub search_duration_ms: u128,
    pub formats_found: HashMap<String, usize>,
}

pub async fn execute_search_command(args: SearchArgs) -> Result<(), CliError> {
    let formatter = OutputFormatter::new(args.output_format.clone());
    let start_time = std::time::Instant::now();
    
    // Compile regex if needed
    let search_pattern = if args.regex {
        match Regex::new(&args.query) {
            Ok(regex) => SearchPattern::Regex(regex),
            Err(e) => return Err(CliError::Validation(format!("Invalid regex pattern: {}", e))),
        }
    } else if args.case_sensitive {
        SearchPattern::Literal(args.query.clone())
    } else {
        SearchPattern::CaseInsensitive(args.query.to_lowercase())
    };
    
    // Collect files to search
    let files = collect_files(&args.directory, &args.files, args.recursive, args.include_hidden)?;
    
    if files.is_empty() {
        formatter.print_warning("No files found to search");
        return Ok(());
    }
    
    // Set up progress bar
    let progress = ProgressBar::new(files.len() as u64);
    progress.set_style(
        ProgressStyle::default_bar()
            .template("{spinner:.green} [{bar:40.cyan/blue}] {pos}/{len} {msg}")
            .unwrap()
            .progress_chars("#>-")
    );
    
    let mut results = Vec::new();
    let mut total_matches = 0;
    let mut formats_found = HashMap::new();
    
    for file_path in &files {
        progress.set_message(format!("Searching: {}", file_path.file_name().unwrap_or_default().to_string_lossy()));
        
        if let Ok(search_result) = search_file(
            file_path,
            &search_pattern,
            args.content,
            args.metadata,
        ).await {
            if !search_result.matches.is_empty() {
                total_matches += search_result.matches.len();
                *formats_found.entry(search_result.format.clone()).or_insert(0) += 1;
                results.push(search_result);
                
                if results.len() >= args.max_results {
                    break;
                }
            }
        }
        
        progress.inc(1);
    }
    
    progress.finish_with_message("Search completed");
    
    let summary = SearchSummary {
        total_files_searched: files.len(),
        files_with_matches: results.len(),
        total_matches,
        search_duration_ms: start_time.elapsed().as_millis(),
        formats_found,
    };
    
    // Display results
    display_search_results(&results, &summary, &formatter)?;
    
    // Export results if requested
    if let Some(export_path) = args.export {
        export_search_results(&results, &summary, &export_path, &formatter)?;
    }
    
    Ok(())
}

enum SearchPattern {
    Literal(String),
    CaseInsensitive(String),
    Regex(Regex),
}

impl SearchPattern {
    fn is_match(&self, text: &str) -> bool {
        match self {
            SearchPattern::Literal(pattern) => text.contains(pattern),
            SearchPattern::CaseInsensitive(pattern) => text.to_lowercase().contains(pattern),
            SearchPattern::Regex(regex) => regex.is_match(text),
        }
    }
    
    fn find_matches(&self, text: &str) -> Vec<(usize, usize)> {
        match self {
            SearchPattern::Literal(pattern) => {
                text.match_indices(pattern)
                    .map(|(start, matched)| (start, start + matched.len()))
                    .collect()
            }
            SearchPattern::CaseInsensitive(pattern) => {
                text.to_lowercase()
                    .match_indices(pattern)
                    .map(|(start, matched)| (start, start + matched.len()))
                    .collect()
            }
            SearchPattern::Regex(regex) => {
                regex.find_iter(text)
                    .map(|m| (m.start(), m.end()))
                    .collect()
            }
        }
    }
}

fn collect_files(
    directory: &Path,
    patterns: &[String],
    recursive: bool,
    include_hidden: bool,
) -> Result<Vec<PathBuf>, CliError> {
    let mut files = Vec::new();
    
    let walker = if recursive {
        WalkDir::new(directory)
    } else {
        WalkDir::new(directory).max_depth(1)
    };
    
    for entry in walker {
        let entry = entry.map_err(|e| CliError::Io(e.into()))?;
        let path = entry.path();
        
        if path.is_file() {
            // Check if hidden file
            if !include_hidden {
                if let Some(name) = path.file_name() {
                    if name.to_string_lossy().starts_with('.') {
                        continue;
                    }
                }
            }
            
            // Check if matches patterns
            if patterns.is_empty() || matches_patterns(path, patterns) {
                files.push(path.to_path_buf());
            }
        }
    }
    
    Ok(files)
}

fn matches_patterns(path: &Path, patterns: &[String]) -> bool {
    let file_name = path.file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("");
    
    for pattern in patterns {
        if pattern.contains('*') || pattern.contains('?') {
            // Simple glob matching
            let regex_pattern = pattern
                .replace(".", r"\.")
                .replace("*", ".*")
                .replace("?", ".");
            
            if let Ok(regex) = Regex::new(&format!("^{}$", regex_pattern)) {
                if regex.is_match(file_name) {
                    return true;
                }
            }
        } else if file_name.contains(pattern) {
            return true;
        }
    }
    
    false
}

async fn search_file(
    file_path: &Path,
    pattern: &SearchPattern,
    search_content: bool,
    search_metadata: bool,
) -> Result<SearchResult, CliError> {
    let mut matches = Vec::new();
    let file_name = file_path.file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("")
        .to_string();
    
    // Check filename
    if pattern.is_match(&file_name) {
        matches.push(Match {
            line_number: None,
            column: None,
            context: file_name.clone(),
            match_type: MatchType::Filename,
        });
    }
    
    // Detect format
    let detector = FormatDetector::new();
    let format = if let Ok(content) = fs::read(file_path).await {
        detector.detect_from_bytes(&content, file_path.to_str())
            .map(|d| d.format.to_string())
            .unwrap_or_else(|| "unknown".to_string())
    } else {
        "unknown".to_string()
    };
    
    let mut metadata_info = None;
    
    // Search content and metadata
    if search_content || search_metadata {
        let extractor = ContentExtractor::new();
        
        if let Ok(extracted) = extractor.extract_from_file(file_path).await {
            // Search in content
            if search_content {
                let lines: Vec<&str> = extracted.text.lines().collect();
                for (line_num, line) in lines.iter().enumerate() {
                    if pattern.is_match(line) {
                        let line_matches = pattern.find_matches(line);
                        for (start, _) in line_matches {
                            // Get context (surrounding lines)
                            let context_start = line_num.saturating_sub(1);
                            let context_end = (line_num + 2).min(lines.len());
                            let context = lines[context_start..context_end].join("\n");
                            
                            matches.push(Match {
                                line_number: Some(line_num + 1),
                                column: Some(start + 1),
                                context,
                                match_type: MatchType::Content,
                            });
                        }
                    }
                }
            }
            
            // Search in metadata
            if search_metadata {
                let mut meta_matches = Vec::new();
                
                if let Some(title) = &extracted.metadata.title {
                    if pattern.is_match(title) {
                        meta_matches.push(("Title", title.clone()));
                    }
                }
                
                if let Some(author) = &extracted.metadata.author {
                    if pattern.is_match(author) {
                        meta_matches.push(("Author", author.clone()));
                    }
                }
                
                for keyword in &extracted.metadata.keywords {
                    if pattern.is_match(keyword) {
                        meta_matches.push(("Keyword", keyword.clone()));
                    }
                }
                
                for (field, value) in meta_matches {
                    matches.push(Match {
                        line_number: None,
                        column: None,
                        context: format!("{}: {}", field, value),
                        match_type: MatchType::Metadata,
                    });
                }
                
                metadata_info = Some(SearchMetadata {
                    title: extracted.metadata.title,
                    author: extracted.metadata.author,
                    creation_date: extracted.metadata.creation_date.map(|d| d.to_string()),
                    modification_date: extracted.metadata.modification_date.map(|d| d.to_string()),
                    keywords: extracted.metadata.keywords,
                });
            }
        }
    }
    
    Ok(SearchResult {
        file_path: file_path.display().to_string(),
        file_name,
        format,
        matches,
        metadata: metadata_info,
    })
}

fn display_search_results(
    results: &[SearchResult],
    summary: &SearchSummary,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    match formatter.format {
        OutputFormat::Json => {
            let output = serde_json::json!({
                "results": results,
                "summary": summary,
            });
            formatter.print_json(&output)?;
        }
        OutputFormat::Yaml => {
            let output = serde_json::json!({
                "results": results,
                "summary": summary,
            });
            formatter.print_yaml(&output)?;
        }
        _ => {
            formatter.print_header("Search Results");
            
            if results.is_empty() {
                formatter.print_info("No matches found");
            } else {
                for result in results {
                    formatter.print_info(&format!("\n📄 {}", result.file_path));
                    formatter.print_info(&format!("   Format: {}", result.format));
                    formatter.print_info(&format!("   Matches: {}", result.matches.len()));
                    
                    for (i, match_item) in result.matches.iter().enumerate() {
                        if i < 3 {  // Show first 3 matches
                            match match_item.match_type {
                                MatchType::Content => {
                                    if let Some(line) = match_item.line_number {
                                        formatter.print_info(&format!(
                                            "   • Line {}: {}",
                                            line,
                                            match_item.context.lines().next().unwrap_or("")
                                        ));
                                    }
                                }
                                MatchType::Metadata => {
                                    formatter.print_info(&format!("   • Metadata: {}", match_item.context));
                                }
                                MatchType::Filename => {
                                    formatter.print_info("   • Match in filename");
                                }
                            }
                        }
                    }
                    
                    if result.matches.len() > 3 {
                        formatter.print_info(&format!("   ... and {} more matches", result.matches.len() - 3));
                    }
                }
            }
            
            formatter.print_header("Search Summary");
            formatter.print_info(&format!("Files searched: {}", summary.total_files_searched));
            formatter.print_info(&format!("Files with matches: {}", summary.files_with_matches));
            formatter.print_info(&format!("Total matches: {}", summary.total_matches));
            formatter.print_info(&format!("Search time: {}ms", summary.search_duration_ms));
            
            if !summary.formats_found.is_empty() {
                formatter.print_info("\nFormats found:");
                for (format, count) in &summary.formats_found {
                    formatter.print_info(&format!("  • {}: {}", format, count));
                }
            }
        }
    }
    
    Ok(())
}

fn export_search_results(
    results: &[SearchResult],
    summary: &SearchSummary,
    export_path: &Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let export_data = serde_json::json!({
        "search_results": results,
        "summary": summary,
        "exported_at": chrono::Utc::now().to_rfc3339(),
    });
    
    let content = match export_path.extension().and_then(|s| s.to_str()) {
        Some("json") => serde_json::to_string_pretty(&export_data)
            .map_err(|e| CliError::Serialization(format!("Failed to serialize to JSON: {}", e)))?,
        Some("yaml") | Some("yml") => serde_yaml::to_string(&export_data)
            .map_err(|e| CliError::Serialization(format!("Failed to serialize to YAML: {}", e)))?,
        Some("csv") => {
            // Convert to CSV format
            let mut csv_content = String::from("File Path,File Name,Format,Match Type,Line Number,Context\n");
            for result in results {
                for match_item in &result.matches {
                    csv_content.push_str(&format!(
                        "{},{},{},{:?},{},\"{}\"\n",
                        result.file_path,
                        result.file_name,
                        result.format,
                        match_item.match_type,
                        match_item.line_number.unwrap_or(0),
                        match_item.context.replace("\"", "\"\"")
                    ));
                }
            }
            csv_content
        }
        _ => serde_json::to_string_pretty(&export_data)
            .map_err(|e| CliError::Serialization(format!("Failed to serialize: {}", e)))?,
    };
    
    std::fs::write(export_path, content)
        .map_err(|e| CliError::Io(e))?;
    
    formatter.print_success(&format!("Search results exported to {}", export_path.display()));
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_search_pattern_literal() {
        let pattern = SearchPattern::Literal("test".to_string());
        assert!(pattern.is_match("This is a test string"));
        assert!(!pattern.is_match("This is a string"));
    }
    
    #[test]
    fn test_search_pattern_case_insensitive() {
        let pattern = SearchPattern::CaseInsensitive("test".to_string());
        assert!(pattern.is_match("This is a TEST string"));
        assert!(pattern.is_match("This is a test string"));
        assert!(!pattern.is_match("This is a string"));
    }
    
    #[test]
    fn test_search_pattern_regex() {
        let pattern = SearchPattern::Regex(Regex::new(r"\d+").unwrap());
        assert!(pattern.is_match("Page 123"));
        assert!(!pattern.is_match("Page ABC"));
    }
    
    #[test]
    fn test_matches_patterns() {
        let path = Path::new("document.doc");
        assert!(matches_patterns(path, &vec!["*.doc".to_string()]));
        assert!(matches_patterns(path, &vec!["document.*".to_string()]));
        assert!(!matches_patterns(path, &vec!["*.pdf".to_string()]));
    }
}