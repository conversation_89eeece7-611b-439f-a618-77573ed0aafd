use axum::{
    body::Body,
    extract::Request,
    http::{header, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
};
use tower::{Layer, Service};
use std::future::Future;
use std::pin::Pin;
use std::task::{Context, Poll};
use tracing::warn;

/// API Key authentication layer
#[derive(Clone)]
pub struct ApiKeyLayer {
    api_key: String,
}

impl ApiKeyLayer {
    pub fn new(api_key: String) -> Self {
        Self { api_key }
    }
}

impl<S> Layer<S> for ApiKeyLayer {
    type Service = ApiKeyMiddleware<S>;

    fn layer(&self, inner: S) -> Self::Service {
        ApiKeyMiddleware {
            inner,
            api_key: self.api_key.clone(),
        }
    }
}

/// API Key middleware service
#[derive(Clone)]
pub struct ApiKeyMiddleware<S> {
    inner: S,
    api_key: String,
}

impl<S> Service<Request> for ApiKeyMiddleware<S>
where
    S: Service<Request, Response = Response> + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>> + Send>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request) -> Self::Future {
        let api_key = self.api_key.clone();
        
        // Extract information from request before moving it
        let path = request.uri().path().to_string();
        let auth_header = request
            .headers()
            .get("X-API-Key")
            .and_then(|h| h.to_str().ok())
            .map(|s| s.to_string());
        
        let future = self.inner.call(request);
        
        Box::pin(async move {
            // Skip authentication for health and info endpoints
            if path == "/health" || path == "/info" || path == "/docs" || path == "/metrics" {
                return future.await;
            }
            
            match auth_header {
                Some(key) if key == api_key => {
                    // Valid API key, proceed
                    future.await
                }
                Some(_) => {
                    // Invalid API key
                    warn!("Invalid API key provided");
                    Ok(unauthorized_response("Invalid API key"))
                }
                None => {
                    // Missing API key
                    warn!("Missing API key");
                    Ok(unauthorized_response("API key required"))
                }
            }
        })
    }
}

/// Create an unauthorized response
fn unauthorized_response(message: &str) -> Response {
    let body = serde_json::json!({
        "error": "unauthorized",
        "message": message,
        "request_id": uuid::Uuid::new_v4().to_string(),
    });
    
    (
        StatusCode::UNAUTHORIZED,
        [(header::CONTENT_TYPE, "application/json")],
        body.to_string(),
    ).into_response()
}

/// Simpler API key middleware function for use with axum's middleware::from_fn
pub async fn api_key_auth(
    req: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // This is a simplified version that would need the API key passed via state
    // For now, we'll use the Layer approach above
    Ok(next.run(req).await)
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{routing::get, Router};
    use tower::ServiceExt;
    
    #[tokio::test]
    async fn test_api_key_layer() {
        let app = Router::new()
            .route("/test", get(|| async { "OK" }))
            .layer(ApiKeyLayer::new("test-key".to_string()));
        
        // Test with valid API key
        let response = app.clone()
            .oneshot(
                Request::builder()
                    .uri("/test")
                    .header("X-API-Key", "test-key")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();
        
        assert_eq!(response.status(), StatusCode::OK);
        
        // Test with invalid API key
        let response = app.clone()
            .oneshot(
                Request::builder()
                    .uri("/test")
                    .header("X-API-Key", "wrong-key")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();
        
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
        
        // Test without API key
        let response = app
            .oneshot(
                Request::builder()
                    .uri("/test")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();
        
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }
}