use crate::cli::app::{ConfigCommands, ProfileCommands};
use crate::cli::config::manager::ConfigManager;
use crate::cli::commands::config::execute_config_command;
use crate::cli::output::OutputFormat;
use std::path::PathBuf;
use tempfile::TempDir;

#[tokio::test]
async fn test_config_show_command() {
    let result = execute_config_command(
        ConfigCommands::Show,
        OutputFormat::Json,
    ).await;
    
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_config_set_get_command() {
    // Set a value
    let set_result = execute_config_command(
        ConfigCommands::Set {
            key: "test.key".to_string(),
            value: "test_value".to_string(),
        },
        OutputFormat::Plain,
    ).await;
    assert!(set_result.is_ok());
    
    // Get the value
    let get_result = execute_config_command(
        ConfigCommands::Get {
            key: "test.key".to_string(),
        },
        OutputFormat::Plain,
    ).await;
    assert!(get_result.is_ok());
}

#[tokio::test]
async fn test_config_list_command() {
    let result = execute_config_command(
        ConfigCommands::List,
        OutputFormat::Table,
    ).await;
    
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_profile_create_and_switch() {
    // Create profile
    let create_result = execute_config_command(
        ConfigCommands::Profile {
            action: ProfileCommands::Create {
                name: "test_profile".to_string(),
                description: Some("Test profile".to_string()),
            },
        },
        OutputFormat::Plain,
    ).await;
    assert!(create_result.is_ok());
    
    // Switch to profile
    let switch_result = execute_config_command(
        ConfigCommands::Profile {
            action: ProfileCommands::Switch {
                name: "test_profile".to_string(),
            },
        },
        OutputFormat::Plain,
    ).await;
    assert!(switch_result.is_ok());
}

#[tokio::test]
async fn test_config_export_import() {
    let temp_dir = TempDir::new().unwrap();
    let export_path = temp_dir.path().join("config.yaml");
    
    // Export config
    let export_result = execute_config_command(
        ConfigCommands::Export {
            output: Some(export_path.display().to_string()),
        },
        OutputFormat::Plain,
    ).await;
    assert!(export_result.is_ok());
    
    // Import config
    let import_result = execute_config_command(
        ConfigCommands::Import {
            file: export_path.display().to_string(),
        },
        OutputFormat::Plain,
    ).await;
    assert!(import_result.is_ok());
}

#[test]
fn test_config_manager_creation() {
    let manager = ConfigManager::new();
    assert_eq!(manager.active_profile, "default");
    assert!(manager.profiles.contains_key("default"));
}

#[test]
fn test_config_manager_profile_operations() {
    let mut manager = ConfigManager::new();
    
    // Create profile
    let result = manager.create_profile("test", Some("Test profile"));
    assert!(result.is_ok());
    assert!(manager.profiles.contains_key("test"));
    
    // Switch profile
    let switch_result = manager.switch_profile("test");
    assert!(switch_result.is_ok());
    assert_eq!(manager.active_profile, "test");
    
    // Delete profile
    let delete_result = manager.delete_profile("test");
    assert!(delete_result.is_ok());
    assert!(!manager.profiles.contains_key("test"));
}

#[test]
fn test_config_manager_value_operations() {
    let mut manager = ConfigManager::new();
    
    // Set value
    let set_result = manager.set_value("test.key", "test_value");
    assert!(set_result.is_ok());
    
    // Get value
    let value = manager.get_value("test.key");
    assert_eq!(value, Some("test_value".to_string()));
    
    // Get with env override
    std::env::set_var("LEGACYBRIDGE_TEST_KEY", "env_value");
    let env_value = manager.get_value_with_env("test.key");
    assert_eq!(env_value, Some("env_value".to_string()));
    std::env::remove_var("LEGACYBRIDGE_TEST_KEY");
}

#[test]
fn test_config_manager_serialization() {
    let manager = ConfigManager::new();
    
    // To TOML
    let toml_result = manager.to_toml();
    assert!(toml_result.is_ok());
    
    // From TOML
    let toml_str = toml_result.unwrap();
    let from_toml_result = ConfigManager::from_toml(&toml_str);
    assert!(from_toml_result.is_ok());
    
    // To YAML
    let yaml_result = manager.to_yaml();
    assert!(yaml_result.is_ok());
    
    // From YAML
    let yaml_str = yaml_result.unwrap();
    let from_yaml_result = ConfigManager::from_yaml(&yaml_str);
    assert!(from_yaml_result.is_ok());
}