// MCP Server Module for LegacyBridge
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub mod server;
pub mod types;
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub mod client;
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub mod websocket;
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub mod job_tracker;
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub mod validation;
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub mod cache;
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub mod security;
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub mod integrations;
pub mod deployment;

// Official rust-mcp-sdk integration
#[cfg(feature = "mcp")]
pub mod official_server;

#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub use server::LegacyBridgeMcpServer;
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub use client::McpClient;
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub use websocket::{WebSocketServer, WebSocketConfig, ProgressUpdate, UpdateType};
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub use job_tracker::{JobTracker, JobProgressEvent, JobEventType, JobEventDetails};
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub use validation::{InputValidator, ValidationResult, ValidationError, ValidationWarning, ErrorRecovery, ErrorHandler};
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub use cache::{CacheManager, CacheConfig, CacheStats, PerformanceOptimizer};
#[cfg(any(feature = "tauri-app", feature = "cli", feature = "api"))]
pub use security::{SecurityManager, SecurityConfig, RateLimit, SecurityStats, SecurityMiddleware, ClientInfo};
pub use types::*;

// Re-export official server when MCP feature is enabled
#[cfg(feature = "mcp")]
pub use official_server::{LegacyBridgeMcpServerOfficial, OfficialMcpServer};