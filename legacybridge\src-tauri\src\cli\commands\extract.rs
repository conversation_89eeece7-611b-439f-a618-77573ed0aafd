use clap::Args;
use std::path::PathBuf;
use crate::cli::output::{OutputFormat, OutputFormatter};
use crate::cli::utils::{ContentExtractor, ExtractionOptions, ExtractedContent};
use serde::{Serialize, Deserialize};
use indicatif::{ProgressBar, ProgressStyle};
use tokio::task::JoinSet;

#[derive(Args, Debug)]
pub struct ExtractCommand {
    /// Input files or patterns to extract content from
    #[arg(required = true)]
    pub input: Vec<String>,
    
    /// Output format for extracted content
    #[arg(short, long, value_enum, default_value = "table")]
    pub output_format: OutputFormat,
    
    /// Include formatting in extracted text
    #[arg(short = 'f', long)]
    pub include_formatting: bool,
    
    /// Extract metadata only (no text content)
    #[arg(short = 'm', long)]
    pub metadata_only: bool,
    
    /// Maximum text length to extract (in characters)
    #[arg(short = 'l', long)]
    pub max_length: Option<usize>,
    
    /// Output directory for extracted content files
    #[arg(short = 'd', long)]
    pub output_dir: Option<PathBuf>,
    
    /// Process files in parallel
    #[arg(short = 'p', long)]
    pub parallel: bool,
    
    /// Number of parallel jobs (default: CPU count)
    #[arg(short = 'j', long)]
    pub jobs: Option<usize>,
    
    /// Verbose output
    #[arg(short, long)]
    pub verbose: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExtractionResult {
    pub file: String,
    pub status: String,
    pub format: Option<String>,
    pub word_count: Option<usize>,
    pub character_count: Option<usize>,
    pub line_count: Option<usize>,
    pub title: Option<String>,
    pub author: Option<String>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExtractionSummary {
    pub total_files: usize,
    pub successful: usize,
    pub failed: usize,
    pub total_words: usize,
    pub total_characters: usize,
    pub results: Vec<ExtractionResult>,
}

pub async fn execute(cmd: ExtractCommand) -> Result<(), Box<dyn std::error::Error>> {
    // Expand input patterns
    let files = crate::cli::utils::expand_patterns(&cmd.input)?;
    
    if files.is_empty() {
        return Err("No files found matching the input patterns".into());
    }
    
    if cmd.verbose {
        println!("Found {} files to process", files.len());
    }
    
    // Create output directory if specified
    if let Some(ref dir) = cmd.output_dir {
        tokio::fs::create_dir_all(dir).await?;
    }
    
    let extractor = ContentExtractor::new();
    let options = ExtractionOptions {
        include_formatting: cmd.include_formatting,
        extract_metadata: true,
        max_length: cmd.max_length,
        preserve_structure: true,
    };
    
    let results = if cmd.parallel {
        process_parallel(&files, &extractor, &options, &cmd).await?
    } else {
        process_sequential(&files, &extractor, &options, &cmd).await?
    };
    
    // Generate summary
    let summary = generate_summary(results);
    
    // Output results
    let formatter = OutputFormatter::new(cmd.output_format);
    formatter.print_extraction_summary(&summary)?;
    
    Ok(())
}

async fn process_sequential(
    files: &[PathBuf],
    extractor: &ContentExtractor,
    options: &ExtractionOptions,
    cmd: &ExtractCommand,
) -> Result<Vec<ExtractionResult>, Box<dyn std::error::Error>> {
    let pb = if !cmd.verbose {
        let pb = ProgressBar::new(files.len() as u64);
        pb.set_style(
            ProgressStyle::default_bar()
                .template("[{elapsed_precise}] {bar:40.cyan/blue} {pos}/{len} {msg}")?
                .progress_chars("=>-")
        );
        Some(pb)
    } else {
        None
    };
    
    let mut results = Vec::new();
    
    for file in files {
        if let Some(ref pb) = pb {
            pb.set_message(format!("Processing {}", file.display()));
        } else if cmd.verbose {
            println!("Processing: {}", file.display());
        }
        
        let result = process_file(file, extractor, options, cmd).await;
        results.push(result);
        
        if let Some(ref pb) = pb {
            pb.inc(1);
        }
    }
    
    if let Some(pb) = pb {
        pb.finish_with_message("Extraction complete");
    }
    
    Ok(results)
}

async fn process_parallel(
    files: &[PathBuf],
    extractor: &ContentExtractor,
    options: &ExtractionOptions,
    cmd: &ExtractCommand,
) -> Result<Vec<ExtractionResult>, Box<dyn std::error::Error>> {
    let max_jobs = cmd.jobs.unwrap_or_else(num_cpus::get);
    let semaphore = std::sync::Arc::new(tokio::sync::Semaphore::new(max_jobs));
    
    let pb = if !cmd.verbose {
        let pb = ProgressBar::new(files.len() as u64);
        pb.set_style(
            ProgressStyle::default_bar()
                .template("[{elapsed_precise}] {bar:40.cyan/blue} {pos}/{len} {msg}")?
                .progress_chars("=>-")
        );
        Some(pb)
    } else {
        None
    };
    
    let mut tasks = JoinSet::new();
    
    for file in files {
        let file = file.clone();
        let permit = semaphore.clone().acquire_owned().await?;
        let extractor_clone = ContentExtractor::new();
        let options_clone = options.clone();
        let cmd_clone = ExtractCommand {
            input: cmd.input.clone(),
            output_format: cmd.output_format,
            include_formatting: cmd.include_formatting,
            metadata_only: cmd.metadata_only,
            max_length: cmd.max_length,
            output_dir: cmd.output_dir.clone(),
            parallel: cmd.parallel,
            jobs: cmd.jobs,
            verbose: cmd.verbose,
        };
        let pb_clone = pb.clone();
        
        tasks.spawn(async move {
            let _permit = permit;
            
            if let Some(ref pb) = pb_clone {
                pb.set_message(format!("Processing {}", file.display()));
            }
            
            let result = process_file(&file, &extractor_clone, &options_clone, &cmd_clone).await;
            
            if let Some(ref pb) = pb_clone {
                pb.inc(1);
            }
            
            result
        });
    }
    
    let mut results = Vec::new();
    while let Some(result) = tasks.join_next().await {
        results.push(result?);
    }
    
    if let Some(pb) = pb {
        pb.finish_with_message("Extraction complete");
    }
    
    Ok(results)
}

async fn process_file(
    file: &PathBuf,
    extractor: &ContentExtractor,
    options: &ExtractionOptions,
    cmd: &ExtractCommand,
) -> ExtractionResult {
    match extractor.extract_from_file(file, options).await {
        Ok(content) => {
            // Save extracted content if output directory specified
            if let Some(ref output_dir) = cmd.output_dir {
                let _ = save_extracted_content(file, &content, output_dir, cmd.metadata_only).await;
            }
            
            ExtractionResult {
                file: file.display().to_string(),
                status: "success".to_string(),
                format: Some(content.format.name),
                word_count: Some(content.metadata.word_count),
                character_count: Some(content.metadata.character_count),
                line_count: Some(content.metadata.line_count),
                title: content.metadata.title,
                author: content.metadata.author,
                error: None,
            }
        }
        Err(e) => ExtractionResult {
            file: file.display().to_string(),
            status: "failed".to_string(),
            format: None,
            word_count: None,
            character_count: None,
            line_count: None,
            title: None,
            author: None,
            error: Some(e.to_string()),
        }
    }
}

async fn save_extracted_content(
    original_file: &PathBuf,
    content: &ExtractedContent,
    output_dir: &PathBuf,
    metadata_only: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    let file_stem = original_file.file_stem()
        .and_then(|s| s.to_str())
        .unwrap_or("extracted");
    
    if metadata_only {
        // Save metadata as JSON
        let metadata_file = output_dir.join(format!("{}_metadata.json", file_stem));
        let json = serde_json::to_string_pretty(&content.metadata)?;
        tokio::fs::write(metadata_file, json).await?;
    } else {
        // Save extracted text
        let text_file = output_dir.join(format!("{}_extracted.txt", file_stem));
        tokio::fs::write(text_file, &content.text).await?;
        
        // Also save metadata
        let metadata_file = output_dir.join(format!("{}_metadata.json", file_stem));
        let json = serde_json::to_string_pretty(&content.metadata)?;
        tokio::fs::write(metadata_file, json).await?;
    }
    
    Ok(())
}

fn generate_summary(results: Vec<ExtractionResult>) -> ExtractionSummary {
    let total_files = results.len();
    let successful = results.iter().filter(|r| r.status == "success").count();
    let failed = total_files - successful;
    
    let total_words: usize = results.iter()
        .filter_map(|r| r.word_count)
        .sum();
    
    let total_characters: usize = results.iter()
        .filter_map(|r| r.character_count)
        .sum();
    
    ExtractionSummary {
        total_files,
        successful,
        failed,
        total_words,
        total_characters,
        results,
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::io::Write;
    
    #[tokio::test]
    async fn test_extract_command() {
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test.rtf");
        
        let rtf_content = r"{\rtf1\ansi\deff0 {\fonttbl{\f0 Times New Roman;}} \f0\fs24 Test Document\par}";
        let mut file = std::fs::File::create(&file_path).unwrap();
        file.write_all(rtf_content.as_bytes()).unwrap();
        
        let cmd = ExtractCommand {
            input: vec![file_path.display().to_string()],
            output_format: OutputFormat::Json,
            include_formatting: false,
            metadata_only: false,
            max_length: None,
            output_dir: None,
            parallel: false,
            jobs: None,
            verbose: false,
        };
        
        let result = execute(cmd).await;
        assert!(result.is_ok());
    }
}