import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { logger, startTimer } from './error-logger-client';

// Types for conversion results and errors
export interface ConversionResult {
  success: boolean;
  content?: string;
  error?: string;
  conversionTime?: number;
  metadata?: {
    originalFormat: string;
    convertedFormat: string;
    timestamp: number;
  };
}

export interface ConversionError {
  code: string;
  message: string;
  details?: string;
}

export interface FileInfo {
  name: string;
  path: string;
  size: number;
  type: 'rtf' | 'md';
}

// Pipeline types
export interface PipelineConfig {
  strict_validation?: boolean;
  auto_recovery?: boolean;
  template?: string;
  preserve_formatting?: boolean;
  legacy_mode?: boolean;
}

export interface ValidationResult {
  level: string;
  code: string;
  message: string;
  location?: string;
}

export interface RecoveryAction {
  action_type: string;
  description: string;
  applied: boolean;
}

export interface PipelineConversionResult {
  success: boolean;
  markdown?: string;
  validation_results?: ValidationResult[];
  recovery_actions?: RecoveryAction[];
  error?: string;
}

// Streaming types
export interface StreamUpdateData {
  progress?: number;
  content?: string;
  validation?: ValidationResult[];
  error?: ConversionError;
  metadata?: Record<string, unknown>;
}

export interface StreamUpdate {
  type: 'progress' | 'partial' | 'validation' | 'complete' | 'error';
  data: StreamUpdateData;
  timestamp: number;
}

// Tauri command invocations
export const tauriApi = {
  // Convert RTF to Markdown
  async convertRtfToMarkdown(filePath: string): Promise<ConversionResult> {
    const endTimer = startTimer('convertRtfToMarkdown');
    logger.info('Conversion', 'Starting RTF to Markdown conversion', { filePath });
    
    try {
      const result = await invoke<string>('convert_rtf_to_markdown', {
        filePath
      });
      
      endTimer();
      logger.info('Conversion', 'RTF to Markdown conversion successful', { 
        filePath,
        resultLength: result.length 
      });
      
      return {
        success: true,
        content: result,
        metadata: {
          originalFormat: 'rtf',
          convertedFormat: 'md',
          timestamp: Date.now()
        }
      };
    } catch (error) {
      endTimer();
      logger.error('Conversion', 'RTF to Markdown conversion failed', error, { filePath });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  },

  // Convert Markdown to RTF
  async convertMarkdownToRtf(filePath: string): Promise<ConversionResult> {
    const endTimer = startTimer('convertMarkdownToRtf');
    logger.info('Conversion', 'Starting Markdown to RTF conversion', { filePath });
    
    try {
      const result = await invoke<string>('convert_markdown_to_rtf', {
        filePath
      });
      
      endTimer();
      logger.info('Conversion', 'Markdown to RTF conversion successful', { 
        filePath,
        resultLength: result.length 
      });
      
      return {
        success: true,
        content: result,
        metadata: {
          originalFormat: 'md',
          convertedFormat: 'rtf',
          timestamp: Date.now()
        }
      };
    } catch (error) {
      endTimer();
      logger.error('Conversion', 'Markdown to RTF conversion failed', error, { filePath });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  },

  // Batch convert multiple files
  async batchConvert(
    filePaths: string[],
    targetFormat: 'rtf' | 'md'
  ): Promise<ConversionResult[]> {
    const endTimer = startTimer('batchConvert');
    logger.info('Conversion', 'Starting batch conversion', { 
      fileCount: filePaths.length,
      targetFormat 
    });
    
    const results = await Promise.all(
      filePaths.map(path =>
        targetFormat === 'md'
          ? this.convertRtfToMarkdown(path)
          : this.convertMarkdownToRtf(path)
      )
    );
    
    endTimer();
    const successCount = results.filter(r => r.success).length;
    logger.info('Conversion', 'Batch conversion completed', { 
      totalFiles: filePaths.length,
      successCount,
      failureCount: filePaths.length - successCount,
      targetFormat
    });
    
    return results;
  },

  // Save converted file
  async saveConvertedFile(
    content: string,
    originalPath: string,
    format: 'rtf' | 'md'
  ): Promise<{ success: boolean; path?: string; error?: string }> {
    logger.info('FileOperation', 'Saving converted file', { 
      originalPath,
      format,
      contentLength: content.length 
    });
    
    try {
      const savedPath = await invoke<string>('save_converted_file', {
        content,
        originalPath,
        format
      });
      
      logger.info('FileOperation', 'File saved successfully', { 
        originalPath,
        savedPath,
        format 
      });
      
      return {
        success: true,
        path: savedPath
      };
    } catch (error) {
      logger.error('FileOperation', 'Failed to save file', error, { 
        originalPath,
        format 
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  },

  // Convert using pipeline with validation and recovery
  async convertWithPipeline(
    content: string,
    sourceType: 'rtf' | 'markdown',
    config?: PipelineConfig
  ): Promise<PipelineConversionResult> {
    const endTimer = startTimer('convertWithPipeline');
    logger.info('Pipeline', 'Starting pipeline conversion', { 
      sourceType,
      contentLength: content.length,
      config 
    });
    
    try {
      if (sourceType === 'rtf') {
        const result = await invoke<PipelineConversionResult>('rtf_to_markdown_pipeline', {
          rtfContent: content,
          config
        });
        
        endTimer();
        
        if (result.validation_results && result.validation_results.length > 0) {
          logger.warn('Pipeline', 'Validation issues found', { 
            issueCount: result.validation_results.length,
            issues: result.validation_results 
          });
        }
        
        if (result.recovery_actions && result.recovery_actions.length > 0) {
          logger.info('Pipeline', 'Recovery actions applied', { 
            actionCount: result.recovery_actions.length,
            actions: result.recovery_actions 
          });
        }
        
        return result;
      } else {
        // For now, markdown to RTF doesn't use pipeline
        const result = await invoke<string>('markdown_to_rtf', {
          markdownContent: content
        });
        
        endTimer();
        
        return {
          success: true,
          markdown: result,
          validation_results: [],
          recovery_actions: []
        };
      }
    } catch (error) {
      endTimer();
      logger.error('Pipeline', 'Pipeline conversion failed', error, { 
        sourceType,
        config 
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        validation_results: [],
        recovery_actions: []
      };
    }
  },

  // Convert file using pipeline
  async convertFileWithPipeline(
    filePath: string,
    config?: PipelineConfig
  ): Promise<PipelineConversionResult> {
    try {
      const result = await invoke<PipelineConversionResult>('read_rtf_file_pipeline', {
        filePath,
        config
      });
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        validation_results: [],
        recovery_actions: []
      };
    }
  },

  // Stream conversion updates for real-time preview
  async streamConversion(
    content: string,
    sourceType: 'rtf' | 'markdown',
    onUpdate: (update: StreamUpdate) => void
  ): Promise<() => void> {
    // Create a unique channel for this conversion
    const channelId = `conversion-${Date.now()}-${Math.random()}`;
    logger.info('Streaming', 'Starting stream conversion', { 
      channelId,
      sourceType,
      contentLength: content.length 
    });
    
    // Listen for updates
    const unlisten = await listen<StreamUpdate>(channelId, (event) => {
      if (event.payload.type === 'error') {
        logger.error('Streaming', 'Stream conversion error', event.payload.data.error, { 
          channelId 
        });
      } else if (event.payload.type === 'validation') {
        logger.warn('Streaming', 'Stream validation issues', { 
          channelId,
          issues: event.payload.data.validation 
        });
      }
      onUpdate(event.payload);
    });

    // Start the conversion
    invoke('stream_conversion', {
      content,
      sourceType,
      channelId
    }).catch(error => {
      logger.error('Streaming', 'Failed to start stream conversion', error, { 
        channelId,
        sourceType 
      });
      
      onUpdate({
        type: 'error',
        data: { error: error.message },
        timestamp: Date.now()
      });
    });

    // Return cleanup function
    return () => {
      logger.info('Streaming', 'Cleaning up stream conversion', { channelId });
      unlisten();
    };
  },

  // Get conversion statistics
  async getConversionStats(): Promise<{
    totalConversions: number;
    successfulConversions: number;
    failedConversions: number;
    averageProcessingTime: number;
  }> {
    try {
      const stats = await invoke<{
        totalConversions: number;
        successfulConversions: number;
        failedConversions: number;
        averageProcessingTime: number;
      }>('get_conversion_stats');
      return stats;
    } catch (error) {
      return {
        totalConversions: 0,
        successfulConversions: 0,
        failedConversions: 0,
        averageProcessingTime: 0
      };
    }
  },

  // Read file content directly
  async readFileContent(filePath: string): Promise<{ success: boolean; content?: string; error?: string }> {
    logger.info('FileOperation', 'Reading file content', { filePath });
    
    try {
      // Use the existing read_rtf_file command which returns content
      const response = await invoke<{ success: boolean; content?: string; error?: string }>('read_rtf_file', {
        filePath
      });
      
      if (response.success && response.content) {
        // Return the original RTF content, not the converted markdown
        const rtfContent = await invoke<{ success: boolean; content?: string; error?: string }>('read_file_base64', {
          filePath
        });
        
        if (rtfContent.success && rtfContent.content) {
          const content = atob(rtfContent.content);
          logger.info('FileOperation', 'File read successfully', { 
            filePath,
            contentLength: content.length 
          });
          return {
            success: true,
            content
          };
        }
      }
      
      // If RTF read fails, try reading as plain text via base64
      const base64Response = await invoke<{ success: boolean; content?: string; error?: string }>('read_file_base64', {
        filePath
      });
      
      if (base64Response.success && base64Response.content) {
        const content = atob(base64Response.content);
        logger.info('FileOperation', 'File read successfully via base64', { 
          filePath,
          contentLength: content.length 
        });
        return {
          success: true,
          content
        };
      } else {
        logger.error('FileOperation', 'Failed to read file', new Error(base64Response.error || 'Unknown error'), { 
          filePath 
        });
        return {
          success: false,
          error: base64Response.error || 'Failed to read file'
        };
      }
    } catch (error) {
      logger.error('FileOperation', 'Exception while reading file', error, { filePath });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to read file'
      };
    }
  }
};

// DLL Builder Types
export interface DllBuildRequest {
  architectures: string[];
  optimization: 'debug' | 'release' | 'size';
  includeDebugSymbols: boolean;
  staticLinking: boolean;
  includedFormats: string[];
  outputDirectory: string;
  buildMetadata: {
    version: string;
    company: string;
    description: string;
    copyright: string;
  };
  customOptions: Record<string, string>;
}

export interface DllBuildResult {
  dllPath: string;
  libPath?: string;
  headerPath?: string;
  architecture: string;
  size: number;
  exports: string[];
  buildTime: number;
}

export interface DllBuildProgress {
  stage: string;
  currentStep: string;
  progress: number;
  message: string;
}

export interface DllTestRequest {
  dllPath: string;
  testSuites: string[];
  platforms: string[];
  verbose: boolean;
}

export interface DllTestResult {
  testName: string;
  category: string;
  status: 'passed' | 'failed' | 'skipped';
  executionTime: number;
  message?: string;
  logs: string[];
}

export interface DllGenerateCodeRequest {
  dllPath: string;
  languages: string[];
  includeExamples: boolean;
  includeErrorHandling: boolean;
}

export interface DllPackageRequest {
  dllPaths: string[];
  format: string;
  includeDocs: boolean;
  includeExamples: boolean;
  includeSource: boolean;
  packageName: string;
  version: string;
  author: string;
  description: string;
}

export interface DllPackageResult {
  filePath: string;
  fileName: string;
  fileSize: number;
  format: string;
  includedFiles: string[];
  checksum: string;
}

export interface DllInspectionResult {
  dllPath: string;
  dllName: string;
  architecture: string;
  exports: Array<{
    name: string;
    ordinal?: number;
    address?: string;
    type?: string;
  }>;
  imports: Array<{
    dllName: string;
    functions: string[];
  }>;
  dependencies: Array<{
    name: string;
    version?: string;
    path?: string;
    found: boolean;
  }>;
  fileInfo: {
    size: number;
    created?: string;
    modified?: string;
    version?: string;
    company?: string;
    description?: string;
  };
  securityFeatures: {
    dep: boolean;
    aslr: boolean;
    safeSeh: boolean;
    cfg: boolean;
  };
}

export interface DllCommandResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface PerformanceBenchmark {
  name: string;
  iterations: number;
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  opsPerSecond: number;
  memoryUsage: {
    initialBytes: number;
    peakBytes: number;
    finalBytes: number;
    leakedBytes: number;
  };
}

export interface SecurityCheck {
  name: string;
  passed: boolean;
  severity: 'Critical' | 'High' | 'Medium' | 'Low' | 'Info';
  details: string;
  recommendation?: string;
}

export interface TestResult {
  testName: string;
  platform: string;
  passed: boolean;
  skipped?: boolean;
  duration: number;
  details?: string;
  error?: string;
  category?: 'compatibility' | 'performance' | 'integration' | 'security';
}

export interface PackageValidation {
  isValid: boolean;
  errors: string[];
}

export interface DeploymentPackage {
  fileName: string;
  filePath: string;
  fileSize: number;
  format: 'zip' | 'tar' | 'msi' | 'nsis';
  includedFiles: string[];
  downloadUrl: string;
  checksum: string;
  createdAt: string;
  metadata: {
    version: string;
    architecture: string[];
    includedFormats: string[];
  };
}

// DLL Builder API
export const dllApi = {
  async validateConfig(config: DllBuildRequest): Promise<DllCommandResponse<Record<string, boolean>>> {
    logger.info('DLL', 'Validating DLL configuration', { config });
    try {
      const result = await invoke<DllCommandResponse<Record<string, boolean>>>('dll_validate_config', { config });
      logger.info('DLL', 'Configuration validation complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Configuration validation failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Validation failed'
      };
    }
  },

  async build(config: DllBuildRequest, onProgress?: (progress: DllBuildProgress) => void): Promise<DllCommandResponse<DllBuildResult>> {
    logger.info('DLL', 'Starting DLL build', { config });
    
    // Set up progress listener if callback provided
    let unlisten: (() => void) | undefined;
    if (onProgress) {
      unlisten = await listen<DllBuildProgress>('dll-build-progress', (event) => {
        logger.debug('DLL', 'Build progress update', event.payload);
        onProgress(event.payload);
      });
    }

    try {
      const result = await invoke<DllCommandResponse<DllBuildResult>>('dll_build', { config });
      logger.info('DLL', 'DLL build complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'DLL build failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Build failed'
      };
    } finally {
      if (unlisten) {
        unlisten();
      }
    }
  },

  async test(request: DllTestRequest, onTestResult?: (result: DllTestResult) => void): Promise<DllCommandResponse<DllTestResult[]>> {
    logger.info('DLL', 'Starting DLL tests', { request });
    
    // Set up test result listener if callback provided
    let unlisten: (() => void) | undefined;
    if (onTestResult) {
      unlisten = await listen<DllTestResult>('dll-test-result', (event) => {
        logger.debug('DLL', 'Test result', event.payload);
        onTestResult(event.payload);
      });
    }

    try {
      const result = await invoke<DllCommandResponse<DllTestResult[]>>('dll_test', { request });
      logger.info('DLL', 'DLL tests complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'DLL tests failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Tests failed'
      };
    } finally {
      if (unlisten) {
        unlisten();
      }
    }
  },

  async generateCode(request: DllGenerateCodeRequest): Promise<DllCommandResponse<Record<string, string>>> {
    logger.info('DLL', 'Generating integration code', { request });
    try {
      const result = await invoke<DllCommandResponse<Record<string, string>>>('dll_generate_code', { request });
      logger.info('DLL', 'Code generation complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Code generation failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Code generation failed'
      };
    }
  },

  async createPackage(request: DllPackageRequest): Promise<DllCommandResponse<DllPackageResult>> {
    logger.info('DLL', 'Creating deployment package', { request });
    try {
      const result = await invoke<DllCommandResponse<DllPackageResult>>('dll_create_package', { request });
      logger.info('DLL', 'Package creation complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Package creation failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Package creation failed'
      };
    }
  },

  async inspect(dllPath: string): Promise<DllCommandResponse<DllInspectionResult>> {
    logger.info('DLL', 'Inspecting DLL', { dllPath });
    try {
      const result = await invoke<DllCommandResponse<DllInspectionResult>>('dll_inspect', { dllPath });
      logger.info('DLL', 'DLL inspection complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'DLL inspection failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Inspection failed'
      };
    }
  },

  async runPerformanceBenchmarks(dllPath: string, onProgress?: (progress: { stage: string; progress: number }) => void): Promise<DllCommandResponse<PerformanceBenchmark[]>> {
    logger.info('DLL', 'Running performance benchmarks', { dllPath });
    
    // Set up progress listener if callback provided
    let unlisten: (() => void) | undefined;
    if (onProgress) {
      unlisten = await listen<{ stage: string; progress: number }>('dll-performance-progress', (event) => {
        logger.debug('DLL', 'Performance benchmark progress', event.payload);
        onProgress(event.payload);
      });
    }

    try {
      const result = await invoke<DllCommandResponse<PerformanceBenchmark[]>>('dll_run_performance_benchmarks', { dllPath });
      logger.info('DLL', 'Performance benchmarks complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Performance benchmarks failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Performance benchmarks failed'
      };
    } finally {
      if (unlisten) {
        unlisten();
      }
    }
  },

  async runSecurityChecks(dllPath: string, onProgress?: (progress: { stage: string; progress: number }) => void): Promise<DllCommandResponse<SecurityCheck[]>> {
    logger.info('DLL', 'Running security checks', { dllPath });
    
    // Set up progress listener if callback provided
    let unlisten: (() => void) | undefined;
    if (onProgress) {
      unlisten = await listen<{ stage: string; progress: number }>('dll-security-progress', (event) => {
        logger.debug('DLL', 'Security check progress', event.payload);
        onProgress(event.payload);
      });
    }

    try {
      const result = await invoke<DllCommandResponse<SecurityCheck[]>>('dll_run_security_checks', { dllPath });
      logger.info('DLL', 'Security checks complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Security checks failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Security checks failed'
      };
    } finally {
      if (unlisten) {
        unlisten();
      }
    }
  },

  async validatePackaging(buildStatus: any, config: any): Promise<DllCommandResponse<PackageValidation>> {
    logger.info('DLL', 'Validating packaging configuration', { buildStatus, config });
    try {
      const result = await invoke<DllCommandResponse<PackageValidation>>('dll_validate_packaging', { 
        buildStatus,
        config 
      });
      logger.info('DLL', 'Packaging validation complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Packaging validation failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Packaging validation failed'
      };
    }
  },

  async createPackage(
    buildStatus: any, 
    testResults: TestResult[], 
    config: any,
    onProgress?: (progress: number) => void
  ): Promise<DllCommandResponse<DeploymentPackage>> {
    logger.info('DLL', 'Creating deployment package', { buildStatus, config });
    
    // Set up progress listener if callback provided
    let unlisten: (() => void) | undefined;
    if (onProgress) {
      unlisten = await listen<{ progress: number }>('packaging-progress', (event) => {
        logger.debug('DLL', 'Packaging progress', event.payload);
        onProgress(event.payload.progress);
      });
    }
    
    try {
      const result = await invoke<DllCommandResponse<DeploymentPackage>>('dll_create_package', { 
        buildStatus,
        testResults,
        config 
      });
      logger.info('DLL', 'Package creation complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Package creation failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Package creation failed'
      };
    } finally {
      if (unlisten) {
        unlisten();
      }
    }
  },

  async downloadPackage(packagePath: string, format: string): Promise<DllCommandResponse<void>> {
    logger.info('DLL', 'Downloading package', { packagePath, format });
    try {
      const result = await invoke<DllCommandResponse<void>>('dll_download_package', { 
        packagePath,
        format 
      });
      logger.info('DLL', 'Package download complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Package download failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Package download failed'
      };
    }
  },

  async getPackageContents(packagePath: string): Promise<DllCommandResponse<string[]>> {
    logger.info('DLL', 'Getting package contents', { packagePath });
    try {
      const result = await invoke<DllCommandResponse<string[]>>('dll_get_package_contents', { 
        packagePath 
      });
      logger.info('DLL', 'Package contents retrieved', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Failed to get package contents', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get package contents'
      };
    }
  },

  async verifyPackage(packagePath: string, expectedChecksum?: string): Promise<DllCommandResponse<{ isValid: boolean; actualChecksum: string }>> {
    logger.info('DLL', 'Verifying package', { packagePath, expectedChecksum });
    try {
      const result = await invoke<DllCommandResponse<{ isValid: boolean; actualChecksum: string }>>('dll_verify_package', { 
        packagePath,
        expectedChecksum 
      });
      logger.info('DLL', 'Package verification complete', { result });
      return result;
    } catch (error) {
      logger.error('DLL', 'Package verification failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Package verification failed'
      };
    }
  }
};