// Security Features and Rate Limiting for LegacyBridge MCP Server
// Implements authentication, authorization, and rate limiting

use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use serde::{Serialize, Deserialize};
use serde_json::{json, Value as JsonValue};
use sha2::{Sha256, Digest};
use uuid::Uuid;
use crate::mcp::types::IntegrationError;

/// Security manager for MCP server
pub struct SecurityManager {
    /// API key storage
    api_keys: Arc<RwLock<HashMap<String, ApiKey>>>,
    
    /// Rate limiter
    rate_limiter: Arc<RateLimiter>,
    
    /// Access control rules
    access_control: Arc<RwLock<AccessControl>>,
    
    /// Security configuration
    config: SecurityConfig,
    
    /// Audit logger
    audit_log: Arc<RwLock<Vec<AuditEntry>>>,
}

#[derive(Debug, Clone)]
pub struct SecurityConfig {
    /// Enable API key authentication
    pub enable_auth: bool,
    
    /// Enable rate limiting
    pub enable_rate_limiting: bool,
    
    /// Maximum request size in bytes
    pub max_request_size: usize,
    
    /// Allowed IP addresses (empty = all allowed)
    pub allowed_ips: Vec<String>,
    
    /// Blocked IP addresses
    pub blocked_ips: Vec<String>,
    
    /// Enable audit logging
    pub enable_audit_log: bool,
    
    /// Session timeout in seconds
    pub session_timeout: u64,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            enable_auth: false,
            enable_rate_limiting: true,
            max_request_size: 50 * 1024 * 1024, // 50MB
            allowed_ips: vec![],
            blocked_ips: vec![],
            enable_audit_log: true,
            session_timeout: 3600, // 1 hour
        }
    }
}

#[derive(Clone, Serialize, Deserialize)]
struct ApiKey {
    key_id: String,
    key_hash: String,
    name: String,
    permissions: Vec<Permission>,
    rate_limit: Option<RateLimit>,
    created_at: Instant,
    last_used: Option<Instant>,
    expires_at: Option<Instant>,
    active: bool,
}

#[derive(Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
enum Permission {
    ConvertFile,
    BatchConvert,
    DetectFormat,
    BuildDll,
    AdminAccess,
    ReadOnly,
    WriteOnly,
}

/// Rate limiter implementation
pub struct RateLimiter {
    /// Client rate limits
    limits: RwLock<HashMap<String, ClientRateLimit>>,
    
    /// Default rate limit
    default_limit: RateLimit,
}

#[derive(Clone, Copy, Serialize, Deserialize)]
pub struct RateLimit {
    /// Requests per minute
    pub requests_per_minute: u32,
    
    /// Requests per hour
    pub requests_per_hour: u32,
    
    /// Maximum burst size
    pub burst_size: u32,
    
    /// Maximum concurrent requests
    pub max_concurrent: u32,
}

impl Default for RateLimit {
    fn default() -> Self {
        Self {
            requests_per_minute: 60,
            requests_per_hour: 1000,
            burst_size: 10,
            max_concurrent: 5,
        }
    }
}

struct ClientRateLimit {
    client_id: String,
    limit: RateLimit,
    tokens: f64,
    last_refill: Instant,
    request_times: Vec<Instant>,
    concurrent_requests: u32,
}

/// Access control system
struct AccessControl {
    /// Tool-specific permissions
    tool_permissions: HashMap<String, Vec<Permission>>,
    
    /// Resource access rules
    resource_rules: HashMap<String, AccessRule>,
    
    /// IP-based rules
    ip_rules: Vec<IpRule>,
}

#[derive(Clone)]
struct AccessRule {
    resource_pattern: String,
    required_permissions: Vec<Permission>,
    rate_limit_override: Option<RateLimit>,
}

#[derive(Clone)]
struct IpRule {
    ip_pattern: String,
    action: IpAction,
    reason: String,
}

#[derive(Clone)]
enum IpAction {
    Allow,
    Block,
    RateLimit(RateLimit),
}

#[derive(Serialize, Deserialize)]
struct AuditEntry {
    timestamp: chrono::DateTime<chrono::Utc>,
    client_id: String,
    ip_address: Option<String>,
    action: String,
    resource: String,
    success: bool,
    error: Option<String>,
    metadata: Option<JsonValue>,
}

impl SecurityManager {
    /// Create a new security manager
    pub fn new(config: SecurityConfig) -> Self {
        let rate_limiter = Arc::new(RateLimiter {
            limits: RwLock::new(HashMap::new()),
            default_limit: RateLimit::default(),
        });
        
        let mut access_control = AccessControl {
            tool_permissions: HashMap::new(),
            resource_rules: HashMap::new(),
            ip_rules: vec![],
        };
        
        // Set up default tool permissions
        access_control.tool_permissions.insert("convert_file".to_string(), vec![
            Permission::ConvertFile,
            Permission::AdminAccess,
        ]);
        
        access_control.tool_permissions.insert("batch_convert".to_string(), vec![
            Permission::BatchConvert,
            Permission::AdminAccess,
        ]);
        
        access_control.tool_permissions.insert("build_dll".to_string(), vec![
            Permission::BuildDll,
            Permission::AdminAccess,
        ]);
        
        Self {
            api_keys: Arc::new(RwLock::new(HashMap::new())),
            rate_limiter,
            access_control: Arc::new(RwLock::new(access_control)),
            config,
            audit_log: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    /// Generate a new API key
    pub async fn generate_api_key(
        &self,
        name: String,
        permissions: Vec<Permission>,
        custom_limit: Option<RateLimit>,
        expires_in_days: Option<u64>,
    ) -> Result<String, IntegrationError> {
        let key = Uuid::new_v4().to_string();
        let key_id = Uuid::new_v4().to_string();
        
        // Hash the key for storage
        let mut hasher = Sha256::new();
        hasher.update(key.as_bytes());
        let key_hash = format!("{:x}", hasher.finalize());
        
        let expires_at = expires_in_days.map(|days| {
            Instant::now() + Duration::from_secs(days * 24 * 60 * 60)
        });
        
        let api_key = ApiKey {
            key_id: key_id.clone(),
            key_hash,
            name,
            permissions,
            rate_limit: custom_limit,
            created_at: Instant::now(),
            last_used: None,
            expires_at,
            active: true,
        };
        
        self.api_keys.write().await.insert(key_id, api_key);
        
        // Return the unhashed key (only shown once)
        Ok(key)
    }
    
    /// Validate API key
    pub async fn validate_api_key(&self, key: &str) -> Result<ApiKeyInfo, IntegrationError> {
        if !self.config.enable_auth {
            // Auth disabled, return default permissions
            return Ok(ApiKeyInfo {
                key_id: "anonymous".to_string(),
                permissions: vec![Permission::ConvertFile, Permission::DetectFormat],
                rate_limit: None,
            });
        }
        
        // Hash the provided key
        let mut hasher = Sha256::new();
        hasher.update(key.as_bytes());
        let key_hash = format!("{:x}", hasher.finalize());
        
        let mut api_keys = self.api_keys.write().await;
        
        // Find matching key
        for (_, api_key) in api_keys.iter_mut() {
            if api_key.key_hash == key_hash && api_key.active {
                // Check expiration
                if let Some(expires_at) = api_key.expires_at {
                    if Instant::now() > expires_at {
                        api_key.active = false;
                        return Err(IntegrationError::AuthenticationError("API key expired".to_string()));
                    }
                }
                
                // Update last used
                api_key.last_used = Some(Instant::now());
                
                return Ok(ApiKeyInfo {
                    key_id: api_key.key_id.clone(),
                    permissions: api_key.permissions.clone(),
                    rate_limit: api_key.rate_limit,
                });
            }
        }
        
        Err(IntegrationError::AuthenticationError("Invalid API key".to_string()))
    }
    
    /// Check rate limit
    pub async fn check_rate_limit(
        &self,
        client_id: &str,
        custom_limit: Option<RateLimit>,
    ) -> Result<(), IntegrationError> {
        if !self.config.enable_rate_limiting {
            return Ok(());
        }
        
        let mut limits = self.rate_limiter.limits.write().await;
        let now = Instant::now();
        
        let client_limit = limits.entry(client_id.to_string()).or_insert_with(|| {
            ClientRateLimit {
                client_id: client_id.to_string(),
                limit: custom_limit.unwrap_or(self.rate_limiter.default_limit),
                tokens: custom_limit.unwrap_or(self.rate_limiter.default_limit).burst_size as f64,
                last_refill: now,
                request_times: Vec::new(),
                concurrent_requests: 0,
            }
        });
        
        // Apply custom limit if provided
        if let Some(limit) = custom_limit {
            client_limit.limit = limit;
        }
        
        // Refill tokens
        let elapsed = now.duration_since(client_limit.last_refill).as_secs_f64();
        let refill_rate = client_limit.limit.requests_per_minute as f64 / 60.0;
        client_limit.tokens = (client_limit.tokens + elapsed * refill_rate)
            .min(client_limit.limit.burst_size as f64);
        client_limit.last_refill = now;
        
        // Check tokens
        if client_limit.tokens < 1.0 {
            return Err(IntegrationError::RateLimitError(
                "Rate limit exceeded. Please try again later.".to_string()
            ));
        }
        
        // Check concurrent requests
        if client_limit.concurrent_requests >= client_limit.limit.max_concurrent {
            return Err(IntegrationError::RateLimitError(
                "Maximum concurrent requests exceeded".to_string()
            ));
        }
        
        // Remove old request times
        let hour_ago = now - Duration::from_secs(3600);
        client_limit.request_times.retain(|&t| t > hour_ago);
        
        // Check hourly limit
        if client_limit.request_times.len() >= client_limit.limit.requests_per_hour as usize {
            return Err(IntegrationError::RateLimitError(
                "Hourly rate limit exceeded".to_string()
            ));
        }
        
        // Consume token
        client_limit.tokens -= 1.0;
        client_limit.request_times.push(now);
        client_limit.concurrent_requests += 1;
        
        Ok(())
    }
    
    /// Release concurrent request slot
    pub async fn release_request(&self, client_id: &str) {
        if let Some(client_limit) = self.rate_limiter.limits.write().await.get_mut(client_id) {
            client_limit.concurrent_requests = client_limit.concurrent_requests.saturating_sub(1);
        }
    }
    
    /// Check tool permission
    pub async fn check_permission(
        &self,
        tool_name: &str,
        permissions: &[Permission],
    ) -> Result<(), IntegrationError> {
        let access_control = self.access_control.read().await;
        
        if let Some(required_perms) = access_control.tool_permissions.get(tool_name) {
            // Check if user has any of the required permissions
            for required in required_perms {
                if permissions.contains(required) {
                    return Ok(());
                }
            }
            
            return Err(IntegrationError::AuthorizationError(
                format!("Insufficient permissions for tool: {}", tool_name)
            ));
        }
        
        // Tool not in permission list, allow by default
        Ok(())
    }
    
    /// Check IP address
    pub async fn check_ip(&self, ip: &str) -> Result<(), IntegrationError> {
        // Check blocked IPs
        if self.config.blocked_ips.contains(&ip.to_string()) {
            return Err(IntegrationError::SecurityError(
                "IP address is blocked".to_string()
            ));
        }
        
        // Check allowed IPs if whitelist is configured
        if !self.config.allowed_ips.is_empty() && 
           !self.config.allowed_ips.contains(&ip.to_string()) {
            return Err(IntegrationError::SecurityError(
                "IP address not in allowed list".to_string()
            ));
        }
        
        Ok(())
    }
    
    /// Log audit entry
    pub async fn log_audit(
        &self,
        client_id: String,
        ip_address: Option<String>,
        action: String,
        resource: String,
        success: bool,
        error: Option<String>,
        metadata: Option<JsonValue>,
    ) {
        if !self.config.enable_audit_log {
            return;
        }
        
        let entry = AuditEntry {
            timestamp: chrono::Utc::now(),
            client_id,
            ip_address,
            action,
            resource,
            success,
            error,
            metadata,
        };
        
        let mut audit_log = self.audit_log.write().await;
        audit_log.push(entry);
        
        // Limit audit log size
        if audit_log.len() > 10000 {
            audit_log.drain(0..5000);
        }
    }
    
    /// Get security statistics
    pub async fn get_stats(&self) -> SecurityStats {
        let api_keys = self.api_keys.read().await;
        let audit_log = self.audit_log.read().await;
        let rate_limits = self.rate_limiter.limits.read().await;
        
        let failed_attempts = audit_log.iter()
            .filter(|e| !e.success)
            .count();
        
        let rate_limit_hits = audit_log.iter()
            .filter(|e| e.error.as_ref().map_or(false, |err| err.contains("rate limit")))
            .count();
        
        SecurityStats {
            active_api_keys: api_keys.values().filter(|k| k.active).count(),
            total_api_keys: api_keys.len(),
            active_sessions: rate_limits.len(),
            failed_auth_attempts: failed_attempts,
            rate_limit_hits,
            audit_log_size: audit_log.len(),
        }
    }
}

#[derive(Debug)]
pub struct ApiKeyInfo {
    pub key_id: String,
    pub permissions: Vec<Permission>,
    pub rate_limit: Option<RateLimit>,
}

#[derive(Debug, Serialize)]
pub struct SecurityStats {
    pub active_api_keys: usize,
    pub total_api_keys: usize,
    pub active_sessions: usize,
    pub failed_auth_attempts: usize,
    pub rate_limit_hits: usize,
    pub audit_log_size: usize,
}

/// Security middleware for MCP requests
pub struct SecurityMiddleware {
    security_manager: Arc<SecurityManager>,
}

impl SecurityMiddleware {
    pub fn new(security_manager: Arc<SecurityManager>) -> Self {
        Self { security_manager }
    }
    
    /// Process request through security checks
    pub async fn process_request(
        &self,
        request: &JsonValue,
        client_info: ClientInfo,
    ) -> Result<ApiKeyInfo, IntegrationError> {
        // Check IP
        if let Some(ip) = &client_info.ip_address {
            self.security_manager.check_ip(ip).await?;
        }
        
        // Validate API key
        let api_key_info = if let Some(key) = &client_info.api_key {
            self.security_manager.validate_api_key(key).await?
        } else {
            ApiKeyInfo {
                key_id: client_info.client_id.clone(),
                permissions: vec![Permission::ReadOnly],
                rate_limit: None,
            }
        };
        
        // Check rate limit
        self.security_manager.check_rate_limit(
            &api_key_info.key_id,
            api_key_info.rate_limit,
        ).await?;
        
        // Check tool permission
        if let Some(method) = request["method"].as_str() {
            self.security_manager.check_permission(
                method,
                &api_key_info.permissions,
            ).await?;
        }
        
        Ok(api_key_info)
    }
    
    /// Complete request processing
    pub async fn complete_request(
        &self,
        client_id: &str,
        success: bool,
        error: Option<String>,
    ) {
        // Release rate limit slot
        self.security_manager.release_request(client_id).await;
        
        // Log audit entry if needed
        if !success || error.is_some() {
            self.security_manager.log_audit(
                client_id.to_string(),
                None,
                "request".to_string(),
                "mcp".to_string(),
                success,
                error,
                None,
            ).await;
        }
    }
}

#[derive(Debug, Clone)]
pub struct ClientInfo {
    pub client_id: String,
    pub api_key: Option<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

// Integration with MCP server
impl crate::mcp::server::LegacyBridgeMcpServer {
    /// Apply security middleware
    pub async fn apply_security(
        &self,
        request: &JsonValue,
        client_info: ClientInfo,
    ) -> Result<ApiKeyInfo, IntegrationError> {
        // Would need to add security_manager field to server struct
        // let middleware = SecurityMiddleware::new(self.security_manager.clone());
        // middleware.process_request(request, client_info).await
        
        Ok(ApiKeyInfo {
            key_id: "default".to_string(),
            permissions: vec![Permission::ConvertFile],
            rate_limit: None,
        })
    }
}