use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use crate::cli::CliError;

/// Predefined conversion templates for common use cases
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionTemplate {
    pub name: String,
    pub description: String,
    pub source_formats: Vec<String>,
    pub target_format: String,
    pub options: ConversionOptions,
}

/// Options that can be configured in a conversion template
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ConversionOptions {
    /// Quality level for conversion (0-100)
    pub quality: Option<u8>,
    /// Whether to preserve formatting
    pub preserve_formatting: bool,
    /// Whether to include metadata
    pub include_metadata: bool,
    /// Custom style mappings
    pub style_mappings: HashMap<String, String>,
    /// Security mode
    pub secure_mode: bool,
    /// Maximum file size in MB
    pub max_file_size_mb: Option<u64>,
    /// Custom conversion parameters
    pub custom_params: HashMap<String, serde_json::Value>,
}

/// Template manager for handling conversion presets
pub struct TemplateManager {
    templates: HashMap<String, ConversionTemplate>,
}

impl TemplateManager {
    /// Create a new template manager with default templates
    pub fn new() -> Self {
        let mut manager = Self {
            templates: HashMap::new(),
        };
        manager.load_default_templates();
        manager
    }
    
    /// Load default conversion templates
    fn load_default_templates(&mut self) {
        // Legacy Document Conversion Template
        self.add_template(ConversionTemplate {
            name: "legacy-to-modern".to_string(),
            description: "Convert legacy document formats to modern equivalents".to_string(),
            source_formats: vec!["doc".to_string(), "wpd".to_string(), "wps".to_string()],
            target_format: "md".to_string(),
            options: ConversionOptions {
                preserve_formatting: true,
                include_metadata: true,
                secure_mode: true,
                ..Default::default()
            },
        });
        
        // High Quality RTF Conversion
        self.add_template(ConversionTemplate {
            name: "rtf-high-quality".to_string(),
            description: "High-quality RTF to Markdown conversion with formatting preservation".to_string(),
            source_formats: vec!["rtf".to_string()],
            target_format: "md".to_string(),
            options: ConversionOptions {
                quality: Some(100),
                preserve_formatting: true,
                include_metadata: true,
                style_mappings: [
                    ("bold".to_string(), "**".to_string()),
                    ("italic".to_string(), "*".to_string()),
                    ("underline".to_string(), "_".to_string()),
                ].iter().cloned().collect(),
                ..Default::default()
            },
        });
        
        // Batch Processing Template
        self.add_template(ConversionTemplate {
            name: "batch-fast".to_string(),
            description: "Fast batch conversion with minimal formatting".to_string(),
            source_formats: vec!["rtf".to_string(), "doc".to_string(), "txt".to_string()],
            target_format: "md".to_string(),
            options: ConversionOptions {
                quality: Some(70),
                preserve_formatting: false,
                include_metadata: false,
                max_file_size_mb: Some(10),
                ..Default::default()
            },
        });
        
        // Archive Preservation Template
        self.add_template(ConversionTemplate {
            name: "archive-preservation".to_string(),
            description: "Preserve all content and metadata for archival purposes".to_string(),
            source_formats: vec!["rtf".to_string(), "doc".to_string(), "wpd".to_string()],
            target_format: "md".to_string(),
            options: ConversionOptions {
                quality: Some(100),
                preserve_formatting: true,
                include_metadata: true,
                secure_mode: true,
                custom_params: [
                    ("preserve_comments".to_string(), serde_json::json!(true)),
                    ("preserve_revisions".to_string(), serde_json::json!(true)),
                    ("embed_images".to_string(), serde_json::json!(true)),
                ].iter().cloned().collect(),
                ..Default::default()
            },
        });
        
        // Minimal Conversion Template
        self.add_template(ConversionTemplate {
            name: "minimal".to_string(),
            description: "Extract text content only, no formatting".to_string(),
            source_formats: vec!["rtf".to_string(), "doc".to_string(), "wpd".to_string(), "txt".to_string()],
            target_format: "txt".to_string(),
            options: ConversionOptions {
                quality: Some(50),
                preserve_formatting: false,
                include_metadata: false,
                ..Default::default()
            },
        });
    }
    
    /// Add a template to the manager
    pub fn add_template(&mut self, template: ConversionTemplate) {
        self.templates.insert(template.name.clone(), template);
    }
    
    /// Get a template by name
    pub fn get_template(&self, name: &str) -> Option<&ConversionTemplate> {
        self.templates.get(name)
    }
    
    /// List all available templates
    pub fn list_templates(&self) -> Vec<&ConversionTemplate> {
        self.templates.values().collect()
    }
    
    /// Get templates that support a specific source format
    pub fn templates_for_format(&self, format: &str) -> Vec<&ConversionTemplate> {
        self.templates
            .values()
            .filter(|t| t.source_formats.iter().any(|f| f.eq_ignore_ascii_case(format)))
            .collect()
    }
    
    /// Load templates from a JSON file
    pub fn load_from_file(&mut self, path: &Path) -> Result<(), CliError> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| CliError::FileReadError(path.to_path_buf(), e.to_string()))?;
        
        let templates: Vec<ConversionTemplate> = serde_json::from_str(&content)
            .map_err(|e| CliError::ConfigError(format!("Invalid template file: {}", e)))?;
        
        for template in templates {
            self.add_template(template);
        }
        
        Ok(())
    }
    
    /// Save templates to a JSON file
    pub fn save_to_file(&self, path: &Path) -> Result<(), CliError> {
        let templates: Vec<&ConversionTemplate> = self.templates.values().collect();
        let json = serde_json::to_string_pretty(&templates)
            .map_err(|e| CliError::ConfigError(format!("Failed to serialize templates: {}", e)))?;
        
        std::fs::write(path, json)
            .map_err(|e| CliError::FileWriteError(path.to_path_buf(), e.to_string()))?;
        
        Ok(())
    }
}

/// Builder for creating custom conversion templates
pub struct TemplateBuilder {
    template: ConversionTemplate,
}

impl TemplateBuilder {
    /// Create a new template builder
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            template: ConversionTemplate {
                name: name.into(),
                description: String::new(),
                source_formats: Vec::new(),
                target_format: String::new(),
                options: ConversionOptions::default(),
            },
        }
    }
    
    /// Set the template description
    pub fn description(mut self, desc: impl Into<String>) -> Self {
        self.template.description = desc.into();
        self
    }
    
    /// Add source formats
    pub fn source_formats(mut self, formats: Vec<String>) -> Self {
        self.template.source_formats = formats;
        self
    }
    
    /// Set the target format
    pub fn target_format(mut self, format: impl Into<String>) -> Self {
        self.template.target_format = format.into();
        self
    }
    
    /// Set conversion quality
    pub fn quality(mut self, quality: u8) -> Self {
        self.template.options.quality = Some(quality.min(100));
        self
    }
    
    /// Set formatting preservation
    pub fn preserve_formatting(mut self, preserve: bool) -> Self {
        self.template.options.preserve_formatting = preserve;
        self
    }
    
    /// Set metadata inclusion
    pub fn include_metadata(mut self, include: bool) -> Self {
        self.template.options.include_metadata = include;
        self
    }
    
    /// Set secure mode
    pub fn secure_mode(mut self, secure: bool) -> Self {
        self.template.options.secure_mode = secure;
        self
    }
    
    /// Add a custom parameter
    pub fn custom_param(mut self, key: impl Into<String>, value: serde_json::Value) -> Self {
        self.template.options.custom_params.insert(key.into(), value);
        self
    }
    
    /// Build the template
    pub fn build(self) -> ConversionTemplate {
        self.template
    }
}

/// Apply template options to a conversion
pub fn apply_template_options(
    template: &ConversionTemplate,
    _input_format: &str,
    _output_format: &str,
) -> ConversionOptions {
    template.options.clone()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_template_manager() {
        let manager = TemplateManager::new();
        
        // Check default templates are loaded
        assert!(manager.get_template("legacy-to-modern").is_some());
        assert!(manager.get_template("rtf-high-quality").is_some());
        assert!(manager.get_template("batch-fast").is_some());
        
        // Test listing templates
        let templates = manager.list_templates();
        assert!(templates.len() >= 5);
        
        // Test finding templates by format
        let rtf_templates = manager.templates_for_format("rtf");
        assert!(rtf_templates.len() >= 3);
    }
    
    #[test]
    fn test_template_builder() {
        let template = TemplateBuilder::new("custom-template")
            .description("A custom conversion template")
            .source_formats(vec!["doc".to_string(), "rtf".to_string()])
            .target_format("md")
            .quality(90)
            .preserve_formatting(true)
            .secure_mode(true)
            .custom_param("strip_images", serde_json::json!(false))
            .build();
        
        assert_eq!(template.name, "custom-template");
        assert_eq!(template.target_format, "md");
        assert_eq!(template.options.quality, Some(90));
        assert!(template.options.preserve_formatting);
        assert!(template.options.secure_mode);
        assert_eq!(
            template.options.custom_params.get("strip_images"),
            Some(&serde_json::json!(false))
        );
    }
    
    #[test]
    fn test_template_serialization() {
        let template = ConversionTemplate {
            name: "test".to_string(),
            description: "Test template".to_string(),
            source_formats: vec!["rtf".to_string()],
            target_format: "md".to_string(),
            options: ConversionOptions {
                quality: Some(80),
                preserve_formatting: true,
                ..Default::default()
            },
        };
        
        // Test serialization
        let json = serde_json::to_string(&template).unwrap();
        let deserialized: ConversionTemplate = serde_json::from_str(&json).unwrap();
        
        assert_eq!(deserialized.name, template.name);
        assert_eq!(deserialized.options.quality, template.options.quality);
    }
}