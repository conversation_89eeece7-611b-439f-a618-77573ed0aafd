use crate::cli::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::path::{Path, PathBuf};

/// Path utilities for intelligent output path generation and management
pub struct PathUtils;

impl PathUtils {
    /// Generate intelligent output path from input path and target format
    pub fn generate_output_path(
        input_path: &Path,
        output_format: &str,
        output_dir: Option<&Path>,
        custom_output: Option<&Path>
    ) -> CliResult<PathBuf> {
        // If custom output path is provided, use it
        if let Some(custom) = custom_output {
            return Ok(custom.to_path_buf());
        }
        
        // Get the base name without extension
        let input_stem = input_path.file_stem()
            .ok_or_else(|| CliError::InvalidPath(input_path.to_path_buf()))?
            .to_string_lossy();
        
        // Determine output extension
        let extension = Self::format_to_extension(output_format);
        
        // Generate filename
        let output_filename = format!("{}.{}", input_stem, extension);
        
        // Determine output directory
        let output_path = if let Some(dir) = output_dir {
            dir.join(output_filename)
        } else {
            // Use same directory as input by default
            let input_dir = input_path.parent()
                .unwrap_or_else(|| Path::new("."));
            input_dir.join(output_filename)
        };
        
        Ok(output_path)
    }
    
    /// Convert format string to file extension
    pub fn format_to_extension(format: &str) -> &str {
        match format.to_lowercase().as_str() {
            "md" | "markdown" => "md",
            "rtf" => "rtf", 
            "html" | "htm" => "html",
            "pdf" => "pdf",
            "txt" | "text" => "txt",
            "csv" => "csv",
            "json" => "json",
            "xml" => "xml",
            "yaml" | "yml" => "yaml",
            _ => "out",
        }
    }
    
    /// Validate and normalize path
    pub fn normalize_path(path: &Path, base_dir: Option<&Path>) -> CliResult<PathBuf> {
        let resolved_path = if path.is_absolute() {
            path.to_path_buf()
        } else if let Some(base) = base_dir {
            base.join(path)
        } else {
            std::env::current_dir()
                .map_err(|e| CliError::IoError(format!("Failed to get current directory: {}", e)))?
                .join(path)
        };
        
        // Canonicalize if path exists, otherwise just return normalized version
        if resolved_path.exists() {
            resolved_path.canonicalize()
                .map_err(|e| CliError::IoError(format!("Failed to canonicalize path: {}", e)))
        } else {
            Ok(resolved_path)
        }
    }
    
    /// Check if path is safe (no directory traversal attacks)
    pub fn is_safe_path(path: &Path, base_dir: &Path) -> bool {
        match path.canonicalize().and_then(|p| base_dir.canonicalize().map(|b| (p, b))) {
            Ok((canonical_path, canonical_base)) => canonical_path.starts_with(canonical_base),
            Err(_) => {
                // If canonicalization fails, do basic check
                !path.to_string_lossy().contains("..")
            }
        }
    }
    
    /// Generate unique filename if file already exists
    pub fn make_unique_path(path: &Path) -> PathBuf {
        if !path.exists() {
            return path.to_path_buf();
        }
        
        let parent = path.parent().unwrap_or_else(|| Path::new("."));
        let stem = path.file_stem()
            .map(|s| s.to_string_lossy())
            .unwrap_or_else(|| "file".into());
        let extension = path.extension()
            .map(|e| format!(".{}", e.to_string_lossy()))
            .unwrap_or_default();
        
        for i in 1..=9999 {
            let new_name = format!("{}_{}{})", stem, i, extension);
            let new_path = parent.join(new_name);
            if !new_path.exists() {
                return new_path;
            }
        }
        
        // Fallback with timestamp
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let new_name = format!("{}_{}{})", stem, timestamp, extension);
        parent.join(new_name)
    }
    
    /// Get relative path from base directory
    pub fn get_relative_path(path: &Path, base: &Path) -> CliResult<PathBuf> {
        path.strip_prefix(base)
            .map(|p| p.to_path_buf())
            .map_err(|_| CliError::InvalidPath(path.to_path_buf()))
    }
    
    /// Validate output directory and create if needed
    pub fn prepare_output_directory(path: &Path) -> CliResult<()> {
        if let Some(parent) = path.parent() {
            if !parent.exists() {
                std::fs::create_dir_all(parent)
                    .map_err(|e| CliError::IoError(format!("Failed to create output directory: {}", e)))?;
                println!("📁 Created output directory: {}", parent.display());
            }
        }
        Ok(())
    }
    
    /// Check if path has specific extension
    pub fn has_extension(path: &Path, ext: &str) -> bool {
        path.extension()
            .and_then(|e| e.to_str())
            .map(|e| e.eq_ignore_ascii_case(ext))
            .unwrap_or(false)
    }
    
    /// Get file size in human readable format
    pub fn format_file_size(size: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = size as f64;
        let mut unit_idx = 0;
        
        while size >= 1024.0 && unit_idx < UNITS.len() - 1 {
            size /= 1024.0;
            unit_idx += 1;
        }
        
        if unit_idx == 0 {
            format!("{} {}", size as u64, UNITS[unit_idx])
        } else {
            format!("{:.1} {}", size, UNITS[unit_idx])
        }
    }
}

/// Path validation result
#[derive(Debug, Clone)]
pub struct PathValidation {
    pub is_valid: bool,
    pub is_safe: bool,
    pub exists: bool,
    pub is_file: bool,
    pub is_directory: bool,
    pub is_readable: bool,
    pub is_writable: bool,
    pub size: Option<u64>,
}

impl PathUtils {
    /// Comprehensive path validation
    pub fn validate_path(path: &Path, base_dir: Option<&Path>) -> PathValidation {
        let exists = path.exists();
        let is_file = path.is_file();
        let is_directory = path.is_dir();
        
        let is_safe = if let Some(base) = base_dir {
            Self::is_safe_path(path, base)
        } else {
            !path.to_string_lossy().contains("..")
        };
        
        let size = if is_file {
            std::fs::metadata(path).ok().map(|m| m.len())
        } else {
            None
        };
        
        // Basic checks for readability/writability
        let is_readable = if exists {
            std::fs::File::open(path).is_ok()
        } else {
            true // Assume readable if doesn't exist yet
        };
        
        let is_writable = if exists {
            path.metadata()
                .map(|m| !m.permissions().readonly())
                .unwrap_or(false)
        } else {
            // Check if parent directory is writable
            path.parent()
                .map(|p| p.exists() && !p.metadata().map(|m| m.permissions().readonly()).unwrap_or(true))
                .unwrap_or(true)
        };
        
        PathValidation {
            is_valid: is_safe && (is_file || is_directory || !exists),
            is_safe,
            exists,
            is_file,
            is_directory,
            is_readable,
            is_writable,
            size,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    use std::fs;
    
    #[test]
    fn test_generate_output_path() {
        let input = Path::new("/test/input.rtf");
        let output = PathUtils::generate_output_path(input, "markdown", None, None).unwrap();
        assert_eq!(output, Path::new("/test/input.md"));
    }
    
    #[test]
    fn test_format_to_extension() {
        assert_eq!(PathUtils::format_to_extension("markdown"), "md");
        assert_eq!(PathUtils::format_to_extension("RTF"), "rtf");
        assert_eq!(PathUtils::format_to_extension("unknown"), "out");
    }
    
    #[test]
    fn test_unique_path_generation() {
        let temp_dir = tempdir().unwrap();
        let test_file = temp_dir.path().join("test.txt");
        
        // Create original file
        fs::write(&test_file, "content").unwrap();
        
        // Generate unique path
        let unique_path = PathUtils::make_unique_path(&test_file);
        assert_ne!(unique_path, test_file);
        assert!(!unique_path.exists());
    }
    
    #[test]
    fn test_path_validation() {
        let temp_dir = tempdir().unwrap();
        let test_file = temp_dir.path().join("test.txt");
        fs::write(&test_file, "content").unwrap();
        
        let validation = PathUtils::validate_path(&test_file, Some(temp_dir.path()));
        assert!(validation.is_valid);
        assert!(validation.is_safe);
        assert!(validation.exists);
        assert!(validation.is_file);
        assert!(!validation.is_directory);
    }
    
    #[test]
    fn test_unsafe_path_detection() {
        let base = Path::new("/safe/base");
        let unsafe_path = Path::new("/safe/base/../../../etc/passwd");
        assert!(!PathUtils::is_safe_path(unsafe_path, base));
    }
}