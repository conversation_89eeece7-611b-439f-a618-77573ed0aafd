// Analysis Report Module
// Provides comprehensive reporting for format detection and validation results

use std::collections::HashMap;
use std::fs;
use std::io::Write;
use std::path::Path;
use chrono::{DateTime, Utc};
use csv::Writer;
use serde::{Deserialize, Serialize};
use serde_json;

use crate::conversion::error::ConversionError;
use crate::cli::analysis::{FormatAnalysis, IntegrityStatus};

/// Analysis report containing multiple file analyses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisReport {
    pub report_id: String,
    pub timestamp: DateTime<Utc>,
    pub total_files: usize,
    pub analyses: Vec<FormatAnalysis>,
    pub summary: AnalysisSummary,
}

/// Summary statistics for the analysis report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisSummary {
    pub format_distribution: HashMap<String, usize>,
    pub integrity_summary: IntegritySummary,
    pub confidence_distribution: ConfidenceDistribution,
    pub total_size_bytes: u64,
    pub average_confidence: f32,
    pub processing_time_ms: u64,
}

/// Integrity summary across all files
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegritySummary {
    pub valid_files: usize,
    pub invalid_files: usize,
    pub common_issues: HashMap<String, usize>,
}

/// Confidence score distribution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfidenceDistribution {
    pub high_confidence: usize,      // > 0.8
    pub medium_confidence: usize,    // 0.5 - 0.8
    pub low_confidence: usize,       // < 0.5
}

impl AnalysisReport {
    /// Create a new analysis report
    pub fn new(analyses: Vec<FormatAnalysis>, processing_time_ms: u64) -> Self {
        let summary = Self::generate_summary(&analyses, processing_time_ms);
        
        Self {
            report_id: uuid::Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            total_files: analyses.len(),
            analyses,
            summary,
        }
    }

    /// Generate summary statistics
    fn generate_summary(analyses: &[FormatAnalysis], processing_time_ms: u64) -> AnalysisSummary {
        let mut format_distribution = HashMap::new();
        let mut total_size = 0u64;
        let mut total_confidence = 0.0f32;
        let mut valid_files = 0;
        let mut invalid_files = 0;
        let mut all_issues = HashMap::new();
        let mut high_confidence = 0;
        let mut medium_confidence = 0;
        let mut low_confidence = 0;

        for analysis in analyses {
            // Format distribution
            *format_distribution.entry(analysis.format_type.clone()).or_insert(0) += 1;
            
            // Size accumulation
            total_size += analysis.file_size;
            
            // Confidence accumulation
            total_confidence += analysis.confidence;
            
            // Confidence distribution
            if analysis.confidence > 0.8 {
                high_confidence += 1;
            } else if analysis.confidence >= 0.5 {
                medium_confidence += 1;
            } else {
                low_confidence += 1;
            }
            
            // Integrity tracking
            if analysis.integrity_status.is_valid {
                valid_files += 1;
            } else {
                invalid_files += 1;
                // Track issues
                for issue in &analysis.integrity_status.issues {
                    *all_issues.entry(issue.clone()).or_insert(0) += 1;
                }
            }
        }

        let average_confidence = if analyses.is_empty() {
            0.0
        } else {
            total_confidence / analyses.len() as f32
        };

        AnalysisSummary {
            format_distribution,
            integrity_summary: IntegritySummary {
                valid_files,
                invalid_files,
                common_issues: all_issues,
            },
            confidence_distribution: ConfidenceDistribution {
                high_confidence,
                medium_confidence,
                low_confidence,
            },
            total_size_bytes: total_size,
            average_confidence,
            processing_time_ms,
        }
    }

    /// Save report as JSON
    pub fn save_json(&self, path: &Path) -> Result<(), ConversionError> {
        let json = serde_json::to_string_pretty(self)
            .map_err(|e| ConversionError::SerializationError(e.to_string()))?;
        
        fs::write(path, json)
            .map_err(|e| ConversionError::IoError(e.to_string()))?;
        
        Ok(())
    }

    /// Save report as CSV
    pub fn save_csv(&self, path: &Path) -> Result<(), ConversionError> {
        let file = fs::File::create(path)
            .map_err(|e| ConversionError::IoError(e.to_string()))?;
        
        let mut writer = Writer::from_writer(file);
        
        // Write headers
        writer.write_record(&[
            "File Path",
            "Format Type",
            "Confidence",
            "Version",
            "File Size",
            "Is Valid",
            "Issues",
            "Readable %",
            "Suggested Formats",
            "Analysis Time",
        ]).map_err(|e| ConversionError::IoError(e.to_string()))?;
        
        // Write data rows
        for analysis in &self.analyses {
            let issues = analysis.integrity_status.issues.join("; ");
            let suggested = analysis.suggested_formats
                .iter()
                .map(|s| format!("{} ({:.2})", s.format, s.confidence))
                .collect::<Vec<_>>()
                .join(", ");
            
            writer.write_record(&[
                &analysis.file_path,
                &analysis.format_type,
                &format!("{:.2}", analysis.confidence),
                &analysis.version.as_ref().unwrap_or(&"N/A".to_string()),
                &analysis.file_size.to_string(),
                &analysis.integrity_status.is_valid.to_string(),
                &issues,
                &format!("{:.1}", analysis.integrity_status.readable_percentage),
                &suggested,
                &analysis.analysis_timestamp.to_rfc3339(),
            ]).map_err(|e| ConversionError::IoError(e.to_string()))?;
        }
        
        writer.flush()
            .map_err(|e| ConversionError::IoError(e.to_string()))?;
        
        Ok(())
    }

    /// Print detailed report to console
    pub fn print_detailed(&self) {
        println!("\n{}", "=".repeat(80));
        println!("📊 FORMAT ANALYSIS REPORT");
        println!("{}", "=".repeat(80));
        
        println!("\n📋 Report ID: {}", self.report_id);
        println!("🕐 Generated: {}", self.timestamp.format("%Y-%m-%d %H:%M:%S UTC"));
        println!("📁 Total Files: {}", self.total_files);
        println!("💾 Total Size: {}", Self::format_size(self.summary.total_size_bytes));
        println!("⏱️  Processing Time: {:.2}s", self.summary.processing_time_ms as f64 / 1000.0);
        
        // Format distribution
        println!("\n📊 Format Distribution:");
        let mut formats: Vec<_> = self.summary.format_distribution.iter().collect();
        formats.sort_by(|a, b| b.1.cmp(a.1));
        
        for (format, count) in formats {
            let percentage = (*count as f32 / self.total_files as f32) * 100.0;
            println!("   {} {}: {} ({:.1}%)", 
                Self::get_format_emoji(format), 
                format, 
                count, 
                percentage
            );
        }
        
        // Confidence distribution
        println!("\n🎯 Confidence Distribution:");
        println!("   ✅ High (>80%): {} files", self.summary.confidence_distribution.high_confidence);
        println!("   ⚠️  Medium (50-80%): {} files", self.summary.confidence_distribution.medium_confidence);
        println!("   ❌ Low (<50%): {} files", self.summary.confidence_distribution.low_confidence);
        println!("   📊 Average: {:.1}%", self.summary.average_confidence * 100.0);
        
        // Integrity summary
        println!("\n🔍 Integrity Summary:");
        println!("   ✅ Valid Files: {}", self.summary.integrity_summary.valid_files);
        println!("   ❌ Invalid Files: {}", self.summary.integrity_summary.invalid_files);
        
        if !self.summary.integrity_summary.common_issues.is_empty() {
            println!("\n   Common Issues:");
            let mut issues: Vec<_> = self.summary.integrity_summary.common_issues.iter().collect();
            issues.sort_by(|a, b| b.1.cmp(a.1));
            
            for (issue, count) in issues.iter().take(5) {
                println!("      • {} ({} files)", issue, count);
            }
        }
        
        // Detailed file analysis (first 10)
        if !self.analyses.is_empty() {
            println!("\n📄 Detailed Analysis (showing first 10 files):");
            println!("{}", "-".repeat(80));
            
            for (i, analysis) in self.analyses.iter().take(10).enumerate() {
                println!("\n{}. {}", i + 1, analysis.file_path);
                println!("   Format: {} (confidence: {:.1}%)", 
                    analysis.format_type, 
                    analysis.confidence * 100.0
                );
                println!("   Size: {}", Self::format_size(analysis.file_size));
                println!("   Valid: {}", if analysis.integrity_status.is_valid { "✅ Yes" } else { "❌ No" });
                
                if !analysis.integrity_status.issues.is_empty() {
                    println!("   Issues: {}", analysis.integrity_status.issues.join(", "));
                }
                
                if !analysis.suggested_formats.is_empty() {
                    println!("   Alternatives: {}", 
                        analysis.suggested_formats.iter()
                            .map(|s| format!("{} ({:.0}%)", s.format, s.confidence * 100.0))
                            .collect::<Vec<_>>()
                            .join(", ")
                    );
                }
            }
            
            if self.analyses.len() > 10 {
                println!("\n   ... and {} more files", self.analyses.len() - 10);
            }
        }
        
        println!("\n{}", "=".repeat(80));
    }

    /// Print summary report
    pub fn print_summary(&self) {
        println!("\n📊 Analysis Summary:");
        println!("   Files analyzed: {}", self.total_files);
        println!("   Valid files: {} ({:.1}%)", 
            self.summary.integrity_summary.valid_files,
            (self.summary.integrity_summary.valid_files as f32 / self.total_files as f32) * 100.0
        );
        println!("   Average confidence: {:.1}%", self.summary.average_confidence * 100.0);
        println!("   Processing time: {:.2}s", self.summary.processing_time_ms as f64 / 1000.0);
        
        // Most common format
        if let Some((format, count)) = self.summary.format_distribution.iter()
            .max_by_key(|&(_, count)| count) {
            println!("   Most common format: {} ({} files)", format, count);
        }
    }

    /// Format size in human-readable format
    fn format_size(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;
        
        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }
        
        format!("{:.2} {}", size, UNITS[unit_index])
    }

    /// Get emoji for format type
    fn get_format_emoji(format: &str) -> &'static str {
        match format {
            "Doc" => "📄",
            "WordPerfect" => "📝",
            "DBase" => "🗄️",
            "WordStar" => "✍️",
            "Lotus123" => "📊",
            _ => "📋",
        }
    }

    /// Export hex dump for a specific file
    pub fn export_hex_dump(&self, file_path: &str, output_path: &Path) -> Result<(), ConversionError> {
        let analysis = self.analyses.iter()
            .find(|a| a.file_path == file_path)
            .ok_or_else(|| ConversionError::InvalidInput(format!("File {} not found in report", file_path)))?;
        
        let mut output = String::new();
        output.push_str(&format!("Hex Dump: {}\n", file_path));
        output.push_str(&format!("Format: {} (confidence: {:.1}%)\n", analysis.format_type, analysis.confidence * 100.0));
        output.push_str(&format!("Size: {}\n", Self::format_size(analysis.file_size)));
        output.push_str(&format!("Generated: {}\n", analysis.analysis_timestamp.format("%Y-%m-%d %H:%M:%S UTC")));
        output.push_str(&format!("{}\n", "=".repeat(80)));
        output.push_str(&analysis.header_hex);
        
        fs::write(output_path, output)
            .map_err(|e| ConversionError::IoError(e.to_string()))?;
        
        Ok(())
    }
}