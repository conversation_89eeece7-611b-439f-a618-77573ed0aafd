use crate::cli::app::WorkflowCommands;
use crate::cli::output::{OutputFormat, OutputFormatter};
use crate::cli::CliError;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::process::Command;
use tokio::fs;
use indicatif::{ProgressBar, ProgressStyle};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Workflow {
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    pub author: Option<String>,
    pub parameters: Vec<WorkflowParameter>,
    pub environment: HashMap<String, String>,
    pub steps: Vec<WorkflowStep>,
    pub on_error: Option<ErrorHandler>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowParameter {
    pub name: String,
    pub description: Option<String>,
    #[serde(rename = "type")]
    pub param_type: String,
    pub default: Option<String>,
    pub required: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct WorkflowStep {
    pub name: String,
    pub description: Option<String>,
    #[serde(rename = "type")]
    pub step_type: StepType,
    pub condition: Option<String>,
    pub retry: Option<RetryConfig>,
    pub on_error: Option<ErrorHandler>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum StepType {
    #[serde(rename = "command")]
    Command {
        command: String,
        args: Vec<String>,
        working_dir: Option<String>,
    },
    #[serde(rename = "convert")]
    Convert {
        input: String,
        output: String,
        format: String,
        options: HashMap<String, String>,
    },
    #[serde(rename = "batch")]
    Batch {
        input_dir: String,
        output_dir: String,
        pattern: String,
        format: String,
    },
    #[serde(rename = "script")]
    Script {
        language: String,
        code: String,
    },
    #[serde(rename = "parallel")]
    Parallel {
        steps: Vec<WorkflowStep>,
        max_parallel: Option<usize>,
    },
    #[serde(rename = "foreach")]
    ForEach {
        items: String,
        variable: String,
        steps: Vec<WorkflowStep>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    pub max_attempts: u32,
    pub delay_seconds: u64,
    pub backoff_multiplier: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ErrorHandler {
    Continue,
    Stop,
    Retry,
    Skip,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkflowExecution {
    pub workflow_name: String,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub status: ExecutionStatus,
    pub steps_completed: usize,
    pub steps_total: usize,
    pub errors: Vec<String>,
    pub output: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ExecutionStatus {
    Running,
    Completed,
    Failed,
    Cancelled,
}

pub async fn execute_workflow_command(
    command: WorkflowCommands,
    output_format: OutputFormat,
) -> Result<(), CliError> {
    let formatter = OutputFormatter::new(output_format);
    let workflow_dir = get_workflow_directory()?;
    
    match command {
        WorkflowCommands::List => {
            list_workflows(&workflow_dir, &formatter).await?;
        }
        WorkflowCommands::Run { name, params, dry_run, continue_on_error, env } => {
            run_workflow(&name, params, dry_run, continue_on_error, env, &workflow_dir, &formatter).await?;
        }
        WorkflowCommands::Create { name, file, template, description } => {
            create_workflow(&name, file, template, description, &workflow_dir, &formatter).await?;
        }
        WorkflowCommands::Edit { name, editor } => {
            edit_workflow(&name, editor, &workflow_dir, &formatter).await?;
        }
        WorkflowCommands::Delete { name, force } => {
            delete_workflow(&name, force, &workflow_dir, &formatter).await?;
        }
        WorkflowCommands::Show { name, format } => {
            show_workflow(&name, &format, &workflow_dir, &formatter).await?;
        }
        WorkflowCommands::Validate { workflow } => {
            validate_workflow(&workflow, &workflow_dir, &formatter).await?;
        }
        WorkflowCommands::Export { name, output } => {
            export_workflow(&name, &output, &workflow_dir, &formatter).await?;
        }
        WorkflowCommands::Import { file, force } => {
            import_workflow(&file, force, &workflow_dir, &formatter).await?;
        }
    }
    
    Ok(())
}

fn get_workflow_directory() -> Result<PathBuf, CliError> {
    let config_dir = dirs::config_dir()
        .ok_or_else(|| CliError::Config("Unable to determine config directory".to_string()))?;
    let workflow_dir = config_dir.join("legacybridge").join("workflows");
    std::fs::create_dir_all(&workflow_dir)
        .map_err(|e| CliError::Io(e))?;
    Ok(workflow_dir)
}

async fn list_workflows(workflow_dir: &Path, formatter: &OutputFormatter) -> Result<(), CliError> {
    let mut workflows = Vec::new();
    
    let mut entries = fs::read_dir(workflow_dir).await
        .map_err(|e| CliError::Io(e))?;
    
    while let Some(entry) = entries.next_entry().await.map_err(|e| CliError::Io(e))? {
        let path = entry.path();
        if path.extension().and_then(|s| s.to_str()) == Some("yaml") ||
           path.extension().and_then(|s| s.to_str()) == Some("yml") ||
           path.extension().and_then(|s| s.to_str()) == Some("toml") {
            
            if let Ok(content) = fs::read_to_string(&path).await {
                if let Ok(workflow) = parse_workflow(&content, path.extension().and_then(|s| s.to_str()).unwrap_or("yaml")) {
                    workflows.push((
                        workflow.name.clone(),
                        workflow.description.clone().unwrap_or_default(),
                        workflow.version.clone(),
                        workflow.steps.len(),
                    ));
                }
            }
        }
    }
    
    match formatter.format {
        OutputFormat::Json => formatter.print_json(&workflows)?,
        OutputFormat::Yaml => formatter.print_yaml(&workflows)?,
        _ => {
            formatter.print_header("Available Workflows");
            if workflows.is_empty() {
                formatter.print_info("No workflows found. Create one with 'legacybridge workflow create'");
            } else {
                let headers = vec!["Name", "Description", "Version", "Steps"];
                let rows: Vec<Vec<String>> = workflows.into_iter()
                    .map(|(name, desc, version, steps)| {
                        vec![name, desc, version, steps.to_string()]
                    })
                    .collect();
                formatter.print_table(headers, rows);
            }
        }
    }
    
    Ok(())
}

async fn run_workflow(
    name: &str,
    params: Vec<String>,
    dry_run: bool,
    continue_on_error: bool,
    env: Vec<String>,
    workflow_dir: &Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    // Load workflow
    let workflow_path = find_workflow_file(name, workflow_dir)?;
    let content = fs::read_to_string(&workflow_path).await
        .map_err(|e| CliError::Io(e))?;
    
    let workflow = parse_workflow(&content, workflow_path.extension().and_then(|s| s.to_str()).unwrap_or("yaml"))?;
    
    // Parse parameters
    let mut param_map = HashMap::new();
    for param in params {
        let parts: Vec<&str> = param.splitn(2, '=').collect();
        if parts.len() == 2 {
            param_map.insert(parts[0].to_string(), parts[1].to_string());
        }
    }
    
    // Parse environment variables
    let mut env_map = workflow.environment.clone();
    for env_var in env {
        let parts: Vec<&str> = env_var.splitn(2, '=').collect();
        if parts.len() == 2 {
            env_map.insert(parts[0].to_string(), parts[1].to_string());
        }
    }
    
    // Validate required parameters
    for param in &workflow.parameters {
        if param.required && !param_map.contains_key(&param.name) && param.default.is_none() {
            return Err(CliError::Validation(format!("Required parameter '{}' not provided", param.name)));
        }
    }
    
    if dry_run {
        formatter.print_header("Workflow Dry Run");
        formatter.print_info(&format!("Workflow: {}", workflow.name));
        formatter.print_info(&format!("Version: {}", workflow.version));
        formatter.print_info(&format!("Steps: {}", workflow.steps.len()));
        formatter.print_info("\nSteps to execute:");
        for (i, step) in workflow.steps.iter().enumerate() {
            formatter.print_info(&format!("  {}. {}", i + 1, step.name));
            if let Some(desc) = &step.description {
                formatter.print_info(&format!("     {}", desc));
            }
        }
        return Ok(());
    }
    
    // Execute workflow
    let mut execution = WorkflowExecution {
        workflow_name: workflow.name.clone(),
        start_time: chrono::Utc::now(),
        end_time: None,
        status: ExecutionStatus::Running,
        steps_completed: 0,
        steps_total: workflow.steps.len(),
        errors: Vec::new(),
        output: HashMap::new(),
    };
    
    let progress = ProgressBar::new(workflow.steps.len() as u64);
    progress.set_style(
        ProgressStyle::default_bar()
            .template("{spinner:.green} [{bar:40.cyan/blue}] {pos}/{len} {msg}")
            .unwrap()
            .progress_chars("#>-")
    );
    
    for step in &workflow.steps {
        progress.set_message(format!("Executing: {}", step.name));
        
        match execute_step(step, &param_map, &env_map).await {
            Ok(output) => {
                if let Some(out) = output {
                    execution.output.insert(step.name.clone(), out);
                }
                execution.steps_completed += 1;
                progress.inc(1);
            }
            Err(e) => {
                execution.errors.push(format!("Step '{}' failed: {}", step.name, e));
                if !continue_on_error && step.on_error.as_ref().unwrap_or(&workflow.on_error.as_ref().unwrap_or(&ErrorHandler::Stop)) == &ErrorHandler::Stop {
                    execution.status = ExecutionStatus::Failed;
                    break;
                }
            }
        }
    }
    
    progress.finish_with_message("Workflow execution completed");
    
    if execution.errors.is_empty() {
        execution.status = ExecutionStatus::Completed;
    }
    execution.end_time = Some(chrono::Utc::now());
    
    // Display results
    formatter.print_header("Workflow Execution Summary");
    formatter.print_info(&format!("Workflow: {}", execution.workflow_name));
    formatter.print_info(&format!("Status: {:?}", execution.status));
    formatter.print_info(&format!("Steps: {}/{}", execution.steps_completed, execution.steps_total));
    
    if !execution.errors.is_empty() {
        formatter.print_error("\nErrors:");
        for error in &execution.errors {
            formatter.print_error(&format!("  • {}", error));
        }
    }
    
    if !execution.output.is_empty() {
        formatter.print_info("\nOutput:");
        for (key, value) in &execution.output {
            formatter.print_info(&format!("  {}: {}", key, value));
        }
    }
    
    Ok(())
}

async fn execute_step(
    step: &WorkflowStep,
    params: &HashMap<String, String>,
    env: &HashMap<String, String>,
) -> Result<Option<String>, CliError> {
    match &step.step_type {
        StepType::Command { command, args, working_dir } => {
            let expanded_command = expand_variables(command, params, env);
            let expanded_args: Vec<String> = args.iter()
                .map(|arg| expand_variables(arg, params, env))
                .collect();
            
            let mut cmd = Command::new(&expanded_command);
            cmd.args(&expanded_args);
            
            if let Some(dir) = working_dir {
                cmd.current_dir(expand_variables(dir, params, env));
            }
            
            for (key, value) in env {
                cmd.env(key, value);
            }
            
            let output = cmd.output()
                .map_err(|e| CliError::Command(format!("Failed to execute command: {}", e)))?;
            
            if !output.status.success() {
                return Err(CliError::Command(format!("Command failed with status: {}", output.status)));
            }
            
            Ok(Some(String::from_utf8_lossy(&output.stdout).to_string()))
        }
        StepType::Convert { input, output, format, options } => {
            // Implement conversion logic
            let expanded_input = expand_variables(input, params, env);
            let expanded_output = expand_variables(output, params, env);
            let expanded_format = expand_variables(format, params, env);
            
            // TODO: Call actual conversion logic
            Ok(Some(format!("Converted {} to {} (format: {})", expanded_input, expanded_output, expanded_format)))
        }
        StepType::Batch { input_dir, output_dir, pattern, format } => {
            // Implement batch processing logic
            let expanded_input = expand_variables(input_dir, params, env);
            let expanded_output = expand_variables(output_dir, params, env);
            let expanded_pattern = expand_variables(pattern, params, env);
            let expanded_format = expand_variables(format, params, env);
            
            // TODO: Call actual batch processing logic
            Ok(Some(format!("Batch processed {} to {} (pattern: {}, format: {})", 
                expanded_input, expanded_output, expanded_pattern, expanded_format)))
        }
        StepType::Script { language, code } => {
            // Execute script
            let expanded_code = expand_variables(code, params, env);
            
            match language.as_str() {
                "bash" | "sh" => {
                    let output = Command::new("sh")
                        .arg("-c")
                        .arg(&expanded_code)
                        .output()
                        .map_err(|e| CliError::Command(format!("Failed to execute script: {}", e)))?;
                    
                    if !output.status.success() {
                        return Err(CliError::Command(format!("Script failed with status: {}", output.status)));
                    }
                    
                    Ok(Some(String::from_utf8_lossy(&output.stdout).to_string()))
                }
                _ => Err(CliError::Command(format!("Unsupported script language: {}", language)))
            }
        }
        StepType::Parallel { steps, max_parallel } => {
            // TODO: Implement parallel execution
            Ok(Some(format!("Executed {} steps in parallel", steps.len())))
        }
        StepType::ForEach { items, variable, steps } => {
            // TODO: Implement foreach logic
            Ok(Some(format!("Executed {} steps for each item", steps.len())))
        }
    }
}

fn expand_variables(input: &str, params: &HashMap<String, String>, env: &HashMap<String, String>) -> String {
    let mut result = input.to_string();
    
    // Replace parameter variables
    for (key, value) in params {
        result = result.replace(&format!("${{{}}}", key), value);
        result = result.replace(&format!("${}", key), value);
    }
    
    // Replace environment variables
    for (key, value) in env {
        result = result.replace(&format!("${{{}}}", key), value);
        result = result.replace(&format!("${}", key), value);
    }
    
    result
}

async fn create_workflow(
    name: &str,
    file: Option<PathBuf>,
    template: Option<String>,
    description: Option<String>,
    workflow_dir: &Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let workflow_path = workflow_dir.join(format!("{}.yaml", name));
    
    if workflow_path.exists() {
        return Err(CliError::Validation(format!("Workflow '{}' already exists", name)));
    }
    
    let workflow = if let Some(file_path) = file {
        // Load from file
        let content = fs::read_to_string(&file_path).await
            .map_err(|e| CliError::Io(e))?;
        let mut workflow = parse_workflow(&content, file_path.extension().and_then(|s| s.to_str()).unwrap_or("yaml"))?;
        workflow.name = name.to_string();
        if let Some(desc) = description {
            workflow.description = Some(desc);
        }
        workflow
    } else if let Some(template_name) = template {
        // Create from template
        create_workflow_from_template(&template_name, name, description)?
    } else {
        // Create basic workflow
        Workflow {
            name: name.to_string(),
            description: description.or(Some("New workflow".to_string())),
            version: "1.0.0".to_string(),
            author: None,
            parameters: Vec::new(),
            environment: HashMap::new(),
            steps: vec![
                WorkflowStep {
                    name: "example-step".to_string(),
                    description: Some("Example step - replace with your logic".to_string()),
                    step_type: StepType::Command {
                        command: "echo".to_string(),
                        args: vec!["Hello from workflow!".to_string()],
                        working_dir: None,
                    },
                    condition: None,
                    retry: None,
                    on_error: None,
                }
            ],
            on_error: Some(ErrorHandler::Stop),
        }
    };
    
    let yaml = serde_yaml::to_string(&workflow)
        .map_err(|e| CliError::Serialization(format!("Failed to serialize workflow: {}", e)))?;
    
    fs::write(&workflow_path, yaml).await
        .map_err(|e| CliError::Io(e))?;
    
    formatter.print_success(&format!("Created workflow '{}' at {}", name, workflow_path.display()));
    
    Ok(())
}

async fn edit_workflow(
    name: &str,
    editor: Option<String>,
    workflow_dir: &Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let workflow_path = find_workflow_file(name, workflow_dir)?;
    
    let editor_cmd = editor.or_else(|| std::env::var("EDITOR").ok())
        .unwrap_or_else(|| "vi".to_string());
    
    let status = Command::new(&editor_cmd)
        .arg(&workflow_path)
        .status()
        .map_err(|e| CliError::Command(format!("Failed to launch editor: {}", e)))?;
    
    if !status.success() {
        return Err(CliError::Command("Editor exited with error".to_string()));
    }
    
    // Validate the edited workflow
    let content = fs::read_to_string(&workflow_path).await
        .map_err(|e| CliError::Io(e))?;
    
    parse_workflow(&content, workflow_path.extension().and_then(|s| s.to_str()).unwrap_or("yaml"))?;
    
    formatter.print_success(&format!("Workflow '{}' edited successfully", name));
    
    Ok(())
}

async fn delete_workflow(
    name: &str,
    force: bool,
    workflow_dir: &Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let workflow_path = find_workflow_file(name, workflow_dir)?;
    
    if !force {
        formatter.print_warning(&format!("Are you sure you want to delete workflow '{}'? Use --force to confirm.", name));
        return Ok(());
    }
    
    fs::remove_file(&workflow_path).await
        .map_err(|e| CliError::Io(e))?;
    
    formatter.print_success(&format!("Deleted workflow '{}'", name));
    
    Ok(())
}

async fn show_workflow(
    name: &str,
    format: &str,
    workflow_dir: &Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let workflow_path = find_workflow_file(name, workflow_dir)?;
    let content = fs::read_to_string(&workflow_path).await
        .map_err(|e| CliError::Io(e))?;
    
    let workflow = parse_workflow(&content, workflow_path.extension().and_then(|s| s.to_str()).unwrap_or("yaml"))?;
    
    match format {
        "json" => {
            let json = serde_json::to_string_pretty(&workflow)
                .map_err(|e| CliError::Serialization(format!("Failed to serialize workflow: {}", e)))?;
            println!("{}", json);
        }
        "yaml" | "yml" => {
            let yaml = serde_yaml::to_string(&workflow)
                .map_err(|e| CliError::Serialization(format!("Failed to serialize workflow: {}", e)))?;
            println!("{}", yaml);
        }
        _ => {
            formatter.print_header(&format!("Workflow: {}", workflow.name));
            if let Some(desc) = &workflow.description {
                formatter.print_info(&format!("Description: {}", desc));
            }
            formatter.print_info(&format!("Version: {}", workflow.version));
            if let Some(author) = &workflow.author {
                formatter.print_info(&format!("Author: {}", author));
            }
            
            if !workflow.parameters.is_empty() {
                formatter.print_info("\nParameters:");
                for param in &workflow.parameters {
                    let required = if param.required { " (required)" } else { "" };
                    let default = param.default.as_ref().map(|d| format!(" [default: {}]", d)).unwrap_or_default();
                    formatter.print_info(&format!("  • {}: {}{}{}", param.name, param.param_type, required, default));
                    if let Some(desc) = &param.description {
                        formatter.print_info(&format!("    {}", desc));
                    }
                }
            }
            
            formatter.print_info(&format!("\nSteps: {}", workflow.steps.len()));
            for (i, step) in workflow.steps.iter().enumerate() {
                formatter.print_info(&format!("  {}. {}", i + 1, step.name));
                if let Some(desc) = &step.description {
                    formatter.print_info(&format!("     {}", desc));
                }
            }
        }
    }
    
    Ok(())
}

async fn validate_workflow(
    workflow: &str,
    workflow_dir: &Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let workflow_path = if workflow.ends_with(".yaml") || workflow.ends_with(".yml") || workflow.ends_with(".toml") {
        PathBuf::from(workflow)
    } else {
        find_workflow_file(workflow, workflow_dir)?
    };
    
    let content = fs::read_to_string(&workflow_path).await
        .map_err(|e| CliError::Io(e))?;
    
    match parse_workflow(&content, workflow_path.extension().and_then(|s| s.to_str()).unwrap_or("yaml")) {
        Ok(workflow) => {
            formatter.print_success(&format!("Workflow '{}' is valid", workflow.name));
            
            // Additional validation checks
            let mut warnings = Vec::new();
            
            if workflow.steps.is_empty() {
                warnings.push("Workflow has no steps defined");
            }
            
            for step in &workflow.steps {
                if let StepType::Command { command, .. } = &step.step_type {
                    if command.is_empty() {
                        warnings.push("Step has empty command");
                    }
                }
            }
            
            if !warnings.is_empty() {
                formatter.print_warning("\nWarnings:");
                for warning in warnings {
                    formatter.print_warning(&format!("  • {}", warning));
                }
            }
        }
        Err(e) => {
            formatter.print_error(&format!("Workflow validation failed: {}", e));
            return Err(e);
        }
    }
    
    Ok(())
}

async fn export_workflow(
    name: &str,
    output: &Path,
    workflow_dir: &Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let workflow_path = find_workflow_file(name, workflow_dir)?;
    let content = fs::read_to_string(&workflow_path).await
        .map_err(|e| CliError::Io(e))?;
    
    fs::write(output, content).await
        .map_err(|e| CliError::Io(e))?;
    
    formatter.print_success(&format!("Exported workflow '{}' to {}", name, output.display()));
    
    Ok(())
}

async fn import_workflow(
    file: &Path,
    force: bool,
    workflow_dir: &Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let content = fs::read_to_string(file).await
        .map_err(|e| CliError::Io(e))?;
    
    let workflow = parse_workflow(&content, file.extension().and_then(|s| s.to_str()).unwrap_or("yaml"))?;
    
    let workflow_path = workflow_dir.join(format!("{}.yaml", workflow.name));
    
    if workflow_path.exists() && !force {
        return Err(CliError::Validation(format!("Workflow '{}' already exists. Use --force to overwrite.", workflow.name)));
    }
    
    let yaml = serde_yaml::to_string(&workflow)
        .map_err(|e| CliError::Serialization(format!("Failed to serialize workflow: {}", e)))?;
    
    fs::write(&workflow_path, yaml).await
        .map_err(|e| CliError::Io(e))?;
    
    formatter.print_success(&format!("Imported workflow '{}' from {}", workflow.name, file.display()));
    
    Ok(())
}

fn find_workflow_file(name: &str, workflow_dir: &Path) -> Result<PathBuf, CliError> {
    for ext in &["yaml", "yml", "toml"] {
        let path = workflow_dir.join(format!("{}.{}", name, ext));
        if path.exists() {
            return Ok(path);
        }
    }
    
    Err(CliError::NotFound(format!("Workflow '{}' not found", name)))
}

fn parse_workflow(content: &str, format: &str) -> Result<Workflow, CliError> {
    match format {
        "toml" => toml::from_str(content)
            .map_err(|e| CliError::Serialization(format!("Failed to parse TOML: {}", e))),
        _ => serde_yaml::from_str(content)
            .map_err(|e| CliError::Serialization(format!("Failed to parse YAML: {}", e))),
    }
}

fn create_workflow_from_template(template: &str, name: &str, description: Option<String>) -> Result<Workflow, CliError> {
    match template {
        "conversion" => Ok(Workflow {
            name: name.to_string(),
            description: description.or(Some("Document conversion workflow".to_string())),
            version: "1.0.0".to_string(),
            author: None,
            parameters: vec![
                WorkflowParameter {
                    name: "input_dir".to_string(),
                    description: Some("Input directory containing documents".to_string()),
                    param_type: "string".to_string(),
                    default: Some("./input".to_string()),
                    required: true,
                },
                WorkflowParameter {
                    name: "output_dir".to_string(),
                    description: Some("Output directory for converted documents".to_string()),
                    param_type: "string".to_string(),
                    default: Some("./output".to_string()),
                    required: true,
                },
                WorkflowParameter {
                    name: "format".to_string(),
                    description: Some("Target output format".to_string()),
                    param_type: "string".to_string(),
                    default: Some("md".to_string()),
                    required: true,
                },
            ],
            environment: HashMap::new(),
            steps: vec![
                WorkflowStep {
                    name: "create-output-dir".to_string(),
                    description: Some("Create output directory if it doesn't exist".to_string()),
                    step_type: StepType::Command {
                        command: "mkdir".to_string(),
                        args: vec!["-p".to_string(), "${output_dir}".to_string()],
                        working_dir: None,
                    },
                    condition: None,
                    retry: None,
                    on_error: None,
                },
                WorkflowStep {
                    name: "batch-convert".to_string(),
                    description: Some("Convert all documents in the input directory".to_string()),
                    step_type: StepType::Batch {
                        input_dir: "${input_dir}".to_string(),
                        output_dir: "${output_dir}".to_string(),
                        pattern: "*".to_string(),
                        format: "${format}".to_string(),
                    },
                    condition: None,
                    retry: Some(RetryConfig {
                        max_attempts: 3,
                        delay_seconds: 5,
                        backoff_multiplier: Some(2.0),
                    }),
                    on_error: Some(ErrorHandler::Continue),
                },
            ],
            on_error: Some(ErrorHandler::Stop),
        }),
        "backup" => Ok(Workflow {
            name: name.to_string(),
            description: description.or(Some("Backup and archive workflow".to_string())),
            version: "1.0.0".to_string(),
            author: None,
            parameters: vec![
                WorkflowParameter {
                    name: "source".to_string(),
                    description: Some("Source directory to backup".to_string()),
                    param_type: "string".to_string(),
                    default: None,
                    required: true,
                },
                WorkflowParameter {
                    name: "destination".to_string(),
                    description: Some("Backup destination".to_string()),
                    param_type: "string".to_string(),
                    default: None,
                    required: true,
                },
            ],
            environment: HashMap::new(),
            steps: vec![
                WorkflowStep {
                    name: "compress".to_string(),
                    description: Some("Compress source directory".to_string()),
                    step_type: StepType::Command {
                        command: "tar".to_string(),
                        args: vec![
                            "-czf".to_string(),
                            "${destination}/backup-$(date +%Y%m%d-%H%M%S).tar.gz".to_string(),
                            "-C".to_string(),
                            "${source}".to_string(),
                            ".".to_string(),
                        ],
                        working_dir: None,
                    },
                    condition: None,
                    retry: None,
                    on_error: None,
                },
            ],
            on_error: Some(ErrorHandler::Stop),
        }),
        _ => Err(CliError::Validation(format!("Unknown template: {}", template))),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_expand_variables() {
        let mut params = HashMap::new();
        params.insert("name".to_string(), "test".to_string());
        params.insert("version".to_string(), "1.0".to_string());
        
        let mut env = HashMap::new();
        env.insert("HOME".to_string(), "/home/<USER>".to_string());
        
        assert_eq!(expand_variables("Hello ${name}", &params, &env), "Hello test");
        assert_eq!(expand_variables("Version: ${version}", &params, &env), "Version: 1.0");
        assert_eq!(expand_variables("Path: ${HOME}/data", &params, &env), "Path: /home/<USER>/data");
        assert_eq!(expand_variables("Combined: ${name}-${version}", &params, &env), "Combined: test-1.0");
    }
    
    #[test]
    fn test_workflow_serialization() {
        let workflow = Workflow {
            name: "test".to_string(),
            description: Some("Test workflow".to_string()),
            version: "1.0.0".to_string(),
            author: Some("Test Author".to_string()),
            parameters: vec![],
            environment: HashMap::new(),
            steps: vec![],
            on_error: Some(ErrorHandler::Stop),
        };
        
        let yaml = serde_yaml::to_string(&workflow).unwrap();
        let parsed: Workflow = serde_yaml::from_str(&yaml).unwrap();
        
        assert_eq!(parsed.name, workflow.name);
        assert_eq!(parsed.description, workflow.description);
        assert_eq!(parsed.version, workflow.version);
    }
}