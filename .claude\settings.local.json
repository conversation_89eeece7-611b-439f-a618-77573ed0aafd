{"permissions": {"allow": ["Bash(npx:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(git clone:*)", "Bash(/home/<USER>/.local/bin/uv run python main.py --help)", "mcp__code-context__index_codebase", "WebFetch(domain:docs.anthropic.com)", "mcp__code-context__search_code"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["firebase", "fetch", "sequential-thinking", "puppeteer", "mcp-playwright", "everything", "memory", "memory-bank-mcp", "quick-data-mcp", "github-official", "mcp-filesystem", "desktop-commander", "netlify", "dart-mcp", "dart", "context7-mcp", "consult7", "brave-search", "taskmanager", "taskmaster-ai", "agentic-tools-claude", "n8n-mcp", "firecrawl", "perplexity-mcp", "deep-code-reasoning", "shadcn-ui", "nextjs-manager", "vibe-coder-mcp-wsl", "vibe-coder-mcp", "mcp-installer", "tailwind-svelte-assistant"], "disabledMcpjsonServers": ["vibe-coder-mcp-wsl", "zen", "dart-mcp", "gemini", "taskmanager", "dart"]}