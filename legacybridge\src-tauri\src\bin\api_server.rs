use legacybridge::api::{build_router, Config};
use tokio::net::TcpListener;
use tracing_subscriber;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();
    
    println!("Starting LegacyBridge API Server...");
    
    // Create server configuration
    let config = Config {
        api_key: Some("test-api-key".to_string()),
        max_body_size: 10 * 1024 * 1024, // 10MB
        enable_cors: true,
        cors_origins: vec![],
        enable_websocket: false,
        enable_upload: false,
        static_dir: None,
        enable_docs: true,
        enable_metrics: true,
        rate_limit: Some(60), // 60 requests per minute
        ssl_cert: None,
        ssl_key: None,
    };
    
    // Build the router (state is created inside build_router)
    let app = build_router(config)?;
    
    // Start the server
    let listener = TcpListener::bind("0.0.0.0:8080").await?;
    println!("API Server running on http://0.0.0.0:8080");
    println!("API Key: test-api-key");
    println!("Endpoints:");
    println!("  GET  /health - Health check");
    println!("  GET  /info - Server info");
    println!("  GET  /docs - API documentation");
    println!("  GET  /metrics - Metrics");
    println!("  GET  /api/v1/formats - List supported formats");
    println!("  POST /api/v1/convert - Convert files");
    println!("  POST /api/v1/detect - Detect file format");
    
    axum::serve(listener, app).await?;
    
    Ok(())
}