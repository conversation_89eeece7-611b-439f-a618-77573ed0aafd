use clap::Args;
use std::path::PathBuf;
use crate::cli::output::{OutputFormat, OutputFormatter};
use serde::{Serialize, Deserialize};
use indicatif::{ProgressBar, ProgressStyle};
use tokio::fs;
use flate2::write::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>der};
use flate2::Compression;
use zip::{ZipWriter, CompressionMethod};
use tar::Builder;
use std::io::Write;

#[derive(Args, Debug, Clone)]
pub struct CompressCommand {
    /// Input files or directories to compress
    #[arg(required = true)]
    pub input: Vec<String>,
    
    /// Output archive file
    #[arg(short, long, required = true)]
    pub output: PathBuf,
    
    /// Compression algorithm
    #[arg(short = 'a', long, value_enum, default_value = "zip")]
    pub algorithm: CompressionAlgorithm,
    
    /// Compression level (0-9, higher = better compression)
    #[arg(short = 'l', long, default_value = "6")]
    pub level: u32,
    
    /// Include hidden files
    #[arg(short = 'H', long)]
    pub hidden: bool,
    
    /// Follow symbolic links
    #[arg(short = 'L', long)]
    pub follow_links: bool,
    
    /// Exclude patterns
    #[arg(short = 'x', long)]
    pub exclude: Vec<String>,
    
    /// Store full paths (default: relative)
    #[arg(long)]
    pub full_paths: bool,
    
    /// Force overwrite if output exists
    #[arg(long)]
    pub force: bool,
    
    /// Output format for results
    #[arg(long, value_enum, default_value = "table")]
    pub output_format: OutputFormat,
    
    /// Verbose output
    #[arg(short, long)]
    pub verbose: bool,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, clap::ValueEnum, Serialize, Deserialize)]
pub enum CompressionAlgorithm {
    Zip,
    Gzip,
    Tar,
    TarGz,
    Zlib,
}

impl std::fmt::Display for CompressionAlgorithm {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Zip => write!(f, "ZIP"),
            Self::Gzip => write!(f, "GZIP"),
            Self::Tar => write!(f, "TAR"),
            Self::TarGz => write!(f, "TAR.GZ"),
            Self::Zlib => write!(f, "ZLIB"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CompressionResult {
    pub file: String,
    pub original_size: u64,
    pub compressed_size: Option<u64>,
    pub status: String,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CompressionSummary {
    pub algorithm: String,
    pub compression_level: u32,
    pub output_file: String,
    pub output_size: u64,
    pub total_files: usize,
    pub compressed: usize,
    pub failed: usize,
    pub total_original_size: u64,
    pub compression_ratio: f64,
    pub files: Vec<CompressionResult>,
}

pub async fn execute(cmd: CompressCommand) -> Result<(), Box<dyn std::error::Error>> {
    // Validate compression level
    if cmd.level > 9 {
        return Err("Compression level must be between 0 and 9".into());
    }
    
    // Check if output exists
    if cmd.output.exists() && !cmd.force {
        return Err(format!(
            "Output file '{}' already exists. Use --force to overwrite.",
            cmd.output.display()
        ).into());
    }
    
    // Create output directory if needed
    if let Some(parent) = cmd.output.parent() {
        fs::create_dir_all(parent).await?;
    }
    
    // Expand input patterns
    let mut all_files = Vec::new();
    for pattern in &cmd.input {
        let files = crate::cli::utils::expand_patterns(&[pattern.clone()])?;
        all_files.extend(files);
    }
    
    if all_files.is_empty() {
        return Err("No files found matching the input patterns".into());
    }
    
    if cmd.verbose {
        println!("Compressing {} files using {} algorithm", all_files.len(), cmd.algorithm);
    }
    
    // Perform compression
    let results = match cmd.algorithm {
        CompressionAlgorithm::Zip => compress_zip(&all_files, &cmd).await?,
        CompressionAlgorithm::Gzip => compress_gzip(&all_files, &cmd).await?,
        CompressionAlgorithm::Tar => compress_tar(&all_files, &cmd, false).await?,
        CompressionAlgorithm::TarGz => compress_tar(&all_files, &cmd, true).await?,
        CompressionAlgorithm::Zlib => compress_zlib(&all_files, &cmd).await?,
    };
    
    // Get output file size
    let output_size = fs::metadata(&cmd.output).await?.len();
    
    // Calculate summary statistics
    let compressed = results.iter().filter(|r| r.status == "success").count();
    let failed = results.len() - compressed;
    let total_original_size: u64 = results.iter()
        .map(|r| r.original_size)
        .sum();
    
    let compression_ratio = if total_original_size > 0 {
        (output_size as f64) / (total_original_size as f64)
    } else {
        1.0
    };
    
    let summary = CompressionSummary {
        algorithm: cmd.algorithm.to_string(),
        compression_level: cmd.level,
        output_file: cmd.output.display().to_string(),
        output_size,
        total_files: all_files.len(),
        compressed,
        failed,
        total_original_size,
        compression_ratio,
        files: results,
    };
    
    // Output results
    let formatter = OutputFormatter::new(cmd.output_format);
    formatter.print_compression_summary(&summary)?;
    
    Ok(())
}

async fn compress_zip(
    files: &[PathBuf],
    cmd: &CompressCommand,
) -> Result<Vec<CompressionResult>, Box<dyn std::error::Error>> {
    let pb = create_progress_bar(files.len(), cmd.verbose)?;
    let mut results = Vec::new();
    
    let file = std::fs::File::create(&cmd.output)?;
    let mut zip = ZipWriter::new(file);
    
    let compression_method = match cmd.level {
        0 => CompressionMethod::Stored,
        _ => CompressionMethod::Deflated,
    };
    
    for (idx, file_path) in files.iter().enumerate() {
        if let Some(ref pb) = pb {
            pb.set_message(format!("Compressing {}", file_path.display()));
        }
        
        let result = add_file_to_zip(&mut zip, file_path, compression_method, cmd).await;
        
        results.push(match result {
            Ok(original_size) => CompressionResult {
                file: file_path.display().to_string(),
                original_size,
                compressed_size: None,
                status: "success".to_string(),
                error: None,
            },
            Err(e) => CompressionResult {
                file: file_path.display().to_string(),
                original_size: 0,
                compressed_size: None,
                status: "failed".to_string(),
                error: Some(e.to_string()),
            },
        });
        
        if let Some(ref pb) = pb {
            pb.inc(1);
        }
    }
    
    zip.finish()?;
    
    if let Some(pb) = pb {
        pb.finish_with_message("Compression complete");
    }
    
    Ok(results)
}

async fn compress_gzip(
    files: &[PathBuf],
    cmd: &CompressCommand,
) -> Result<Vec<CompressionResult>, Box<dyn std::error::Error>> {
    if files.len() > 1 {
        return Err("GZIP can only compress single files. Use TAR.GZ for multiple files.".into());
    }
    
    let input_file = &files[0];
    let data = fs::read(input_file).await?;
    let original_size = data.len() as u64;
    
    let output = std::fs::File::create(&cmd.output)?;
    let mut encoder = GzEncoder::new(output, Compression::new(cmd.level));
    encoder.write_all(&data)?;
    encoder.finish()?;
    
    Ok(vec![CompressionResult {
        file: input_file.display().to_string(),
        original_size,
        compressed_size: None,
        status: "success".to_string(),
        error: None,
    }])
}

async fn compress_tar(
    files: &[PathBuf],
    cmd: &CompressCommand,
    gzip: bool,
) -> Result<Vec<CompressionResult>, Box<dyn std::error::Error>> {
    let pb = create_progress_bar(files.len(), cmd.verbose)?;
    let mut results = Vec::new();
    
    let output_file = std::fs::File::create(&cmd.output)?;
    let writer: Box<dyn Write> = if gzip {
        Box::new(GzEncoder::new(output_file, Compression::new(cmd.level)))
    } else {
        Box::new(output_file)
    };
    
    let mut tar = Builder::new(writer);
    
    for file_path in files {
        if let Some(ref pb) = pb {
            pb.set_message(format!("Adding {}", file_path.display()));
        }
        
        let result = add_file_to_tar(&mut tar, file_path, cmd).await;
        
        results.push(match result {
            Ok(original_size) => CompressionResult {
                file: file_path.display().to_string(),
                original_size,
                compressed_size: None,
                status: "success".to_string(),
                error: None,
            },
            Err(e) => CompressionResult {
                file: file_path.display().to_string(),
                original_size: 0,
                compressed_size: None,
                status: "failed".to_string(),
                error: Some(e.to_string()),
            },
        });
        
        if let Some(ref pb) = pb {
            pb.inc(1);
        }
    }
    
    tar.finish()?;
    
    if let Some(pb) = pb {
        pb.finish_with_message("Compression complete");
    }
    
    Ok(results)
}

async fn compress_zlib(
    files: &[PathBuf],
    cmd: &CompressCommand,
) -> Result<Vec<CompressionResult>, Box<dyn std::error::Error>> {
    if files.len() > 1 {
        return Err("ZLIB can only compress single files.".into());
    }
    
    let input_file = &files[0];
    let data = fs::read(input_file).await?;
    let original_size = data.len() as u64;
    
    let output = std::fs::File::create(&cmd.output)?;
    let mut encoder = ZlibEncoder::new(output, Compression::new(cmd.level));
    encoder.write_all(&data)?;
    encoder.finish()?;
    
    Ok(vec![CompressionResult {
        file: input_file.display().to_string(),
        original_size,
        compressed_size: None,
        status: "success".to_string(),
        error: None,
    }])
}

async fn add_file_to_zip<W: Write + std::io::Seek>(
    zip: &mut ZipWriter<W>,
    file_path: &PathBuf,
    method: CompressionMethod,
    cmd: &CompressCommand,
) -> Result<u64, Box<dyn std::error::Error>> {
    let metadata = fs::metadata(file_path).await?;
    let size = metadata.len();
    
    if metadata.is_file() {
        let data = fs::read(file_path).await?;
        let name = if cmd.full_paths {
            file_path.display().to_string()
        } else {
            file_path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("unknown")
                .to_string()
        };
        
        let options = zip::write::FileOptions::default()
            .compression_method(method)
            .unix_permissions(0o644);
        
        zip.start_file(name, options)?;
        zip.write_all(&data)?;
    }
    
    Ok(size)
}

async fn add_file_to_tar<W: Write>(
    tar: &mut Builder<W>,
    file_path: &PathBuf,
    cmd: &CompressCommand,
) -> Result<u64, Box<dyn std::error::Error>> {
    let metadata = fs::metadata(file_path).await?;
    let size = metadata.len();
    
    if metadata.is_file() {
        let name = if cmd.full_paths {
            file_path.as_path()
        } else {
            std::path::Path::new(file_path.file_name().unwrap_or_default())
        };
        
        tar.append_path_with_name(file_path, name)?;
    }
    
    Ok(size)
}

fn create_progress_bar(
    total: usize,
    verbose: bool,
) -> Result<Option<ProgressBar>, Box<dyn std::error::Error>> {
    if verbose {
        Ok(None)
    } else {
        let pb = ProgressBar::new(total as u64);
        pb.set_style(
            ProgressStyle::default_bar()
                .template("[{elapsed_precise}] {bar:40.cyan/blue} {pos}/{len} {msg}")?
                .progress_chars("=>-")
        );
        Ok(Some(pb))
    }
}

impl OutputFormatter {
    pub fn print_compression_summary(&self, summary: &CompressionSummary) -> Result<(), Box<dyn std::error::Error>> {
        match self.format {
            OutputFormat::Json => {
                println!("{}", serde_json::to_string_pretty(summary)?);
            }
            OutputFormat::Table => {
                println!("\nCompression Summary:");
                println!("──────────────────");
                println!("Algorithm:         {}", summary.algorithm);
                println!("Compression level: {}", summary.compression_level);
                println!("Output file:       {}", summary.output_file);
                println!("Output size:       {} bytes", summary.output_size);
                println!("Total files:       {}", summary.total_files);
                println!("Compressed:        {}", summary.compressed);
                println!("Failed:            {}", summary.failed);
                println!("Original size:     {} bytes", summary.total_original_size);
                println!("Compression ratio: {:.1}% of original", summary.compression_ratio * 100.0);
                println!("Space saved:       {:.1}%", (1.0 - summary.compression_ratio) * 100.0);
                
                if !summary.files.is_empty() && summary.failed > 0 {
                    println!("\nFailed Files:");
                    for file in summary.files.iter().filter(|f| f.status == "failed") {
                        println!("  {}: {}", file.file, file.error.as_ref().unwrap_or(&"Unknown error".to_string()));
                    }
                }
            }
            _ => {
                // CSV and Plain formats
                println!("File,OriginalSize,Status");
                for file in &summary.files {
                    println!("{},{},{}",
                        file.file,
                        file.original_size,
                        file.status
                    );
                }
            }
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::io::Write;
    
    #[tokio::test]
    async fn test_compress_zip() {
        let temp_dir = TempDir::new().unwrap();
        let input_file = temp_dir.path().join("test.txt");
        let output_file = temp_dir.path().join("test.zip");
        
        std::fs::File::create(&input_file).unwrap()
            .write_all(b"Test content for compression").unwrap();
        
        let cmd = CompressCommand {
            input: vec![input_file.display().to_string()],
            output: output_file.clone(),
            algorithm: CompressionAlgorithm::Zip,
            level: 6,
            hidden: false,
            follow_links: false,
            exclude: vec![],
            full_paths: false,
            force: false,
            output_format: OutputFormat::Json,
            verbose: false,
        };
        
        let result = execute(cmd).await;
        assert!(result.is_ok());
        assert!(output_file.exists());
    }
}