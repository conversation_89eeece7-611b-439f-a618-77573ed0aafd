//! Enhanced metrics collection and reporting
//!
//! This module provides comprehensive metrics collection using Prometheus format

use axum::http::StatusCode;
use once_cell::sync::Lazy;
use prometheus::{
    register_counter_vec, register_gauge, register_histogram_vec, register_int_counter_vec,
    register_int_gauge_vec, CounterVec, Encoder, Gauge, HistogramVec, IntCounterVec, IntGaugeVec,
    TextEncoder,
};
use std::sync::Arc;
use std::time::Instant;
use tracing::{error, info};

/// API request counter
static API_REQUESTS: Lazy<IntCounterVec> = Lazy::new(|| {
    register_int_counter_vec!(
        "legacybridge_api_requests_total",
        "Total number of API requests",
        &["method", "endpoint", "status"]
    )
    .expect("Failed to register API requests counter")
});

/// Active connections gauge
static ACTIVE_CONNECTIONS: Lazy<IntGaugeVec> = Lazy::new(|| {
    register_int_gauge_vec!(
        "legacybridge_active_connections",
        "Current number of active connections",
        &["type"]
    )
    .expect("Failed to register active connections gauge")
});

/// Conversion duration histogram
static CONVERSION_DURATION: Lazy<HistogramVec> = Lazy::new(|| {
    register_histogram_vec!(
        "legacybridge_conversion_duration_seconds",
        "Conversion duration in seconds",
        &["input_format", "output_format"],
        vec![0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
    )
    .expect("Failed to register conversion duration histogram")
});

/// WebSocket connections gauge
static WEBSOCKET_CONNECTIONS: Lazy<IntGaugeVec> = Lazy::new(|| {
    register_int_gauge_vec!(
        "legacybridge_websocket_connections",
        "Current number of WebSocket connections",
        &["state"]
    )
    .expect("Failed to register WebSocket connections gauge")
});

/// Batch job metrics
static BATCH_JOBS: Lazy<IntGaugeVec> = Lazy::new(|| {
    register_int_gauge_vec!(
        "legacybridge_batch_jobs",
        "Current batch jobs by status",
        &["status"]
    )
    .expect("Failed to register batch jobs gauge")
});

/// Rate limit hits counter
static RATE_LIMIT_HITS: Lazy<IntCounterVec> = Lazy::new(|| {
    register_int_counter_vec!(
        "legacybridge_rate_limit_hits_total",
        "Total number of rate limit hits",
        &["endpoint", "key_type"]
    )
    .expect("Failed to register rate limit hits counter")
});

/// Memory usage gauge
static MEMORY_USAGE: Lazy<Gauge> = Lazy::new(|| {
    register_gauge!(
        "legacybridge_memory_usage_bytes",
        "Current memory usage in bytes"
    )
    .expect("Failed to register memory usage gauge")
});

/// File size histogram
static FILE_SIZE: Lazy<HistogramVec> = Lazy::new(|| {
    register_histogram_vec!(
        "legacybridge_file_size_bytes",
        "File sizes processed",
        &["operation", "format"],
        vec![1024.0, 10240.0, 102400.0, 1048576.0, 10485760.0, 104857600.0] // 1KB, 10KB, 100KB, 1MB, 10MB, 100MB
    )
    .expect("Failed to register file size histogram")
});

/// Metrics collector
pub struct MetricsCollector {
    start_time: Instant,
}

impl MetricsCollector {
    pub fn new() -> Self {
        Self {
            start_time: Instant::now(),
        }
    }
    
    /// Record an API request
    pub fn record_request(&self, method: &str, endpoint: &str, status: u16) {
        API_REQUESTS
            .with_label_values(&[method, endpoint, &status.to_string()])
            .inc();
    }
    
    /// Record a conversion
    pub fn record_conversion(&self, input_format: &str, output_format: &str, duration_secs: f64) {
        CONVERSION_DURATION
            .with_label_values(&[input_format, output_format])
            .observe(duration_secs);
    }
    
    /// Update active connections
    pub fn set_active_connections(&self, connection_type: &str, count: i64) {
        ACTIVE_CONNECTIONS
            .with_label_values(&[connection_type])
            .set(count);
    }
    
    /// Update WebSocket connections
    pub fn set_websocket_connections(&self, state: &str, count: i64) {
        WEBSOCKET_CONNECTIONS
            .with_label_values(&[state])
            .set(count);
    }
    
    /// Update batch job count
    pub fn set_batch_jobs(&self, status: &str, count: i64) {
        BATCH_JOBS
            .with_label_values(&[status])
            .set(count);
    }
    
    /// Record rate limit hit
    pub fn record_rate_limit_hit(&self, endpoint: &str, key_type: &str) {
        RATE_LIMIT_HITS
            .with_label_values(&[endpoint, key_type])
            .inc();
    }
    
    /// Update memory usage
    pub fn update_memory_usage(&self) {
        #[cfg(target_os = "linux")]
        {
            if let Ok(me) = procfs::process::Process::myself() {
                if let Ok(stat) = me.stat() {
                    MEMORY_USAGE.set(stat.vsize as f64);
                }
            }
        }
        
        #[cfg(not(target_os = "linux"))]
        {
            // Fallback for non-Linux systems
            // This is a rough estimate based on the allocator
            use std::alloc::{GlobalAlloc, Layout, System};
            // Note: This is a placeholder, actual implementation would need platform-specific code
            MEMORY_USAGE.set(52428800.0); // 50MB placeholder
        }
    }
    
    /// Record file size
    pub fn record_file_size(&self, operation: &str, format: &str, size_bytes: usize) {
        FILE_SIZE
            .with_label_values(&[operation, format])
            .observe(size_bytes as f64);
    }
    
    /// Get uptime in seconds
    pub fn uptime_seconds(&self) -> u64 {
        self.start_time.elapsed().as_secs()
    }
    
    /// Collect all metrics in Prometheus format
    pub fn collect_metrics(&self) -> Result<String, Box<dyn std::error::Error>> {
        // Update dynamic metrics
        self.update_memory_usage();
        
        // Create encoder
        let encoder = TextEncoder::new();
        let metric_families = prometheus::gather();
        
        // Encode metrics
        let mut buffer = Vec::new();
        encoder.encode(&metric_families, &mut buffer)?;
        
        // Add custom metrics
        let custom_metrics = format!(
            "# HELP legacybridge_uptime_seconds Server uptime in seconds\n\
             # TYPE legacybridge_uptime_seconds gauge\n\
             legacybridge_uptime_seconds {}\n",
            self.uptime_seconds()
        );
        
        buffer.extend_from_slice(custom_metrics.as_bytes());
        
        Ok(String::from_utf8(buffer)?)
    }
}

impl Default for MetricsCollector {
    fn default() -> Self {
        Self::new()
    }
}

/// Global metrics collector instance
pub static METRICS: Lazy<Arc<MetricsCollector>> = Lazy::new(|| {
    Arc::new(MetricsCollector::new())
});

/// Metrics handler for the API endpoint
pub async fn metrics_handler() -> Result<String, StatusCode> {
    info!("Metrics request received");
    
    match METRICS.collect_metrics() {
        Ok(metrics) => Ok(metrics),
        Err(e) => {
            error!("Failed to collect metrics: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Helper to time operations
pub struct Timer {
    start: Instant,
    labels: Vec<String>,
}

impl Timer {
    pub fn new(labels: Vec<String>) -> Self {
        Self {
            start: Instant::now(),
            labels,
        }
    }
    
    pub fn observe(self, histogram: &HistogramVec) {
        let duration = self.start.elapsed().as_secs_f64();
        let label_refs: Vec<&str> = self.labels.iter().map(|s| s.as_str()).collect();
        histogram.with_label_values(&label_refs).observe(duration);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_metrics_collector() {
        let collector = MetricsCollector::new();
        
        // Record some metrics
        collector.record_request("GET", "/health", 200);
        collector.record_conversion("rtf", "md", 0.5);
        collector.set_active_connections("http", 10);
        collector.record_file_size("upload", "doc", 102400);
        
        // Collect metrics
        let result = collector.collect_metrics();
        assert!(result.is_ok());
        
        let metrics_text = result.unwrap();
        assert!(metrics_text.contains("legacybridge_api_requests_total"));
        assert!(metrics_text.contains("legacybridge_conversion_duration_seconds"));
        assert!(metrics_text.contains("legacybridge_uptime_seconds"));
    }
    
    #[test]
    fn test_timer() {
        let timer = Timer::new(vec!["test".to_string(), "label".to_string()]);
        std::thread::sleep(std::time::Duration::from_millis(10));
        // In real use, timer.observe(&SOME_HISTOGRAM);
    }
}