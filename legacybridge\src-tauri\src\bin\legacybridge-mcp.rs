// LegacyBridge MCP Server Binary
// This binary runs the LegacyBridge MCP server for AI assistant integration
// Updated to use official rust-mcp-sdk

#[cfg(feature = "mcp")]
use legacybridge::mcp::official_server::LegacyBridgeMcpServerOfficial;
use legacybridge::config::Config;
use tracing::{info, error};
use tracing_subscriber;

#[cfg(feature = "mcp")]
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::init();
    
    info!("LegacyBridge MCP Server v2.0.0 starting with official rust-mcp-sdk...");
    eprintln!("LegacyBridge MCP Server v2.0.0 starting...");
    eprintln!("Protocol: stdio (official rust-mcp-sdk)");
    eprintln!("SDK: rust-mcp-sdk v0.2.0");
    
    // Load configuration
    let config = Config::load().unwrap_or_else(|e| {
        error!("Failed to load config, using defaults: {}", e);
        Config::default()
    });
    
    // Create and run server with official SDK
    let server = LegacyBridgeMcpServerOfficial::new(config);
    
    // Run the server
    if let Err(e) = server.run_stdio_server().await {
        error!("Server error: {}", e);
        std::process::exit(1);
    }
    
    Ok(())
}

#[cfg(not(feature = "mcp"))]
fn main() {
    eprintln!("MCP feature not enabled. Please compile with --features mcp");
    std::process::exit(1);
}

