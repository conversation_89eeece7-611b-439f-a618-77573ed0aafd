// LegacyBridge MCP Server Binary
// Standalone executable for running the MCP server

use legacybridge::mcp::{run_mcp_server, McpDeploymentConfig};
use legacybridge::mcp::enterprise_config::{EnterpriseConfigManager, EnterpriseConfig};
use clap::{Parser, Subcommand};
use std::process;
use std::path::PathBuf;
use tracing::{info, error};

#[derive(Parser)]
#[command(name = "legacybridge-mcp")]
#[command(about = "LegacyBridge MCP Server - Document conversion service for AI assistants", long_about = None)]
#[command(version)]
struct Cli {
    #[command(subcommand)]
    command: Option<Commands>,
    
    /// Run in specific environment (development, production, docker)
    #[arg(short, long, default_value = "development")]
    env: String,
    
    /// Path to configuration file
    #[arg(short, long)]
    config: Option<String>,
    
    /// Override server protocol (stdio, http, websocket)
    #[arg(short, long)]
    protocol: Option<String>,
    
    /// Override server port
    #[arg(short = 'P', long)]
    port: Option<u16>,
}

#[derive(Subcommand)]
enum Commands {
    /// Start the MCP server
    Start,
    
    /// Generate example configuration file
    GenConfig {
        /// Output path for configuration file
        #[arg(default_value = "legacybridge-mcp.toml")]
        output: String,
    },
    
    /// Validate configuration file
    Validate {
        /// Path to configuration file to validate
        path: String,
    },
    
    /// Enterprise deployment commands
    Enterprise {
        #[command(subcommand)]
        cmd: EnterpriseCommands,
    },
    
    /// Show server information and capabilities
    Info,
}

#[derive(Subcommand)]
enum EnterpriseCommands {
    /// Initialize enterprise deployment
    Init {
        /// Organization name
        #[arg(long)]
        organization: String,
        
        /// License key
        #[arg(long)]
        license_key: String,
        
        /// Admin email
        #[arg(long)]
        admin_email: String,
        
        /// Output directory for configuration
        #[arg(long, default_value = "/etc/legacybridge")]
        output_dir: String,
    },
    
    /// Generate MCP configuration for enterprise deployment
    Generate {
        /// Deployment ID
        #[arg(long)]
        deployment_id: String,
        
        /// Configuration directory
        #[arg(long, default_value = "/etc/legacybridge")]
        config_dir: String,
    },
    
    /// Validate enterprise license
    ValidateLicense {
        /// License key to validate
        #[arg(long)]
        license_key: String,
    },
}

#[tokio::main]
async fn main() {
    let cli = Cli::parse();
    
    // Set environment variable if provided
    if let Some(config_path) = &cli.config {
        std::env::set_var("MCP_CONFIG_FILE", config_path);
    }
    
    // Set protocol override
    if let Some(protocol) = &cli.protocol {
        std::env::set_var("MCP_SERVER_PROTOCOL", protocol);
    }
    
    // Set port override
    if let Some(port) = cli.port {
        std::env::set_var("MCP_SERVER_PORT", port.to_string());
    }
    
    // Set environment
    std::env::set_var("MCP_ENV", &cli.env);
    
    match &cli.command {
        Some(Commands::Start) | None => {
            info!("Starting LegacyBridge MCP Server");
            if let Err(e) = run_mcp_server().await {
                error!("Server error: {}", e);
                process::exit(1);
            }
        }
        
        Some(Commands::GenConfig { output }) => {
            generate_config_file(output, &cli.env);
        }
        
        Some(Commands::Validate { path }) => {
            validate_config_file(path);
        }
        
        Some(Commands::Info) => {
            show_server_info();
        }
        
        Some(Commands::Enterprise { cmd }) => {
            handle_enterprise_command(cmd).await;
        }
    }
}

fn generate_config_file(output: &str, env: &str) {
    let config = match env {
        "production" => McpDeploymentConfig::production(),
        "docker" => McpDeploymentConfig::docker(),
        _ => McpDeploymentConfig::development(),
    };
    
    let toml_content = toml::to_string_pretty(&config).expect("Failed to serialize config");
    
    if let Err(e) = std::fs::write(output, toml_content) {
        error!("Failed to write configuration file: {}", e);
        process::exit(1);
    }
    
    println!("Configuration file generated: {}", output);
    println!("\nExample usage:");
    println!("  legacybridge-mcp --config {} start", output);
}

fn validate_config_file(path: &str) {
    match McpDeploymentConfig::from_file(path) {
        Ok(config) => {
            match config.validate() {
                Ok(_) => {
                    println!("✓ Configuration file is valid");
                    println!("\nConfiguration summary:");
                    println!("  Protocol: {}", config.server.protocol);
                    println!("  Host: {}", config.server.host);
                    println!("  Port: {}", config.server.port);
                    println!("  Authentication: {}", config.security.enable_authentication);
                    println!("  Worker threads: {}", config.performance.worker_threads);
                }
                Err(e) => {
                    error!("Configuration validation failed: {}", e);
                    process::exit(1);
                }
            }
        }
        Err(e) => {
            error!("Failed to load configuration file: {}", e);
            process::exit(1);
        }
    }
}

async fn handle_enterprise_command(cmd: &EnterpriseCommands) {
    match cmd {
        EnterpriseCommands::Init { organization, license_key, admin_email, output_dir } => {
            println!("Initializing enterprise deployment for {}", organization);
            
            let base_path = PathBuf::from(output_dir);
            let mut manager = EnterpriseConfigManager::new(base_path.clone());
            
            // Validate license
            match manager.validate_license(license_key) {
                Ok(true) => println!("✓ License validated successfully"),
                Ok(false) => {
                    error!("Invalid license key format");
                    process::exit(1);
                }
                Err(e) => {
                    error!("License validation error: {}", e);
                    process::exit(1);
                }
            }
            
            // Initialize deployment
            match manager.initialize_deployment(organization, license_key) {
                Ok(config) => {
                    println!("✓ Enterprise configuration created");
                    println!("\nDeployment Details:");
                    println!("  Deployment ID: {}", config.deployment.deployment_id);
                    println!("  Organization: {}", config.organization.name);
                    println!("  Environment: {}", config.deployment.environment);
                    println!("  High Availability: {}", config.deployment.high_availability);
                    println!("\nConfiguration files created in:");
                    println!("  {}/deployments/{}/", output_dir, config.deployment.deployment_id);
                    println!("\nNext steps:");
                    println!("  1. Configure TLS certificates");
                    println!("  2. Set environment variables");
                    println!("  3. Start the server with:");
                    println!("     legacybridge-mcp server --deployment-id {}", config.deployment.deployment_id);
                }
                Err(e) => {
                    error!("Failed to initialize deployment: {}", e);
                    process::exit(1);
                }
            }
        }
        
        EnterpriseCommands::Generate { deployment_id, config_dir } => {
            println!("Generating MCP configuration for deployment {}", deployment_id);
            
            let base_path = PathBuf::from(config_dir);
            let mut manager = EnterpriseConfigManager::new(base_path);
            
            match manager.load_config(deployment_id) {
                Ok(config) => {
                    match manager.generate_mcp_config(&config) {
                        Ok(mcp_config) => {
                            let output_path = format!("{}/deployments/{}/.mcp.json", config_dir, deployment_id);
                            let json = serde_json::to_string_pretty(&mcp_config).unwrap();
                            
                            if let Err(e) = std::fs::write(&output_path, json) {
                                error!("Failed to write MCP configuration: {}", e);
                                process::exit(1);
                            }
                            
                            println!("✓ MCP configuration generated: {}", output_path);
                        }
                        Err(e) => {
                            error!("Failed to generate MCP configuration: {}", e);
                            process::exit(1);
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to load enterprise configuration: {}", e);
                    process::exit(1);
                }
            }
        }
        
        EnterpriseCommands::ValidateLicense { license_key } => {
            let manager = EnterpriseConfigManager::new(PathBuf::from("/tmp"));
            
            match manager.validate_license(license_key) {
                Ok(true) => {
                    println!("✓ License key is valid");
                    println!("\nLicense format: XXXX-XXXX-XXXX-XXXX");
                    println!("License type: Enterprise");
                }
                Ok(false) => {
                    println!("✗ License key is invalid");
                    println!("\nExpected format: XXXX-XXXX-XXXX-XXXX");
                    process::exit(1);
                }
                Err(e) => {
                    error!("License validation error: {}", e);
                    process::exit(1);
                }
            }
        }
    }
}

fn show_server_info() {
    println!("LegacyBridge MCP Server");
    println!("====================");
    println!();
    println!("Version: 1.0.0");
    println!("Protocol: Model Context Protocol (MCP)");
    println!();
    println!("Supported Formats:");
    println!("  Input:  RTF, DOC, WordPerfect, Lotus 1-2-3, dBase, WordStar");
    println!("  Output: Markdown, RTF, HTML, PDF, TXT, CSV, JSON, XML, DOCX");
    println!();
    println!("Available Tools:");
    println!("  - convert_file: Universal file conversion");
    println!("  - rtf_to_markdown: Optimized RTF to Markdown conversion");
    println!("  - markdown_to_rtf: Convert Markdown to RTF");
    println!("  - convert_legacy_format: Handle legacy formats");
    println!("  - detect_file_format: Automatic format detection");
    println!("  - validate_file: File integrity checking");
    println!("  - batch_convert: Multi-file processing");
    println!("  - build_dll: Create DLLs for VB6/VFP9 integration");
    println!();
    println!("Protocols:");
    println!("  - stdio: For AI assistants (Claude, etc.)");
    println!("  - http: REST API endpoint");
    println!("  - websocket: Real-time communication");
    println!();
    println!("Usage:");
    println!("  legacybridge-mcp start              # Start with defaults");
    println!("  legacybridge-mcp --env production   # Production mode");
    println!("  legacybridge-mcp --protocol stdio   # Force stdio mode");
    println!("  legacybridge-mcp gen-config         # Generate config file");
}