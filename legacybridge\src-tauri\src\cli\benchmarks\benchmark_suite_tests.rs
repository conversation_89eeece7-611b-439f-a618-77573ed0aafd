use super::*;
use tempfile::TempDir;
use std::time::Duration;

#[cfg(test)]
mod benchmark_suite_tests {
    use super::*;

    async fn create_test_suite() -> (BenchmarkSuite, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let data_manager = TestDataManager::new(&temp_dir.path()).unwrap();
        let conversion_engine = ConversionEngine::new();
        let suite = BenchmarkSuite::new(data_manager, conversion_engine);
        (suite, temp_dir)
    }

    #[tokio::test]
    async fn test_suite_creation() {
        let (suite, _temp_dir) = create_test_suite().await;
        // Suite should be created with empty configs
        assert_eq!(suite.configs.len(), 0);
    }

    #[tokio::test]
    async fn test_add_benchmark() {
        let (mut suite, _temp_dir) = create_test_suite().await;
        
        let config = BenchmarkConfig {
            name: "test_benchmark".to_string(),
            description: "Test benchmark".to_string(),
            benchmark_type: BenchmarkType::ConversionSpeed {
                input_format: "rtf".to_string(),
                output_format: "md".to_string(),
                file_sizes: vec![SizeCategory::Small],
            },
            warmup_iterations: 1,
            measurement_iterations: 3,
            tags: vec!["test".to_string()],
        };
        
        suite.add_benchmark(config.clone());
        assert_eq!(suite.configs.len(), 1);
        assert_eq!(suite.configs[0].name, "test_benchmark");
    }

    #[tokio::test]
    async fn test_load_configs_json() {
        let (mut suite, temp_dir) = create_test_suite().await;
        
        // Create a JSON config file
        let config = vec![
            BenchmarkConfig {
                name: "json_benchmark".to_string(),
                description: "JSON loaded benchmark".to_string(),
                benchmark_type: BenchmarkType::MemoryUsage {
                    operation: "conversion".to_string(),
                    iterations: 10,
                },
                warmup_iterations: 2,
                measurement_iterations: 5,
                tags: vec!["json".to_string()],
            },
        ];
        
        let config_path = temp_dir.path().join("benchmarks.json");
        let json = serde_json::to_string_pretty(&config).unwrap();
        fs::write(&config_path, json).unwrap();
        
        // Load configs
        suite.load_configs(&config_path).await.unwrap();
        
        assert_eq!(suite.configs.len(), 1);
        assert_eq!(suite.configs[0].name, "json_benchmark");
    }

    #[tokio::test]
    async fn test_load_configs_yaml() {
        let (mut suite, temp_dir) = create_test_suite().await;
        
        // Create a YAML config file
        let config = vec![
            BenchmarkConfig {
                name: "yaml_benchmark".to_string(),
                description: "YAML loaded benchmark".to_string(),
                benchmark_type: BenchmarkType::Throughput {
                    operation: "conversion".to_string(),
                    duration_seconds: 5,
                },
                warmup_iterations: 1,
                measurement_iterations: 3,
                tags: vec!["yaml".to_string()],
            },
        ];
        
        let config_path = temp_dir.path().join("benchmarks.yaml");
        let yaml = serde_yaml::to_string(&config).unwrap();
        fs::write(&config_path, yaml).unwrap();
        
        // Load configs
        suite.load_configs(&config_path).await.unwrap();
        
        assert_eq!(suite.configs.len(), 1);
        assert_eq!(suite.configs[0].name, "yaml_benchmark");
    }

    #[tokio::test]
    async fn test_run_single_benchmark() {
        let (mut suite, temp_dir) = create_test_suite().await;
        
        // Generate test data
        let mut data_manager = TestDataManager::new(&temp_dir.path()).unwrap();
        data_manager.generate_sample_files("rtf", 2).unwrap();
        
        let config = BenchmarkConfig {
            name: "single_benchmark".to_string(),
            description: "Single benchmark test".to_string(),
            benchmark_type: BenchmarkType::ConversionSpeed {
                input_format: "rtf".to_string(),
                output_format: "md".to_string(),
                file_sizes: vec![SizeCategory::Tiny],
            },
            warmup_iterations: 1,
            measurement_iterations: 2,
            tags: vec!["single".to_string()],
        };
        
        let result = suite.run_benchmark(&config).await;
        
        assert_eq!(result.benchmark_name, "single_benchmark");
        assert_eq!(result.measurements.len(), 2); // 2 measurement iterations
        assert!(result.statistics.mean > 0.0);
        assert!(result.statistics.min <= result.statistics.max);
    }

    #[tokio::test]
    async fn test_calculate_statistics() {
        let (suite, _temp_dir) = create_test_suite().await;
        
        let measurements = vec![
            Measurement {
                iteration: 0,
                duration_ms: 100.0,
                memory_bytes: 1000,
                throughput_mbps: Some(10.0),
                custom_metrics: HashMap::new(),
            },
            Measurement {
                iteration: 1,
                duration_ms: 120.0,
                memory_bytes: 1200,
                throughput_mbps: Some(9.0),
                custom_metrics: HashMap::new(),
            },
            Measurement {
                iteration: 2,
                duration_ms: 110.0,
                memory_bytes: 1100,
                throughput_mbps: Some(9.5),
                custom_metrics: HashMap::new(),
            },
        ];
        
        let stats = suite.calculate_statistics(&measurements);
        
        // Check mean calculation
        assert!((stats.mean - 110.0).abs() < 0.1);
        
        // Check min/max
        assert_eq!(stats.min, 100.0);
        assert_eq!(stats.max, 120.0);
        
        // Check median
        assert_eq!(stats.median, 110.0);
        
        // Check percentiles
        assert!(stats.percentile_95 >= stats.median);
        assert!(stats.percentile_99 >= stats.percentile_95);
    }

    #[tokio::test]
    async fn test_memory_usage_benchmark() {
        let (suite, temp_dir) = create_test_suite().await;
        
        // Generate test data
        let mut data_manager = TestDataManager::new(&temp_dir.path()).unwrap();
        data_manager.generate_sample_files("rtf", 1).unwrap();
        
        let result = suite.benchmark_memory_usage("conversion", 2).await.unwrap();
        
        assert!(result.duration_ms > 0.0);
        assert!(result.custom_metrics.contains_key("peak_memory_mb"));
        assert!(result.custom_metrics.contains_key("iterations"));
        assert_eq!(result.custom_metrics["iterations"], 2.0);
    }

    #[tokio::test]
    async fn test_latency_benchmark() {
        let (suite, temp_dir) = create_test_suite().await;
        
        // Generate test data
        let mut data_manager = TestDataManager::new(&temp_dir.path()).unwrap();
        data_manager.generate_sample_files("rtf", 1).unwrap();
        
        let percentiles = vec![50.0, 90.0, 99.0];
        let result = suite.benchmark_latency("conversion", &percentiles).await.unwrap();
        
        assert!(result.duration_ms > 0.0);
        assert!(result.custom_metrics.contains_key("p50"));
        assert!(result.custom_metrics.contains_key("p90"));
        assert!(result.custom_metrics.contains_key("p99"));
        
        // Percentiles should be in ascending order
        assert!(result.custom_metrics["p50"] <= result.custom_metrics["p90"]);
        assert!(result.custom_metrics["p90"] <= result.custom_metrics["p99"]);
    }

    #[tokio::test]
    async fn test_baseline_comparison() {
        let (suite, temp_dir) = create_test_suite().await;
        
        // Store a baseline
        let mut data_manager = TestDataManager::new(&temp_dir.path()).unwrap();
        let baseline_metrics = PerformanceMetrics {
            duration_ms: 100.0,
            memory_bytes: 1024 * 1024,
            cpu_percent: 20.0,
            throughput_mbps: 10.0,
            timestamp: Utc::now(),
        };
        
        data_manager.store_baseline("test_benchmark", baseline_metrics).unwrap();
        
        // Create current statistics
        let current_stats = BenchmarkStatistics {
            mean: 120.0, // 20% slower
            median: 120.0,
            std_dev: 5.0,
            min: 115.0,
            max: 125.0,
            percentile_95: 124.0,
            percentile_99: 125.0,
        };
        
        let comparison = suite.compare_with_baseline("test_benchmark", &current_stats).await;
        assert!(comparison.is_some());
        
        let comp = comparison.unwrap();
        assert_eq!(comp.baseline_mean, 100.0);
        assert_eq!(comp.current_mean, 120.0);
        assert_eq!(comp.percent_change, 20.0);
        assert!(comp.regression_detected);
        assert!(!comp.improvement_detected);
    }

    #[tokio::test]
    async fn test_export_results_json() {
        let (suite, temp_dir) = create_test_suite().await;
        
        // Create test results
        let results = vec![
            BenchmarkResult {
                benchmark_name: "test1".to_string(),
                measurements: vec![],
                statistics: BenchmarkStatistics {
                    mean: 100.0,
                    median: 100.0,
                    std_dev: 10.0,
                    min: 90.0,
                    max: 110.0,
                    percentile_95: 108.0,
                    percentile_99: 110.0,
                },
                baseline_comparison: None,
                environment: EnvironmentInfo {
                    cpu_model: "Test CPU".to_string(),
                    cpu_cores: 4,
                    memory_gb: 8.0,
                    os: "Test OS".to_string(),
                    rust_version: "1.0.0".to_string(),
                },
                timestamp: Utc::now(),
            },
        ];
        
        {
            let mut suite_results = suite.results.lock().await;
            suite_results.extend(results);
        }
        
        let export_path = temp_dir.path().join("results.json");
        suite.export_results("json", &export_path).await.unwrap();
        
        assert!(export_path.exists());
        
        // Verify JSON structure
        let content = fs::read_to_string(&export_path).unwrap();
        let parsed: Vec<BenchmarkResult> = serde_json::from_str(&content).unwrap();
        assert_eq!(parsed.len(), 1);
        assert_eq!(parsed[0].benchmark_name, "test1");
    }

    #[tokio::test]
    async fn test_export_results_csv() {
        let (suite, temp_dir) = create_test_suite().await;
        
        // Create test results
        let results = vec![
            BenchmarkResult {
                benchmark_name: "csv_test".to_string(),
                measurements: vec![],
                statistics: BenchmarkStatistics {
                    mean: 50.0,
                    median: 50.0,
                    std_dev: 5.0,
                    min: 45.0,
                    max: 55.0,
                    percentile_95: 54.0,
                    percentile_99: 55.0,
                },
                baseline_comparison: None,
                environment: EnvironmentInfo {
                    cpu_model: "Test CPU".to_string(),
                    cpu_cores: 4,
                    memory_gb: 8.0,
                    os: "Test OS".to_string(),
                    rust_version: "1.0.0".to_string(),
                },
                timestamp: Utc::now(),
            },
        ];
        
        {
            let mut suite_results = suite.results.lock().await;
            suite_results.extend(results);
        }
        
        let export_path = temp_dir.path().join("results.csv");
        suite.export_results("csv", &export_path).await.unwrap();
        
        assert!(export_path.exists());
        
        // Verify CSV structure
        let content = fs::read_to_string(&export_path).unwrap();
        assert!(content.contains("benchmark,mean_ms,median_ms"));
        assert!(content.contains("csv_test,50.00"));
    }

    #[tokio::test]
    async fn test_export_results_html() {
        let (suite, temp_dir) = create_test_suite().await;
        
        // Create test results
        let results = vec![
            BenchmarkResult {
                benchmark_name: "html_test".to_string(),
                measurements: vec![],
                statistics: BenchmarkStatistics {
                    mean: 75.0,
                    median: 75.0,
                    std_dev: 7.5,
                    min: 70.0,
                    max: 80.0,
                    percentile_95: 79.0,
                    percentile_99: 80.0,
                },
                baseline_comparison: Some(BaselineComparison {
                    baseline_mean: 70.0,
                    current_mean: 75.0,
                    percent_change: 7.14,
                    regression_detected: false,
                    improvement_detected: false,
                }),
                environment: EnvironmentInfo {
                    cpu_model: "Test CPU".to_string(),
                    cpu_cores: 4,
                    memory_gb: 8.0,
                    os: "Test OS".to_string(),
                    rust_version: "1.0.0".to_string(),
                },
                timestamp: Utc::now(),
            },
        ];
        
        {
            let mut suite_results = suite.results.lock().await;
            suite_results.extend(results);
        }
        
        let export_path = temp_dir.path().join("results.html");
        suite.export_results("html", &export_path).await.unwrap();
        
        assert!(export_path.exists());
        
        // Verify HTML structure
        let content = fs::read_to_string(&export_path).unwrap();
        assert!(content.contains("<!DOCTYPE html>"));
        assert!(content.contains("LegacyBridge Benchmark Report"));
        assert!(content.contains("html_test"));
        assert!(content.contains("75.00"));
    }

    #[test]
    fn test_create_default_benchmarks() {
        let benchmarks = create_default_benchmarks();
        
        assert_eq!(benchmarks.len(), 5);
        
        // Verify benchmark names
        let names: Vec<&str> = benchmarks.iter().map(|b| b.name.as_str()).collect();
        assert!(names.contains(&"conversion_speed_rtf_to_md"));
        assert!(names.contains(&"memory_usage_conversion"));
        assert!(names.contains(&"throughput_batch_conversion"));
        assert!(names.contains(&"latency_single_file"));
        assert!(names.contains(&"concurrency_scaling"));
        
        // Verify all have valid configurations
        for benchmark in benchmarks {
            assert!(!benchmark.name.is_empty());
            assert!(!benchmark.description.is_empty());
            assert!(benchmark.warmup_iterations > 0);
            assert!(benchmark.measurement_iterations > 0);
            assert!(!benchmark.tags.is_empty());
        }
    }

    #[test]
    fn test_environment_info() {
        let (suite, _temp_dir) = tokio_test::block_on(create_test_suite());
        
        let env_info = suite.get_environment_info();
        
        assert!(env_info.cpu_cores > 0);
        assert!(env_info.memory_gb > 0.0);
        assert!(!env_info.os.is_empty());
        assert!(!env_info.rust_version.is_empty());
    }
}