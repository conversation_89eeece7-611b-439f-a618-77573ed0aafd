use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::time::{Duration, Instant};
use std::sync::Arc;
use std::fs;
use tokio::sync::Mutex;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use indicatif::{ProgressBar, ProgressStyle};
use uuid::Uuid;
use statrs::statistics::{Statistics, OrderStatistics};

use crate::conversion::{ConversionEngine, ConversionOptions, ConversionQuality};
use crate::formats::FormatType;
use crate::cli::testing::test_data::{TestDataManager, PerformanceMetrics, SizeCategory};

/// Benchmark types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BenchmarkType {
    /// Conversion speed benchmark
    ConversionSpeed {
        input_format: String,
        output_format: String,
        file_sizes: Vec<SizeCategory>,
    },
    /// Memory usage benchmark
    MemoryUsage {
        operation: String,
        iterations: u32,
    },
    /// Throughput benchmark
    Throughput {
        operation: String,
        duration_seconds: u64,
    },
    /// Latency benchmark
    Latency {
        operation: String,
        percentiles: Vec<f64>,
    },
    /// Concurrent operations benchmark
    Concurrency {
        operation: String,
        thread_counts: Vec<usize>,
    },
}

/// Benchmark configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BenchmarkConfig {
    pub name: String,
    pub description: String,
    pub benchmark_type: BenchmarkType,
    pub warmup_iterations: u32,
    pub measurement_iterations: u32,
    pub tags: Vec<String>,
}

/// Benchmark result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BenchmarkResult {
    pub benchmark_name: String,
    pub measurements: Vec<Measurement>,
    pub statistics: BenchmarkStatistics,
    pub baseline_comparison: Option<BaselineComparison>,
    pub environment: EnvironmentInfo,
    pub timestamp: DateTime<Utc>,
}

/// Individual measurement
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Measurement {
    pub iteration: u32,
    pub duration_ms: f64,
    pub memory_bytes: u64,
    pub throughput_mbps: Option<f64>,
    pub custom_metrics: HashMap<String, f64>,
}

/// Benchmark statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BenchmarkStatistics {
    pub mean: f64,
    pub median: f64,
    pub std_dev: f64,
    pub min: f64,
    pub max: f64,
    pub percentile_95: f64,
    pub percentile_99: f64,
}

/// Baseline comparison
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BaselineComparison {
    pub baseline_mean: f64,
    pub current_mean: f64,
    pub percent_change: f64,
    pub regression_detected: bool,
    pub improvement_detected: bool,
}

/// Environment information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentInfo {
    pub cpu_model: String,
    pub cpu_cores: usize,
    pub memory_gb: f64,
    pub os: String,
    pub rust_version: String,
}

/// Benchmark suite for running performance benchmarks
pub struct BenchmarkSuite {
    configs: Vec<BenchmarkConfig>,
    data_manager: Arc<Mutex<TestDataManager>>,
    conversion_engine: Arc<ConversionEngine>,
    results: Arc<Mutex<Vec<BenchmarkResult>>>,
}

impl BenchmarkSuite {
    /// Create a new benchmark suite
    pub fn new(
        data_manager: TestDataManager,
        conversion_engine: ConversionEngine,
    ) -> Self {
        Self {
            configs: Vec::new(),
            data_manager: Arc::new(Mutex::new(data_manager)),
            conversion_engine: Arc::new(conversion_engine),
            results: Arc::new(Mutex::new(Vec::new())),
        }
    }
    
    /// Add a benchmark configuration
    pub fn add_benchmark(&mut self, config: BenchmarkConfig) {
        self.configs.push(config);
    }
    
    /// Load benchmark configurations from file
    pub async fn load_configs(&mut self, path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        let content = fs::read_to_string(path)?;
        if path.extension().and_then(|e| e.to_str()) == Some("yaml") || 
           path.extension().and_then(|e| e.to_str()) == Some("yml") {
            let configs: Vec<BenchmarkConfig> = serde_yaml::from_str(&content)?;
            self.configs.extend(configs);
        } else {
            let configs: Vec<BenchmarkConfig> = serde_json::from_str(&content)?;
            self.configs.extend(configs);
        }
        Ok(())
    }
    
    /// Run all benchmarks
    pub async fn run_all(&self) -> Vec<BenchmarkResult> {
        let mut all_results = Vec::new();
        
        for config in &self.configs {
            println!("Running benchmark: {}", config.name);
            let result = self.run_benchmark(config).await;
            all_results.push(result.clone());
            
            let mut results = self.results.lock().await;
            results.push(result);
        }
        
        all_results
    }
    
    /// Run a single benchmark
    pub async fn run_benchmark(&self, config: &BenchmarkConfig) -> BenchmarkResult {
        let progress = ProgressBar::new((config.warmup_iterations + config.measurement_iterations) as u64);
        progress.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({msg})")
                .progress_chars("#>-")
        );
        
        // Warmup phase
        progress.set_message("Warming up...");
        for _ in 0..config.warmup_iterations {
            let _ = self.run_single_iteration(config).await;
            progress.inc(1);
        }
        
        // Measurement phase
        progress.set_message("Measuring...");
        let mut measurements = Vec::new();
        for i in 0..config.measurement_iterations {
            if let Ok(measurement) = self.run_single_iteration(config).await {
                measurements.push(measurement);
            }
            progress.inc(1);
        }
        
        progress.finish_with_message("Benchmark complete");
        
        // Calculate statistics
        let statistics = self.calculate_statistics(&measurements);
        
        // Load baseline for comparison
        let baseline_comparison = self.compare_with_baseline(&config.name, &statistics).await;
        
        BenchmarkResult {
            benchmark_name: config.name.clone(),
            measurements,
            statistics,
            baseline_comparison,
            environment: self.get_environment_info(),
            timestamp: Utc::now(),
        }
    }
    
    /// Run a single benchmark iteration
    async fn run_single_iteration(&self, config: &BenchmarkConfig) -> Result<Measurement, Box<dyn std::error::Error>> {
        match &config.benchmark_type {
            BenchmarkType::ConversionSpeed { input_format, output_format, file_sizes } => {
                self.benchmark_conversion_speed(input_format, output_format, file_sizes).await
            }
            BenchmarkType::MemoryUsage { operation, iterations } => {
                self.benchmark_memory_usage(operation, *iterations).await
            }
            BenchmarkType::Throughput { operation, duration_seconds } => {
                self.benchmark_throughput(operation, *duration_seconds).await
            }
            BenchmarkType::Latency { operation, percentiles } => {
                self.benchmark_latency(operation, percentiles).await
            }
            BenchmarkType::Concurrency { operation, thread_counts } => {
                self.benchmark_concurrency(operation, thread_counts).await
            }
        }
    }
    
    /// Benchmark conversion speed
    async fn benchmark_conversion_speed(
        &self,
        input_format: &str,
        output_format: &str,
        file_sizes: &[SizeCategory],
    ) -> Result<Measurement, Box<dyn std::error::Error>> {
        let mut total_duration = Duration::from_secs(0);
        let mut total_bytes = 0u64;
        let mut memory_before = self.get_memory_usage();
        
        for size in file_sizes {
            let data_manager = self.data_manager.lock().await;
            let test_files = data_manager.get_by_tags(&[
                input_format.to_string(),
                size.as_str().to_string(),
            ]);
            
            if let Some(test_file) = test_files.first() {
                let content = fs::read(&test_file.path)?;
                total_bytes += content.len() as u64;
                
                drop(data_manager); // Release lock before conversion
                
                let start = Instant::now();
                
                let options = ConversionOptions {
                    target_format: FormatType::from_extension(output_format).unwrap_or(FormatType::Markdown),
                    quality: ConversionQuality::Balanced,
                    preserve_formatting: true,
                    preserve_metadata: true,
                    custom_options: HashMap::new(),
                };
                
                let _ = self.conversion_engine.convert(&content, options).await?;
                
                total_duration += start.elapsed();
            }
        }
        
        let memory_after = self.get_memory_usage();
        let throughput_mbps = if total_duration.as_secs_f64() > 0.0 {
            Some((total_bytes as f64 / total_duration.as_secs_f64()) / 1_048_576.0)
        } else {
            None
        };
        
        Ok(Measurement {
            iteration: 0,
            duration_ms: total_duration.as_secs_f64() * 1000.0,
            memory_bytes: memory_after.saturating_sub(memory_before),
            throughput_mbps,
            custom_metrics: HashMap::new(),
        })
    }
    
    /// Benchmark memory usage
    async fn benchmark_memory_usage(
        &self,
        operation: &str,
        iterations: u32,
    ) -> Result<Measurement, Box<dyn std::error::Error>> {
        let memory_start = self.get_memory_usage();
        let mut peak_memory = memory_start;
        
        let start = Instant::now();
        
        for _ in 0..iterations {
            match operation {
                "conversion" => {
                    let _ = self.benchmark_conversion_speed("rtf", "md", &[SizeCategory::Medium]).await?;
                }
                _ => {
                    return Err(format!("Unknown operation: {}", operation).into());
                }
            }
            
            let current_memory = self.get_memory_usage();
            if current_memory > peak_memory {
                peak_memory = current_memory;
            }
        }
        
        let duration = start.elapsed();
        
        Ok(Measurement {
            iteration: 0,
            duration_ms: duration.as_secs_f64() * 1000.0,
            memory_bytes: peak_memory.saturating_sub(memory_start),
            throughput_mbps: None,
            custom_metrics: HashMap::from([
                ("peak_memory_mb".to_string(), (peak_memory as f64) / 1_048_576.0),
                ("iterations".to_string(), iterations as f64),
            ]),
        })
    }
    
    /// Benchmark throughput
    async fn benchmark_throughput(
        &self,
        operation: &str,
        duration_seconds: u64,
    ) -> Result<Measurement, Box<dyn std::error::Error>> {
        let start = Instant::now();
        let target_duration = Duration::from_secs(duration_seconds);
        let mut operations_completed = 0u64;
        let mut total_bytes = 0u64;
        
        while start.elapsed() < target_duration {
            match operation {
                "conversion" => {
                    if let Ok(measurement) = self.benchmark_conversion_speed("rtf", "md", &[SizeCategory::Small]).await {
                        operations_completed += 1;
                        if let Some(throughput) = measurement.throughput_mbps {
                            total_bytes += (throughput * 1_048_576.0 * (measurement.duration_ms / 1000.0)) as u64;
                        }
                    }
                }
                _ => {
                    return Err(format!("Unknown operation: {}", operation).into());
                }
            }
        }
        
        let actual_duration = start.elapsed();
        let throughput_mbps = (total_bytes as f64 / actual_duration.as_secs_f64()) / 1_048_576.0;
        
        Ok(Measurement {
            iteration: 0,
            duration_ms: actual_duration.as_secs_f64() * 1000.0,
            memory_bytes: 0,
            throughput_mbps: Some(throughput_mbps),
            custom_metrics: HashMap::from([
                ("operations_per_second".to_string(), operations_completed as f64 / actual_duration.as_secs_f64()),
                ("total_operations".to_string(), operations_completed as f64),
            ]),
        })
    }
    
    /// Benchmark latency
    async fn benchmark_latency(
        &self,
        operation: &str,
        percentiles: &[f64],
    ) -> Result<Measurement, Box<dyn std::error::Error>> {
        let mut latencies = Vec::new();
        let iterations = 100;
        
        for _ in 0..iterations {
            let start = Instant::now();
            
            match operation {
                "conversion" => {
                    let _ = self.benchmark_conversion_speed("rtf", "md", &[SizeCategory::Tiny]).await?;
                }
                _ => {
                    return Err(format!("Unknown operation: {}", operation).into());
                }
            }
            
            latencies.push(start.elapsed().as_secs_f64() * 1000.0);
        }
        
        latencies.sort_by(|a, b| a.partial_cmp(b).unwrap());
        
        let mut custom_metrics = HashMap::new();
        for percentile in percentiles {
            let index = ((percentile / 100.0) * (latencies.len() as f64)) as usize;
            let value = latencies.get(index.min(latencies.len() - 1)).copied().unwrap_or(0.0);
            custom_metrics.insert(format!("p{}", percentile), value);
        }
        
        Ok(Measurement {
            iteration: 0,
            duration_ms: latencies.iter().sum::<f64>() / latencies.len() as f64,
            memory_bytes: 0,
            throughput_mbps: None,
            custom_metrics,
        })
    }
    
    /// Benchmark concurrent operations
    async fn benchmark_concurrency(
        &self,
        operation: &str,
        thread_counts: &[usize],
    ) -> Result<Measurement, Box<dyn std::error::Error>> {
        let mut results = HashMap::new();
        
        for &thread_count in thread_counts {
            let start = Instant::now();
            let mut handles = Vec::new();
            
            for _ in 0..thread_count {
                let suite = self.clone_suite();
                let op = operation.to_string();
                
                let handle = tokio::spawn(async move {
                    match op.as_str() {
                        "conversion" => {
                            suite.benchmark_conversion_speed("rtf", "md", &[SizeCategory::Small]).await
                        }
                        _ => {
                            Err(format!("Unknown operation: {}", op).into())
                        }
                    }
                });
                
                handles.push(handle);
            }
            
            for handle in handles {
                let _ = handle.await;
            }
            
            let duration = start.elapsed();
            results.insert(
                format!("threads_{}_ms", thread_count),
                duration.as_secs_f64() * 1000.0,
            );
        }
        
        let avg_duration = results.values().sum::<f64>() / results.len() as f64;
        
        Ok(Measurement {
            iteration: 0,
            duration_ms: avg_duration,
            memory_bytes: 0,
            throughput_mbps: None,
            custom_metrics: results,
        })
    }
    
    /// Calculate statistics from measurements
    fn calculate_statistics(&self, measurements: &[Measurement]) -> BenchmarkStatistics {
        let durations: Vec<f64> = measurements.iter().map(|m| m.duration_ms).collect();
        
        if durations.is_empty() {
            return BenchmarkStatistics {
                mean: 0.0,
                median: 0.0,
                std_dev: 0.0,
                min: 0.0,
                max: 0.0,
                percentile_95: 0.0,
                percentile_99: 0.0,
            };
        }
        
        let mean = durations.mean();
        let median = durations.clone().median();
        let std_dev = durations.std_dev();
        let min = durations.min();
        let max = durations.max();
        
        let mut sorted = durations.clone();
        sorted.sort_by(|a, b| a.partial_cmp(b).unwrap());
        
        let p95_index = ((0.95 * sorted.len() as f64) as usize).min(sorted.len() - 1);
        let p99_index = ((0.99 * sorted.len() as f64) as usize).min(sorted.len() - 1);
        
        BenchmarkStatistics {
            mean,
            median,
            std_dev,
            min,
            max,
            percentile_95: sorted[p95_index],
            percentile_99: sorted[p99_index],
        }
    }
    
    /// Compare with baseline
    async fn compare_with_baseline(
        &self,
        benchmark_name: &str,
        current_stats: &BenchmarkStatistics,
    ) -> Option<BaselineComparison> {
        let data_manager = self.data_manager.lock().await;
        let baselines = data_manager.get_by_tags(&["baseline".to_string(), benchmark_name.to_string()]);
        
        if let Some(baseline_item) = baselines.first() {
            if let Ok(content) = fs::read_to_string(&baseline_item.path) {
                if let Ok(baseline_metrics): Result<PerformanceMetrics, _> = serde_json::from_str(&content) {
                    let baseline_mean = baseline_metrics.duration_ms;
                    let current_mean = current_stats.mean;
                    let percent_change = ((current_mean - baseline_mean) / baseline_mean) * 100.0;
                    
                    return Some(BaselineComparison {
                        baseline_mean,
                        current_mean,
                        percent_change,
                        regression_detected: percent_change > 10.0,
                        improvement_detected: percent_change < -10.0,
                    });
                }
            }
        }
        
        None
    }
    
    /// Get memory usage
    fn get_memory_usage(&self) -> u64 {
        #[cfg(target_os = "linux")]
        {
            if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
                for line in status.lines() {
                    if line.starts_with("VmRSS:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<u64>() {
                                return kb * 1024;
                            }
                        }
                    }
                }
            }
        }
        
        0
    }
    
    /// Get environment information
    fn get_environment_info(&self) -> EnvironmentInfo {
        EnvironmentInfo {
            cpu_model: sys_info::cpu_num().map(|n| format!("{} cores", n)).unwrap_or_else(|_| "Unknown".to_string()),
            cpu_cores: num_cpus::get(),
            memory_gb: sys_info::mem_info().map(|m| m.total as f64 / 1_048_576.0).unwrap_or(0.0),
            os: format!("{} {}", sys_info::os_type().unwrap_or_else(|_| "Unknown".to_string()),
                        sys_info::os_release().unwrap_or_else(|_| "".to_string())),
            rust_version: env!("CARGO_PKG_RUST_VERSION").to_string(),
        }
    }
    
    /// Clone the suite for parallel execution
    fn clone_suite(&self) -> Self {
        Self {
            configs: self.configs.clone(),
            data_manager: self.data_manager.clone(),
            conversion_engine: self.conversion_engine.clone(),
            results: self.results.clone(),
        }
    }
    
    /// Export results to various formats
    pub async fn export_results(&self, format: &str, path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        let results = self.results.lock().await;
        
        match format {
            "json" => {
                let json = serde_json::to_string_pretty(&*results)?;
                fs::write(path, json)?;
            }
            "csv" => {
                let mut csv = String::from("benchmark,mean_ms,median_ms,std_dev,min_ms,max_ms,p95_ms,p99_ms\n");
                for result in results.iter() {
                    csv.push_str(&format!(
                        "{},{:.2},{:.2},{:.2},{:.2},{:.2},{:.2},{:.2}\n",
                        result.benchmark_name,
                        result.statistics.mean,
                        result.statistics.median,
                        result.statistics.std_dev,
                        result.statistics.min,
                        result.statistics.max,
                        result.statistics.percentile_95,
                        result.statistics.percentile_99
                    ));
                }
                fs::write(path, csv)?;
            }
            "html" => {
                let html = self.generate_html_report(&results);
                fs::write(path, html)?;
            }
            _ => {
                return Err(format!("Unsupported export format: {}", format).into());
            }
        }
        
        Ok(())
    }
    
    /// Generate HTML report
    fn generate_html_report(&self, results: &[BenchmarkResult]) -> String {
        let mut html = String::from(r#"<!DOCTYPE html>
<html>
<head>
    <title>LegacyBridge Benchmark Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .improvement { color: green; }
        .regression { color: red; }
        .chart { margin: 20px 0; }
    </style>
</head>
<body>
    <h1>LegacyBridge Benchmark Report</h1>
"#);
        
        html.push_str(&format!("<p>Generated at: {}</p>", Utc::now().format("%Y-%m-%d %H:%M:%S UTC")));
        
        // Summary table
        html.push_str("<h2>Summary</h2><table><tr><th>Benchmark</th><th>Mean (ms)</th><th>Std Dev</th><th>Min</th><th>Max</th><th>Status</th></tr>");
        
        for result in results {
            let status = if let Some(comparison) = &result.baseline_comparison {
                if comparison.regression_detected {
                    format!("<span class='regression'>Regression ({:+.1}%)</span>", comparison.percent_change)
                } else if comparison.improvement_detected {
                    format!("<span class='improvement'>Improvement ({:+.1}%)</span>", comparison.percent_change)
                } else {
                    "OK".to_string()
                }
            } else {
                "No baseline".to_string()
            };
            
            html.push_str(&format!(
                "<tr><td>{}</td><td>{:.2}</td><td>{:.2}</td><td>{:.2}</td><td>{:.2}</td><td>{}</td></tr>",
                result.benchmark_name,
                result.statistics.mean,
                result.statistics.std_dev,
                result.statistics.min,
                result.statistics.max,
                status
            ));
        }
        
        html.push_str("</table>");
        
        // Environment info
        if let Some(result) = results.first() {
            html.push_str("<h2>Environment</h2><ul>");
            html.push_str(&format!("<li>CPU: {} ({} cores)</li>", result.environment.cpu_model, result.environment.cpu_cores));
            html.push_str(&format!("<li>Memory: {:.1} GB</li>", result.environment.memory_gb));
            html.push_str(&format!("<li>OS: {}</li>", result.environment.os));
            html.push_str(&format!("<li>Rust: {}</li>", result.environment.rust_version));
            html.push_str("</ul>");
        }
        
        html.push_str("</body></html>");
        html
    }
}

/// Create default benchmark configurations
pub fn create_default_benchmarks() -> Vec<BenchmarkConfig> {
    vec![
        BenchmarkConfig {
            name: "conversion_speed_rtf_to_md".to_string(),
            description: "Benchmark RTF to Markdown conversion speed".to_string(),
            benchmark_type: BenchmarkType::ConversionSpeed {
                input_format: "rtf".to_string(),
                output_format: "md".to_string(),
                file_sizes: vec![SizeCategory::Small, SizeCategory::Medium, SizeCategory::Large],
            },
            warmup_iterations: 3,
            measurement_iterations: 10,
            tags: vec!["conversion".to_string(), "speed".to_string()],
        },
        BenchmarkConfig {
            name: "memory_usage_conversion".to_string(),
            description: "Benchmark memory usage during conversion".to_string(),
            benchmark_type: BenchmarkType::MemoryUsage {
                operation: "conversion".to_string(),
                iterations: 20,
            },
            warmup_iterations: 2,
            measurement_iterations: 5,
            tags: vec!["memory".to_string()],
        },
        BenchmarkConfig {
            name: "throughput_batch_conversion".to_string(),
            description: "Benchmark conversion throughput".to_string(),
            benchmark_type: BenchmarkType::Throughput {
                operation: "conversion".to_string(),
                duration_seconds: 10,
            },
            warmup_iterations: 1,
            measurement_iterations: 3,
            tags: vec!["throughput".to_string()],
        },
        BenchmarkConfig {
            name: "latency_single_file".to_string(),
            description: "Benchmark single file conversion latency".to_string(),
            benchmark_type: BenchmarkType::Latency {
                operation: "conversion".to_string(),
                percentiles: vec![50.0, 90.0, 95.0, 99.0],
            },
            warmup_iterations: 5,
            measurement_iterations: 1,
            tags: vec!["latency".to_string()],
        },
        BenchmarkConfig {
            name: "concurrency_scaling".to_string(),
            description: "Benchmark concurrent operation scaling".to_string(),
            benchmark_type: BenchmarkType::Concurrency {
                operation: "conversion".to_string(),
                thread_counts: vec![1, 2, 4, 8],
            },
            warmup_iterations: 2,
            measurement_iterations: 3,
            tags: vec!["concurrency".to_string(), "scaling".to_string()],
        },
    ]
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_benchmark_statistics_calculation() {
        let measurements = vec![
            Measurement {
                iteration: 0,
                duration_ms: 100.0,
                memory_bytes: 1000,
                throughput_mbps: Some(10.0),
                custom_metrics: HashMap::new(),
            },
            Measurement {
                iteration: 1,
                duration_ms: 110.0,
                memory_bytes: 1100,
                throughput_mbps: Some(9.5),
                custom_metrics: HashMap::new(),
            },
            Measurement {
                iteration: 2,
                duration_ms: 105.0,
                memory_bytes: 1050,
                throughput_mbps: Some(9.8),
                custom_metrics: HashMap::new(),
            },
        ];
        
        let suite = BenchmarkSuite::new(
            TestDataManager::new(".").unwrap(),
            ConversionEngine::new(),
        );
        
        let stats = suite.calculate_statistics(&measurements);
        
        assert!(stats.mean > 100.0 && stats.mean < 110.0);
        assert!(stats.min >= 100.0);
        assert!(stats.max <= 110.0);
    }
    
    #[test]
    fn test_default_benchmarks_creation() {
        let benchmarks = create_default_benchmarks();
        assert_eq!(benchmarks.len(), 5);
        
        for benchmark in benchmarks {
            assert!(!benchmark.name.is_empty());
            assert!(!benchmark.description.is_empty());
            assert!(benchmark.warmup_iterations > 0);
            assert!(benchmark.measurement_iterations > 0);
        }
    }
}