use std::path::PathBuf;
use std::sync::Arc;
use clap::Args;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::time::Duration;

use crate::conversion::ConversionEngine;
use crate::cli::testing::{
    TestRunner, TestRunnerConfig, TestSuite, TestCase, TestType, TestStatus, 
    TestDataManager, load_test_suites
};
use crate::cli::output::OutputFormatter;

/// Test command arguments
#[derive(Debug, Args)]
pub struct TestCommand {
    /// Test suite or pattern to run
    #[arg(help = "Test suite name, file path, or pattern (e.g., 'integration', 'unit/*', 'test.yaml')")]
    pub suite: Option<String>,
    
    /// Filter tests by tags
    #[arg(short = 't', long, value_delimiter = ',', help = "Filter tests by tags (comma-separated)")]
    pub tags: Option<Vec<String>>,
    
    /// Filter tests by name pattern
    #[arg(short = 'n', long, help = "Filter tests by name pattern (regex)")]
    pub name_pattern: Option<String>,
    
    /// Number of parallel test jobs
    #[arg(short = 'j', long, help = "Number of parallel test jobs (default: CPU count)")]
    pub parallel_jobs: Option<usize>,
    
    /// Continue running tests after failures
    #[arg(short = 'c', long, help = "Continue running tests after failures")]
    pub continue_on_failure: bool,
    
    /// Verbose output
    #[arg(short = 'v', long, help = "Verbose output showing test details")]
    pub verbose: bool,
    
    /// Generate test data before running tests
    #[arg(long, help = "Generate test data before running tests")]
    pub generate_data: bool,
    
    /// Test data directory
    #[arg(long, default_value = "./test-data", help = "Directory for test data")]
    pub data_dir: PathBuf,
    
    /// Export test results
    #[arg(long, value_enum, help = "Export test results to file")]
    pub export: Option<TestExportFormat>,
    
    /// Export file path
    #[arg(long, help = "Path for exported test results")]
    pub export_path: Option<PathBuf>,
    
    /// Compare with baseline
    #[arg(long, help = "Compare performance tests with baseline")]
    pub baseline: bool,
    
    /// Update baseline with current results
    #[arg(long, help = "Update baseline with current test results")]
    pub update_baseline: bool,
    
    /// Test timeout multiplier
    #[arg(long, default_value = "1.0", help = "Multiply test timeouts by this factor")]
    pub timeout_multiplier: f32,
    
    /// Skip memory leak tests
    #[arg(long, help = "Skip memory leak detection tests")]
    pub skip_memory_tests: bool,
    
    /// Test retry count
    #[arg(long, default_value = "2", help = "Number of times to retry failed tests")]
    pub retry_count: u32,
    
    /// CI mode (simplified output for CI systems)
    #[arg(long, help = "CI mode with simplified output")]
    pub ci: bool,
    
    /// List available tests without running
    #[arg(long, help = "List available tests without running them")]
    pub list: bool,
    
    /// Clean test data after completion
    #[arg(long, help = "Clean up test data after completion")]
    pub clean: bool,
}

/// Test export formats
#[derive(Debug, Clone, Copy, ValueEnum)]
pub enum TestExportFormat {
    Json,
    Junit,
    Html,
    Csv,
}

/// Test categories
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TestCategory {
    Unit,
    Integration,
    Format,
    Performance,
    Memory,
    Accuracy,
    All,
}

/// Execute test command
pub async fn execute_test_command(
    args: TestCommand,
    output_format: crate::cli::OutputFormat,
) -> Result<(), Box<dyn std::error::Error>> {
    // Initialize test data manager
    let mut data_manager = TestDataManager::new(&args.data_dir)?;
    
    // Generate test data if requested
    if args.generate_data {
        println!("Generating test data...");
        generate_test_data(&mut data_manager).await?;
        println!("Test data generated successfully.");
    }
    
    // Load test suites
    let test_suites = load_test_suites(&args).await?;
    
    // List tests if requested
    if args.list {
        list_tests(&test_suites, &output_format);
        return Ok(());
    }
    
    // Create test runner configuration
    let config = TestRunnerConfig {
        parallel_jobs: args.parallel_jobs.unwrap_or_else(num_cpus::get),
        timeout_multiplier: args.timeout_multiplier,
        retry_failed: args.retry_count > 0,
        continue_on_failure: args.continue_on_failure,
        verbose: args.verbose,
        capture_metrics: true,
    };
    
    // Create conversion engine
    let conversion_engine = ConversionEngine::new();
    
    // Create test runner
    let runner = TestRunner::new(config, data_manager, conversion_engine);
    
    // Run tests
    println!("Running {} test suites...", test_suites.len());
    let mut all_results = Vec::new();
    
    for suite in test_suites {
        let suite_results = runner.run_suite(&suite).await;
        all_results.extend(suite_results);
    }
    
    // Generate report
    let report = runner.generate_report().await;
    
    // Display results
    display_test_results(&report, &output_format, args.ci);
    
    // Export results if requested
    if let Some(export_format) = args.export {
        let export_path = args.export_path.unwrap_or_else(|| {
            PathBuf::from(format!("test-results-{}.{}", 
                chrono::Utc::now().format("%Y%m%d-%H%M%S"),
                match export_format {
                    TestExportFormat::Json => "json",
                    TestExportFormat::Junit => "xml",
                    TestExportFormat::Html => "html",
                    TestExportFormat::Csv => "csv",
                }
            ))
        });
        
        export_test_results(&report, export_format, &export_path)?;
        println!("Test results exported to: {:?}", export_path);
    }
    
    // Update baseline if requested
    if args.update_baseline {
        update_baseline_from_results(&runner, &report).await?;
        println!("Baseline updated with current test results.");
    }
    
    // Clean test data if requested
    if args.clean {
        let mut data_manager = TestDataManager::new(&args.data_dir)?;
        let removed = data_manager.cleanup_old_data(0)?;
        println!("Cleaned up {} test data items.", removed);
    }
    
    // Exit with appropriate code
    if report.failed > 0 || report.errors > 0 {
        std::process::exit(1);
    }
    
    Ok(())
}

/// Generate test data
async fn generate_test_data(data_manager: &mut TestDataManager) -> Result<(), Box<dyn std::error::Error>> {
    // Generate sample files for each format
    let formats = vec!["rtf", "doc", "wpd", "txt", "md"];
    
    for format in formats {
        println!("  Generating {} samples...", format);
        data_manager.generate_sample_files(format, 3)?;
    }
    
    Ok(())
}

/// Load test suites based on command arguments
async fn load_test_suites(args: &TestCommand) -> Result<Vec<TestSuite>, Box<dyn std::error::Error>> {
    let mut all_suites = Vec::new();
    
    if let Some(suite_spec) = &args.suite {
        // Load specific suite or pattern
        if suite_spec.ends_with(".yaml") || suite_spec.ends_with(".yml") || suite_spec.ends_with(".json") {
            // Load from file
            let path = PathBuf::from(suite_spec);
            all_suites = crate::cli::testing::load_test_suites(&path).await?;
        } else {
            // Load built-in suites
            all_suites = load_builtin_suites(suite_spec)?;
        }
    } else {
        // Load all built-in suites
        all_suites = load_all_builtin_suites()?;
    }
    
    // Filter by tags if specified
    if let Some(tags) = &args.tags {
        all_suites = all_suites.into_iter()
            .filter(|suite| tags.iter().any(|tag| suite.tags.contains(tag)))
            .collect();
    }
    
    // Filter tests by name pattern if specified
    if let Some(pattern) = &args.name_pattern {
        let regex = regex::Regex::new(pattern)?;
        for suite in &mut all_suites {
            suite.test_cases.retain(|test| regex.is_match(&test.name));
        }
    }
    
    // Skip memory tests if requested
    if args.skip_memory_tests {
        for suite in &mut all_suites {
            suite.test_cases.retain(|test| {
                !matches!(test.test_type, TestType::MemoryLeak { .. })
            });
        }
    }
    
    // Update retry count
    for suite in &mut all_suites {
        for test in &mut suite.test_cases {
            test.retry_count = args.retry_count;
        }
    }
    
    Ok(all_suites)
}

/// Load built-in test suites
fn load_builtin_suites(pattern: &str) -> Result<Vec<TestSuite>, Box<dyn std::error::Error>> {
    let mut suites = Vec::new();
    
    match pattern {
        "unit" => suites.push(create_unit_test_suite()),
        "integration" => suites.push(create_integration_test_suite()),
        "format" => suites.push(create_format_test_suite()),
        "performance" => suites.push(create_performance_test_suite()),
        "memory" => suites.push(create_memory_test_suite()),
        "accuracy" => suites.push(create_accuracy_test_suite()),
        _ => return Err(format!("Unknown test suite pattern: {}", pattern).into()),
    }
    
    Ok(suites)
}

/// Load all built-in test suites
fn load_all_builtin_suites() -> Result<Vec<TestSuite>, Box<dyn std::error::Error>> {
    Ok(vec![
        create_unit_test_suite(),
        create_integration_test_suite(),
        create_format_test_suite(),
        create_performance_test_suite(),
        create_memory_test_suite(),
        create_accuracy_test_suite(),
    ])
}

/// Create unit test suite
fn create_unit_test_suite() -> TestSuite {
    TestSuite {
        id: Uuid::new_v4(),
        name: "Unit Tests".to_string(),
        description: "Unit tests for individual components".to_string(),
        test_cases: vec![
            TestCase {
                id: Uuid::new_v4(),
                name: "Format detection unit test".to_string(),
                test_type: TestType::Unit {
                    component: "formats".to_string(),
                    function: "detect_format".to_string(),
                },
                tags: vec!["unit".to_string(), "formats".to_string()],
                timeout: Duration::from_secs(5),
                retry_count: 0,
            },
            TestCase {
                id: Uuid::new_v4(),
                name: "Parser unit test".to_string(),
                test_type: TestType::Unit {
                    component: "parsers".to_string(),
                    function: "parse_rtf".to_string(),
                },
                tags: vec!["unit".to_string(), "parsers".to_string()],
                timeout: Duration::from_secs(5),
                retry_count: 0,
            },
        ],
        tags: vec!["unit".to_string()],
    }
}

/// Create integration test suite
fn create_integration_test_suite() -> TestSuite {
    let mut test_cases = Vec::new();
    
    // Generate integration tests for common format conversions
    let conversions = vec![
        ("rtf", "md"),
        ("doc", "md"),
        ("wpd", "txt"),
        ("rtf", "html"),
    ];
    
    for (input, output) in conversions {
        test_cases.push(TestCase {
            id: Uuid::new_v4(),
            name: format!("{} to {} conversion", input, output),
            test_type: TestType::Integration {
                input_format: input.to_string(),
                output_format: output.to_string(),
            },
            tags: vec!["integration".to_string(), input.to_string(), output.to_string()],
            timeout: Duration::from_secs(30),
            retry_count: 1,
        });
    }
    
    TestSuite {
        id: Uuid::new_v4(),
        name: "Integration Tests".to_string(),
        description: "End-to-end conversion pipeline tests".to_string(),
        test_cases,
        tags: vec!["integration".to_string()],
    }
}

/// Create format validation test suite
fn create_format_test_suite() -> TestSuite {
    let formats = vec!["rtf", "doc", "wpd", "txt", "md"];
    let mut test_cases = Vec::new();
    
    for format in formats {
        test_cases.push(TestCase {
            id: Uuid::new_v4(),
            name: format!("{} format validation", format),
            test_type: TestType::FormatValidation {
                format: format.to_string(),
            },
            tags: vec!["format".to_string(), format.to_string()],
            timeout: Duration::from_secs(10),
            retry_count: 0,
        });
    }
    
    TestSuite {
        id: Uuid::new_v4(),
        name: "Format Validation Tests".to_string(),
        description: "Tests for format detection and validation".to_string(),
        test_cases,
        tags: vec!["format".to_string()],
    }
}

/// Create performance test suite
fn create_performance_test_suite() -> TestSuite {
    TestSuite {
        id: Uuid::new_v4(),
        name: "Performance Tests".to_string(),
        description: "Performance regression and benchmark tests".to_string(),
        test_cases: vec![
            TestCase {
                id: Uuid::new_v4(),
                name: "Conversion performance regression".to_string(),
                test_type: TestType::PerformanceRegression {
                    operation: "conversion".to_string(),
                    baseline_id: None,
                },
                tags: vec!["performance".to_string(), "regression".to_string()],
                timeout: Duration::from_secs(60),
                retry_count: 2,
            },
        ],
        tags: vec!["performance".to_string()],
    }
}

/// Create memory test suite
fn create_memory_test_suite() -> TestSuite {
    TestSuite {
        id: Uuid::new_v4(),
        name: "Memory Tests".to_string(),
        description: "Memory leak detection tests".to_string(),
        test_cases: vec![
            TestCase {
                id: Uuid::new_v4(),
                name: "Conversion memory leak test".to_string(),
                test_type: TestType::MemoryLeak {
                    operation: "conversion".to_string(),
                    iterations: 50,
                },
                tags: vec!["memory".to_string(), "leak".to_string()],
                timeout: Duration::from_secs(120),
                retry_count: 0,
            },
        ],
        tags: vec!["memory".to_string()],
    }
}

/// Create accuracy test suite
fn create_accuracy_test_suite() -> TestSuite {
    // This would normally load from test data directory
    TestSuite {
        id: Uuid::new_v4(),
        name: "Accuracy Tests".to_string(),
        description: "Conversion accuracy validation tests".to_string(),
        test_cases: vec![],
        tags: vec!["accuracy".to_string()],
    }
}

/// List available tests
fn list_tests(suites: &[TestSuite], output_format: &OutputFormatter) {
    println!("Available test suites:");
    println!();
    
    for suite in suites {
        println!("Suite: {} ({} tests)", suite.name, suite.test_cases.len());
        println!("  Description: {}", suite.description);
        println!("  Tags: {}", suite.tags.join(", "));
        
        if !suite.test_cases.is_empty() {
            println!("  Tests:");
            for test in &suite.test_cases {
                println!("    - {} [{}]", test.name, test.tags.join(", "));
            }
        }
        println!();
    }
}

/// Display test results
fn display_test_results(
    report: &crate::cli::testing::TestReport,
    output_format: &OutputFormatter,
    ci_mode: bool,
) {
    if ci_mode {
        // Simplified CI output
        println!("Test Results: {} total, {} passed, {} failed, {} skipped",
            report.total_tests, report.passed, report.failed, report.skipped);
        
        if report.failed > 0 || report.errors > 0 {
            println!("\nFailed tests:");
            for result in &report.results {
                if result.status == TestStatus::Failed || result.status == TestStatus::Error {
                    println!("  - {}: {}", result.test_name, 
                        result.error_message.as_ref().unwrap_or(&"Unknown error".to_string()));
                }
            }
        }
        
        return;
    }
    
    // Detailed output
    println!("\n{}", "=".repeat(80));
    println!("Test Execution Report");
    println!("{}", "=".repeat(80));
    
    println!("\nSummary:");
    println!("  Total tests:    {}", report.total_tests);
    println!("  Passed:         {} ({:.1}%)", report.passed, report.pass_rate());
    println!("  Failed:         {}", report.failed);
    println!("  Errors:         {}", report.errors);
    println!("  Skipped:        {}", report.skipped);
    println!("  Timeouts:       {}", report.timeouts);
    println!("  Total duration: {:.2}s", report.total_duration.as_secs_f64());
    println!("  Avg duration:   {:.2}s", report.average_duration.as_secs_f64());
    
    if report.failed > 0 || report.errors > 0 {
        println!("\nFailed/Error Tests:");
        for result in &report.results {
            if result.status == TestStatus::Failed || result.status == TestStatus::Error {
                println!("\n  Test: {}", result.test_name);
                println!("  Status: {:?}", result.status);
                println!("  Duration: {:.2}s", result.duration.as_secs_f64());
                if let Some(error) = &result.error_message {
                    println!("  Error: {}", error);
                }
            }
        }
    }
    
    if report.timeouts > 0 {
        println!("\nTimed Out Tests:");
        for result in &report.results {
            if result.status == TestStatus::Timeout {
                println!("  - {} (timeout after {:.2}s)", result.test_name, result.duration.as_secs_f64());
            }
        }
    }
    
    // Performance metrics if available
    let perf_tests: Vec<_> = report.results.iter()
        .filter(|r| r.metrics.is_some())
        .collect();
    
    if !perf_tests.is_empty() {
        println!("\nPerformance Metrics:");
        for result in perf_tests {
            if let Some(metrics) = &result.metrics {
                println!("  {}: {:.2}ms, {:.2}MB, {:.2}MB/s", 
                    result.test_name,
                    metrics.duration_ms,
                    metrics.memory_bytes as f64 / 1_048_576.0,
                    metrics.throughput_mbps
                );
            }
        }
    }
}

/// Export test results
fn export_test_results(
    report: &crate::cli::testing::TestReport,
    format: TestExportFormat,
    path: &std::path::Path,
) -> Result<(), Box<dyn std::error::Error>> {
    match format {
        TestExportFormat::Json => {
            let json = report.to_json()?;
            std::fs::write(path, json)?;
        }
        TestExportFormat::Junit => {
            let xml = report.to_junit_xml();
            std::fs::write(path, xml)?;
        }
        TestExportFormat::Html => {
            let html = generate_html_report(report);
            std::fs::write(path, html)?;
        }
        TestExportFormat::Csv => {
            let csv = generate_csv_report(report);
            std::fs::write(path, csv)?;
        }
    }
    
    Ok(())
}

/// Generate HTML test report
fn generate_html_report(report: &crate::cli::testing::TestReport) -> String {
    let mut html = String::from(r#"<!DOCTYPE html>
<html>
<head>
    <title>LegacyBridge Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .passed { color: green; }
        .failed { color: red; }
        .skipped { color: orange; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>LegacyBridge Test Report</h1>
"#);
    
    html.push_str(&format!("<p>Generated: {}</p>", report.generated_at.format("%Y-%m-%d %H:%M:%S UTC")));
    
    // Summary
    html.push_str(&format!(r#"
    <div class="summary">
        <h2>Summary</h2>
        <p>Total: {} | <span class="passed">Passed: {} ({:.1}%)</span> | 
           <span class="failed">Failed: {}</span> | Errors: {} | 
           <span class="skipped">Skipped: {}</span></p>
        <p>Duration: {:.2}s (avg: {:.2}s)</p>
    </div>
    "#, 
        report.total_tests, report.passed, report.pass_rate(),
        report.failed, report.errors, report.skipped,
        report.total_duration.as_secs_f64(), report.average_duration.as_secs_f64()
    ));
    
    // Test results table
    html.push_str("<h2>Test Results</h2><table><tr><th>Test</th><th>Status</th><th>Duration</th><th>Details</th></tr>");
    
    for result in &report.results {
        let status_class = match result.status {
            TestStatus::Passed => "passed",
            TestStatus::Failed => "failed",
            TestStatus::Skipped => "skipped",
            _ => "failed",
        };
        
        html.push_str(&format!(
            "<tr><td>{}</td><td class='{}'>{:?}</td><td>{:.2}s</td><td>{}</td></tr>",
            result.test_name,
            status_class,
            result.status,
            result.duration.as_secs_f64(),
            result.error_message.as_ref().unwrap_or(&"".to_string())
        ));
    }
    
    html.push_str("</table></body></html>");
    html
}

/// Generate CSV test report
fn generate_csv_report(report: &crate::cli::testing::TestReport) -> String {
    let mut csv = String::from("test_name,status,duration_ms,error_message\n");
    
    for result in &report.results {
        csv.push_str(&format!(
            "{},{:?},{:.2},{}\n",
            result.test_name,
            result.status,
            result.duration.as_secs_f64() * 1000.0,
            result.error_message.as_ref().unwrap_or(&"".to_string()).replace(',', ";")
        ));
    }
    
    csv
}

/// Update baseline from test results
async fn update_baseline_from_results(
    runner: &TestRunner,
    report: &crate::cli::testing::TestReport,
) -> Result<(), Box<dyn std::error::Error>> {
    // This would update baseline metrics in the test data manager
    // Implementation depends on specific baseline storage strategy
    
    println!("Baseline update not yet implemented");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_create_unit_test_suite() {
        let suite = create_unit_test_suite();
        assert!(!suite.test_cases.is_empty());
        assert!(suite.tags.contains(&"unit".to_string()));
    }
    
    #[test]
    fn test_create_integration_test_suite() {
        let suite = create_integration_test_suite();
        assert!(!suite.test_cases.is_empty());
        
        for test in &suite.test_cases {
            assert!(matches!(test.test_type, TestType::Integration { .. }));
        }
    }
}