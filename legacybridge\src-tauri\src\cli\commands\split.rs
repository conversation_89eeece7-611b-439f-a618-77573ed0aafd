use clap::Args;
use std::path::PathBuf;
use crate::cli::output::{OutputFormat, OutputFormatter};
use crate::conversion::{ConversionEngine, ConversionOptions};
use crate::formats::FormatDetector;
use serde::{Serialize, Deserialize};
use indicatif::{ProgressBar, ProgressStyle};
use tokio::fs;
use tokio::io::{AsyncReadExt, AsyncWriteExt};

#[derive(Args, Debug)]
pub struct SplitCommand {
    /// Input file to split
    #[arg(required = true)]
    pub input: PathBuf,
    
    /// Output directory for split files
    #[arg(short, long)]
    pub output_dir: Option<PathBuf>,
    
    /// Split by size in bytes (e.g., 1MB, 500KB)
    #[arg(short, long, value_parser = parse_size)]
    pub size: Option<u64>,
    
    /// Split by number of lines
    #[arg(short, long)]
    pub lines: Option<usize>,
    
    /// Split by number of pages (estimated)
    #[arg(short = 'p', long)]
    pub pages: Option<usize>,
    
    /// Split into N equal parts
    #[arg(short = 'n', long)]
    pub parts: Option<usize>,
    
    /// Output format (default: same as input)
    #[arg(short = 'f', long)]
    pub format: Option<String>,
    
    /// Prefix for output files
    #[arg(long, default_value = "split")]
    pub prefix: String,
    
    /// Include headers in each split file
    #[arg(short = 'H', long)]
    pub headers: bool,
    
    /// Force overwrite existing files
    #[arg(long)]
    pub force: bool,
    
    /// Output format for results
    #[arg(long, value_enum, default_value = "table")]
    pub output_format: OutputFormat,
    
    /// Verbose output
    #[arg(short, long)]
    pub verbose: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SplitResult {
    pub part: usize,
    pub filename: String,
    pub size: u64,
    pub lines: Option<usize>,
    pub status: String,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SplitSummary {
    pub input_file: String,
    pub input_size: u64,
    pub split_method: String,
    pub total_parts: usize,
    pub successful: usize,
    pub failed: usize,
    pub parts: Vec<SplitResult>,
}

pub async fn execute(cmd: SplitCommand) -> Result<(), Box<dyn std::error::Error>> {
    // Validate input
    if !cmd.input.exists() {
        return Err(format!("Input file '{}' does not exist", cmd.input.display()).into());
    }
    
    // Validate split options
    let split_options = [
        cmd.size.is_some(),
        cmd.lines.is_some(),
        cmd.pages.is_some(),
        cmd.parts.is_some(),
    ];
    
    let active_options = split_options.iter().filter(|&&x| x).count();
    if active_options == 0 {
        return Err("Must specify one of: --size, --lines, --pages, or --parts".into());
    }
    if active_options > 1 {
        return Err("Cannot specify multiple split methods".into());
    }
    
    // Determine output directory
    let output_dir = cmd.output_dir.clone().unwrap_or_else(|| {
        cmd.input.parent().unwrap_or(&PathBuf::from(".")).to_path_buf()
    });
    
    // Create output directory if needed
    fs::create_dir_all(&output_dir).await?;
    
    // Get file info
    let metadata = fs::metadata(&cmd.input).await?;
    let input_size = metadata.len();
    
    // Determine split method
    let split_method = if cmd.size.is_some() {
        format!("by size ({} bytes)", cmd.size.unwrap())
    } else if cmd.lines.is_some() {
        format!("by lines ({})", cmd.lines.unwrap())
    } else if cmd.pages.is_some() {
        format!("by pages ({})", cmd.pages.unwrap())
    } else {
        format!("into {} parts", cmd.parts.unwrap())
    };
    
    if cmd.verbose {
        println!("Splitting {} ({})", cmd.input.display(), split_method);
    }
    
    // Perform split
    let results = if cmd.size.is_some() {
        split_by_size(&cmd, &output_dir, input_size).await?
    } else if cmd.lines.is_some() {
        split_by_lines(&cmd, &output_dir).await?
    } else if cmd.pages.is_some() {
        split_by_pages(&cmd, &output_dir).await?
    } else {
        split_into_parts(&cmd, &output_dir, input_size).await?
    };
    
    // Generate summary
    let successful = results.iter().filter(|r| r.status == "success").count();
    let failed = results.len() - successful;
    
    let summary = SplitSummary {
        input_file: cmd.input.display().to_string(),
        input_size,
        split_method,
        total_parts: results.len(),
        successful,
        failed,
        parts: results,
    };
    
    // Output results
    let formatter = OutputFormatter::new(cmd.output_format);
    formatter.print_split_summary(&summary)?;
    
    Ok(())
}

async fn split_by_size(
    cmd: &SplitCommand,
    output_dir: &PathBuf,
    input_size: u64,
) -> Result<Vec<SplitResult>, Box<dyn std::error::Error>> {
    let chunk_size = cmd.size.unwrap();
    let total_parts = ((input_size as f64) / (chunk_size as f64)).ceil() as usize;
    
    let pb = create_progress_bar(total_parts, cmd.verbose)?;
    let mut results = Vec::new();
    
    let mut file = tokio::fs::File::open(&cmd.input).await?;
    let mut buffer = vec![0u8; chunk_size as usize];
    
    for part in 0..total_parts {
        if let Some(ref pb) = pb {
            pb.set_message(format!("Writing part {}/{}", part + 1, total_parts));
        }
        
        let output_path = generate_output_path(output_dir, &cmd.prefix, part + 1, &cmd.input)?;
        
        if output_path.exists() && !cmd.force {
            results.push(SplitResult {
                part: part + 1,
                filename: output_path.display().to_string(),
                size: 0,
                lines: None,
                status: "skipped".to_string(),
                error: Some("File exists".to_string()),
            });
            continue;
        }
        
        match write_chunk(&mut file, &output_path, &mut buffer).await {
            Ok(bytes_written) => {
                results.push(SplitResult {
                    part: part + 1,
                    filename: output_path.display().to_string(),
                    size: bytes_written,
                    lines: None,
                    status: "success".to_string(),
                    error: None,
                });
            }
            Err(e) => {
                results.push(SplitResult {
                    part: part + 1,
                    filename: output_path.display().to_string(),
                    size: 0,
                    lines: None,
                    status: "failed".to_string(),
                    error: Some(e.to_string()),
                });
            }
        }
        
        if let Some(ref pb) = pb {
            pb.inc(1);
        }
    }
    
    if let Some(pb) = pb {
        pb.finish_with_message("Split complete");
    }
    
    Ok(results)
}

async fn split_by_lines(
    cmd: &SplitCommand,
    output_dir: &PathBuf,
) -> Result<Vec<SplitResult>, Box<dyn std::error::Error>> {
    let lines_per_file = cmd.lines.unwrap();
    let content = fs::read_to_string(&cmd.input).await?;
    let all_lines: Vec<&str> = content.lines().collect();
    let total_parts = (all_lines.len() as f64 / lines_per_file as f64).ceil() as usize;
    
    let pb = create_progress_bar(total_parts, cmd.verbose)?;
    let mut results = Vec::new();
    
    for part in 0..total_parts {
        if let Some(ref pb) = pb {
            pb.set_message(format!("Writing part {}/{}", part + 1, total_parts));
        }
        
        let start = part * lines_per_file;
        let end = ((part + 1) * lines_per_file).min(all_lines.len());
        let chunk_lines = &all_lines[start..end];
        
        let output_path = generate_output_path(output_dir, &cmd.prefix, part + 1, &cmd.input)?;
        
        if output_path.exists() && !cmd.force {
            results.push(SplitResult {
                part: part + 1,
                filename: output_path.display().to_string(),
                size: 0,
                lines: Some(0),
                status: "skipped".to_string(),
                error: Some("File exists".to_string()),
            });
            continue;
        }
        
        let chunk_content = chunk_lines.join("\n");
        match fs::write(&output_path, &chunk_content).await {
            Ok(_) => {
                results.push(SplitResult {
                    part: part + 1,
                    filename: output_path.display().to_string(),
                    size: chunk_content.len() as u64,
                    lines: Some(chunk_lines.len()),
                    status: "success".to_string(),
                    error: None,
                });
            }
            Err(e) => {
                results.push(SplitResult {
                    part: part + 1,
                    filename: output_path.display().to_string(),
                    size: 0,
                    lines: Some(0),
                    status: "failed".to_string(),
                    error: Some(e.to_string()),
                });
            }
        }
        
        if let Some(ref pb) = pb {
            pb.inc(1);
        }
    }
    
    if let Some(pb) = pb {
        pb.finish_with_message("Split complete");
    }
    
    Ok(results)
}

async fn split_by_pages(
    cmd: &SplitCommand,
    output_dir: &PathBuf,
) -> Result<Vec<SplitResult>, Box<dyn std::error::Error>> {
    // For text files, estimate ~250 words per page, ~5 characters per word
    let chars_per_page = 250 * 5;
    let pages_per_file = cmd.pages.unwrap();
    let chars_per_file = chars_per_page * pages_per_file;
    
    // Convert to lines (estimate ~80 chars per line)
    let lines_per_file = (chars_per_file / 80).max(1);
    
    // Use line-based splitting with page estimation
    let mut modified_cmd = cmd.clone();
    modified_cmd.lines = Some(lines_per_file);
    
    split_by_lines(&modified_cmd, output_dir).await
}

async fn split_into_parts(
    cmd: &SplitCommand,
    output_dir: &PathBuf,
    input_size: u64,
) -> Result<Vec<SplitResult>, Box<dyn std::error::Error>> {
    let num_parts = cmd.parts.unwrap();
    let chunk_size = (input_size / num_parts as u64).max(1);
    
    // Use size-based splitting
    let mut modified_cmd = cmd.clone();
    modified_cmd.size = Some(chunk_size);
    
    split_by_size(&modified_cmd, output_dir, input_size).await
}

async fn write_chunk(
    file: &mut tokio::fs::File,
    output_path: &PathBuf,
    buffer: &mut [u8],
) -> Result<u64, Box<dyn std::error::Error>> {
    let bytes_read = file.read(buffer).await?;
    if bytes_read > 0 {
        let mut output = tokio::fs::File::create(output_path).await?;
        output.write_all(&buffer[..bytes_read]).await?;
        Ok(bytes_read as u64)
    } else {
        Ok(0)
    }
}

fn generate_output_path(
    output_dir: &PathBuf,
    prefix: &str,
    part: usize,
    input_file: &PathBuf,
) -> Result<PathBuf, Box<dyn std::error::Error>> {
    let extension = input_file.extension()
        .and_then(|e| e.to_str())
        .unwrap_or("txt");
    
    let filename = format!("{}_{:04}.{}", prefix, part, extension);
    Ok(output_dir.join(filename))
}

fn create_progress_bar(
    total: usize,
    verbose: bool,
) -> Result<Option<ProgressBar>, Box<dyn std::error::Error>> {
    if verbose {
        Ok(None)
    } else {
        let pb = ProgressBar::new(total as u64);
        pb.set_style(
            ProgressStyle::default_bar()
                .template("[{elapsed_precise}] {bar:40.cyan/blue} {pos}/{len} {msg}")?
                .progress_chars("=>-")
        );
        Ok(Some(pb))
    }
}

fn parse_size(s: &str) -> Result<u64, String> {
    let s = s.to_uppercase();
    
    if let Ok(bytes) = s.parse::<u64>() {
        return Ok(bytes);
    }
    
    let (number_part, unit_part) = s.split_at(
        s.find(|c: char| c.is_alphabetic())
            .ok_or("Invalid size format")?
    );
    
    let number: f64 = number_part.parse()
        .map_err(|_| "Invalid number")?;
    
    let multiplier = match unit_part {
        "B" => 1,
        "KB" | "K" => 1_024,
        "MB" | "M" => 1_024 * 1_024,
        "GB" | "G" => 1_024 * 1_024 * 1_024,
        _ => return Err(format!("Unknown unit: {}", unit_part)),
    };
    
    Ok((number * multiplier as f64) as u64)
}

impl OutputFormatter {
    pub fn print_split_summary(&self, summary: &SplitSummary) -> Result<(), Box<dyn std::error::Error>> {
        match self.format {
            OutputFormat::Json => {
                println!("{}", serde_json::to_string_pretty(summary)?);
            }
            OutputFormat::Table => {
                println!("\nSplit Summary:");
                println!("─────────────");
                println!("Input file:   {}", summary.input_file);
                println!("Input size:   {} bytes", summary.input_size);
                println!("Split method: {}", summary.split_method);
                println!("Total parts:  {}", summary.total_parts);
                println!("Successful:   {}", summary.successful);
                println!("Failed:       {}", summary.failed);
                
                if !summary.parts.is_empty() {
                    println!("\nPart Details:");
                    println!("{:<6} {:<50} {:<12} {:<10}", "Part", "Filename", "Size", "Status");
                    println!("{}", "─".repeat(80));
                    
                    for part in &summary.parts {
                        let status = match part.status.as_str() {
                            "success" => "✓",
                            "failed" => "✗",
                            _ => "⚠",
                        };
                        println!("{:<6} {:<50} {:<12} {:<10}",
                            part.part,
                            part.filename,
                            format!("{} bytes", part.size),
                            status
                        );
                    }
                }
            }
            _ => {
                // CSV and Plain formats
                println!("Part,Filename,Size,Status");
                for part in &summary.parts {
                    println!("{},{},{},{}",
                        part.part,
                        part.filename,
                        part.size,
                        part.status
                    );
                }
            }
        }
        Ok(())
    }
}

// Clone implementation for SplitCommand (needed for internal use)
impl Clone for SplitCommand {
    fn clone(&self) -> Self {
        Self {
            input: self.input.clone(),
            output_dir: self.output_dir.clone(),
            size: self.size,
            lines: self.lines,
            pages: self.pages,
            parts: self.parts,
            format: self.format.clone(),
            prefix: self.prefix.clone(),
            headers: self.headers,
            force: self.force,
            output_format: self.output_format,
            verbose: self.verbose,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::io::Write;
    
    #[test]
    fn test_parse_size() {
        assert_eq!(parse_size("1024").unwrap(), 1024);
        assert_eq!(parse_size("1KB").unwrap(), 1024);
        assert_eq!(parse_size("1k").unwrap(), 1024);
        assert_eq!(parse_size("2MB").unwrap(), 2 * 1024 * 1024);
        assert_eq!(parse_size("1.5GB").unwrap(), (1.5 * 1024.0 * 1024.0 * 1024.0) as u64);
    }
    
    #[tokio::test]
    async fn test_split_by_lines() {
        let temp_dir = TempDir::new().unwrap();
        let input_file = temp_dir.path().join("test.txt");
        
        let content = "Line 1\nLine 2\nLine 3\nLine 4\nLine 5";
        std::fs::File::create(&input_file).unwrap().write_all(content.as_bytes()).unwrap();
        
        let cmd = SplitCommand {
            input: input_file,
            output_dir: Some(temp_dir.path().to_path_buf()),
            size: None,
            lines: Some(2),
            pages: None,
            parts: None,
            format: None,
            prefix: "test".to_string(),
            headers: false,
            force: false,
            output_format: OutputFormat::Json,
            verbose: false,
        };
        
        let result = execute(cmd).await;
        assert!(result.is_ok());
        
        // Check that split files were created
        assert!(temp_dir.path().join("test_0001.txt").exists());
        assert!(temp_dir.path().join("test_0002.txt").exists());
        assert!(temp_dir.path().join("test_0003.txt").exists());
    }
}