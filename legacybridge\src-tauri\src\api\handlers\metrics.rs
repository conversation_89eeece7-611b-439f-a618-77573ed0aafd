//! Metrics endpoint handler
//!
//! This module provides the HTTP endpoint for Prometheus metrics

use axum::{
    http::{header, StatusCode},
    response::IntoResponse,
};

/// Metrics endpoint handler (Prometheus format)
pub async fn metrics_handler() -> impl IntoResponse {
    match crate::api::metrics::metrics_handler().await {
        Ok(metrics) => (
            StatusCode::OK,
            [(header::CONTENT_TYPE, "text/plain; version=0.0.4")],
            metrics,
        ).into_response(),
        Err(status) => (status, "Failed to collect metrics").into_response(),
    }
}