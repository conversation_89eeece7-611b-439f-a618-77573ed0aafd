pub mod handlers;
pub mod middleware;
pub mod models;
pub mod websocket;
pub mod docs;
pub mod metrics;

use axum::{
    routing::{get, post},
    Router,
};
use crate::api::handlers::convert::AppState;
use crate::conversion::ConversionEngine;
use crate::formats::FormatManager;
use std::path::PathBuf;
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::{
    compression::CompressionLayer,
    limit::RequestBodyLimitLayer,
    trace::TraceLayer,
};

/// API server configuration
#[derive(Clone, Debug)]
pub struct Config {
    pub api_key: Option<String>,
    pub max_body_size: usize,
    pub enable_cors: bool,
    pub cors_origins: Vec<String>,
    pub enable_websocket: bool,
    pub enable_upload: bool,
    pub static_dir: Option<PathBuf>,
    pub enable_docs: bool,
    pub enable_metrics: bool,
    pub rate_limit: Option<u32>,
    pub ssl_cert: Option<PathBuf>,
    pub ssl_key: Option<PathBuf>,
}

/// Build the API router with all endpoints and middleware
pub fn build_router(config: Config) -> anyhow::Result<Router> {
    // Create the main API router
    let mut app = Router::new()
        // Health check endpoint
        .route("/health", get(handlers::health::health_check))
        // Server info endpoint
        .route("/info", get(handlers::info::server_info));
    
    // API v1 routes
    let api_v1 = Router::new()
        .route("/convert", post(handlers::convert::convert_file))
        .route("/detect", post(handlers::detect::detect_format))
        .route("/formats", get(handlers::formats::list_formats))
        .route("/batch", post(handlers::batch::create_batch_job))
        .route("/batch/upload", post(handlers::batch::batch_upload))
        .route("/batch/:job_id", get(handlers::batch::get_job_status))
        .route("/batch", get(handlers::batch::list_jobs));
    
    // Add upload endpoint if enabled
    let api_v1 = if config.enable_upload {
        api_v1.route("/upload", post(handlers::upload::upload_file))
    } else {
        api_v1
    };
    
    // Nest API v1 routes
    app = app.nest("/api/v1", api_v1);
    
    // Add WebSocket endpoint if enabled
    if config.enable_websocket {
        app = app.route("/ws", get(handlers::websocket::websocket_handler));
    }
    
    // Add docs endpoints if enabled
    if config.enable_docs {
        app = app.route("/docs", get(handlers::docs::api_documentation))
            .route("/docs/openapi.json", get(handlers::docs::openapi_spec))
            .route("/docs/redoc", get(handlers::docs::redoc_documentation));
    }
    
    // Add metrics endpoint if enabled
    if config.enable_metrics {
        app = app.route("/metrics", get(handlers::metrics::metrics_handler));
    }
    
    // Add compression
    app = app.layer(CompressionLayer::new());
    
    // Add request tracing
    app = app.layer(TraceLayer::new_for_http());
    
    // Add request body limit
    app = app.layer(RequestBodyLimitLayer::new(config.max_body_size));
    
    // Add CORS if enabled
    if config.enable_cors {
        let cors_config = middleware::cors::CorsConfig {
            allowed_origins: config.cors_origins.clone(),
            allow_credentials: false,
            max_age: 3600,
        };
        let cors = middleware::cors::create_cors_layer(cors_config);
        app = app.layer(cors);
    }
    
    // Add authentication middleware if API key is configured
    if let Some(api_key) = config.api_key {
        app = app.layer(middleware::auth::ApiKeyLayer::new(api_key));
    }
    
    // Add rate limiting if configured
    if let Some(rate_limit) = config.rate_limit {
        app = app.layer(middleware::rate_limit::RateLimitLayer::new(rate_limit));
    }
    
    // Add static file serving if configured
    if let Some(static_dir) = config.static_dir {
        use tower_http::services::ServeDir;
        app = app.nest_service("/static", ServeDir::new(static_dir));
    }
    
    // Create WebSocket state
    let ws_state = Arc::new(websocket::WsState::new());
    
    // Create batch state
    let batch_state = Arc::new(handlers::batch::BatchState {
        jobs: Arc::new(tokio::sync::Mutex::new(std::collections::HashMap::new())),
        ws_state: ws_state.clone(),
        conversion_engine: Arc::new(ConversionEngine::new()),
    });
    
    // Create application state
    let state = Arc::new(AppState {
        conversion_engine: Arc::new(ConversionEngine::new()),
        format_manager: Arc::new(FormatManager::new()),
    });
    
    // Attach states to router - need to handle multiple states
    // For now, we'll use the batch state which includes everything needed
    let app = app.with_state(batch_state);
    
    Ok(app)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_build_router_minimal() {
        let config = Config {
            api_key: None,
            max_body_size: 10 * 1024 * 1024, // 10MB
            enable_cors: false,
            cors_origins: vec![],
            enable_websocket: false,
            enable_upload: false,
            static_dir: None,
            enable_docs: false,
            enable_metrics: false,
            rate_limit: None,
            ssl_cert: None,
            ssl_key: None,
        };
        
        let router = build_router(config);
        assert!(router.is_ok());
    }
    
    #[test]
    fn test_build_router_full() {
        let config = Config {
            api_key: Some("test-key".to_string()),
            max_body_size: 50 * 1024 * 1024, // 50MB
            enable_cors: true,
            cors_origins: vec!["http://localhost:3000".to_string()],
            enable_websocket: true,
            enable_upload: true,
            static_dir: Some(PathBuf::from("/tmp/static")),
            enable_docs: true,
            enable_metrics: true,
            rate_limit: Some(100),
            ssl_cert: Some(PathBuf::from("/tmp/cert.pem")),
            ssl_key: Some(PathBuf::from("/tmp/key.pem")),
        };
        
        let router = build_router(config);
        assert!(router.is_ok());
    }
}