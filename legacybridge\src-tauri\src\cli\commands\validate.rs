// File Validation Command Implementation
// Provides comprehensive file integrity validation and format verification

use std::path::Path;
use std::time::Instant;
use glob::glob;
use indicatif::{ProgressBar, ProgressStyle};
use rayon::prelude::*;

use crate::cli::app::{ValidateArgs, Cli};
use crate::cli::{CliError, CliResult};
use crate::cli::analysis::{FormatAnalyzer, FormatAnalysis, IntegrityStatus};
use crate::cli::output::{OutputFormat, print_table, print_json};
use crate::conversion::error::ConversionError;
use crate::formats::{FormatManager, FormatType};

/// Validation result for a file
#[derive(Debug, Clone, serde::Serialize)]
pub struct ValidationResult {
    pub file_path: String,
    pub file_size: u64,
    pub format_type: String,
    pub format_confidence: f32,
    pub is_valid: bool,
    pub integrity_status: IntegrityStatus,
    pub validation_checks: Vec<ValidationCheck>,
    pub overall_score: f32,
}

/// Individual validation check
#[derive(Debug, Clone, serde::Serialize)]
pub struct ValidationCheck {
    pub name: String,
    pub passed: bool,
    pub message: String,
    pub severity: ValidationSeverity,
}

/// Severity level for validation issues
#[derive(Debug, Clone, Copy, serde::Serialize)]
pub enum ValidationSeverity {
    Critical,
    Warning,
    Info,
}

pub async fn handle_validate_command(
    args: ValidateArgs, 
    global_args: &Cli
) -> CliResult<()> {
    let start_time = Instant::now();
    
    println!("🔍 Starting file validation...");
    
    if global_args.verbose > 0 {
        println!("📝 Validate Args:");
        println!("   Files: {:?}", args.files);
        println!("   Check Integrity: {}", args.check_integrity);
        println!("   Check Format: {}", args.check_format);
        println!("   Check Size: {}", args.check_size);
        println!("   Max Size: {:?}", args.max_size);
        println!("   Min Size: {:?}", args.min_size);
        println!("   Check Structure: {}", args.check_structure);
        println!("   Report: {:?}", args.report);
        println!("   Strict: {}", args.strict);
        println!("   Fix Issues: {}", args.fix_issues);
    }
    
    if args.files.is_empty() {
        return Err(CliError::NoInputFiles);
    }
    
    // Execute the validation
    execute(args, global_args).map_err(|e| CliError::ConversionError(e))?;
    
    let duration = start_time.elapsed();
    println!("✅ File validation completed in {:?}", duration);
    
    Ok(())
}

/// Execute the validate command
fn execute(args: ValidateArgs, global_args: &Cli) -> Result<(), ConversionError> {
    // Expand file patterns
    let paths = expand_patterns(&args.files)?;
    
    if paths.is_empty() {
        return Err(ConversionError::InvalidInput("No files found matching the patterns".to_string()));
    }
    
    println!("📁 Found {} files to validate", paths.len());
    
    // Create validators
    let analyzer = FormatAnalyzer::new();
    let format_manager = FormatManager::new();
    
    // Validate files (with progress if requested)
    let results = if args.show_progress {
        validate_with_progress(&analyzer, &format_manager, &paths, &args)?
    } else {
        validate_files(&analyzer, &format_manager, &paths, &args)?
    };
    
    // Filter and count results
    let valid_count = results.iter().filter(|r| r.is_valid).count();
    let invalid_count = results.len() - valid_count;
    
    // Display results based on output format
    match global_args.output {
        OutputFormat::Table => display_table_output(&results, args.check_structure),
        OutputFormat::Json => display_json_output(&results),
        OutputFormat::Plain => display_plain_output(&results),
        OutputFormat::Csv => display_csv_output(&results),
    }
    
    // Save report if requested
    if let Some(report_path) = &args.report {
        save_validation_report(&results, Path::new(report_path))?;
        println!("\n✅ Validation report saved to: {}", report_path);
    }
    
    // Print summary
    if !matches!(global_args.output, OutputFormat::Json) {
        println!("\n📊 Validation Summary:");
        println!("   Total files: {}", results.len());
        println!("   ✅ Valid: {} ({:.1}%)", valid_count, (valid_count as f32 / results.len() as f32) * 100.0);
        println!("   ❌ Invalid: {} ({:.1}%)", invalid_count, (invalid_count as f32 / results.len() as f32) * 100.0);
        
        // Show common issues
        let mut issue_counts = std::collections::HashMap::new();
        for result in &results {
            for issue in &result.integrity_status.issues {
                *issue_counts.entry(issue.clone()).or_insert(0) += 1;
            }
        }
        
        if !issue_counts.is_empty() {
            println!("\n   Common Issues:");
            let mut issues: Vec<_> = issue_counts.into_iter().collect();
            issues.sort_by(|a, b| b.1.cmp(&a.1));
            
            for (issue, count) in issues.iter().take(5) {
                println!("      • {} ({} files)", issue, count);
            }
        }
    }
    
    // Exit with error if in strict mode and found invalid files
    if args.strict && invalid_count > 0 {
        return Err(ConversionError::ValidationError(format!("{} files failed validation", invalid_count)));
    }
    
    Ok(())
}

/// Expand glob patterns to file paths
fn expand_patterns(patterns: &[String]) -> Result<Vec<String>, ConversionError> {
    let mut paths = Vec::new();
    
    for pattern in patterns {
        if Path::new(pattern).exists() {
            paths.push(pattern.clone());
        } else {
            let matches = glob(pattern)
                .map_err(|e| ConversionError::InvalidInput(format!("Invalid glob pattern: {}", e)))?;
            
            for entry in matches {
                match entry {
                    Ok(path) => {
                        if path.is_file() {
                            paths.push(path.to_string_lossy().to_string());
                        }
                    }
                    Err(e) => eprintln!("⚠️  Error reading path: {}", e),
                }
            }
        }
    }
    
    paths.sort();
    paths.dedup();
    
    Ok(paths)
}

/// Validate files without progress bar
fn validate_files(
    analyzer: &FormatAnalyzer,
    format_manager: &FormatManager,
    paths: &[String],
    args: &ValidateArgs
) -> Result<Vec<ValidationResult>, ConversionError> {
    let results: Vec<_> = paths
        .par_iter()
        .map(|path_str| {
            validate_single_file(analyzer, format_manager, path_str, args)
        })
        .collect();
    
    Ok(results)
}

/// Validate files with progress bar
fn validate_with_progress(
    analyzer: &FormatAnalyzer,
    format_manager: &FormatManager,
    paths: &[String],
    args: &ValidateArgs
) -> Result<Vec<ValidationResult>, ConversionError> {
    let pb = ProgressBar::new(paths.len() as u64);
    pb.set_style(
        ProgressStyle::default_bar()
            .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({eta}) {msg}")
            .unwrap()
            .progress_chars("#>-")
    );
    
    let results: Vec<_> = paths
        .par_iter()
        .map(|path_str| {
            let result = validate_single_file(analyzer, format_manager, path_str, args);
            pb.inc(1);
            
            // Update message
            let filename = Path::new(path_str).file_name()
                .unwrap_or_default()
                .to_string_lossy();
            let status = if result.is_valid { "✓" } else { "✗" };
            pb.set_message(format!("{} {}", status, filename));
            
            result
        })
        .collect();
    
    pb.finish_with_message("Validation complete!");
    
    Ok(results)
}

/// Validate a single file
fn validate_single_file(
    analyzer: &FormatAnalyzer,
    format_manager: &FormatManager,
    path_str: &str,
    args: &ValidateArgs
) -> ValidationResult {
    let path = Path::new(path_str);
    let mut checks = Vec::new();
    let mut is_valid = true;
    
    // Get file metadata
    let metadata = match std::fs::metadata(path) {
        Ok(m) => m,
        Err(e) => {
            checks.push(ValidationCheck {
                name: "File Access".to_string(),
                passed: false,
                message: format!("Cannot access file: {}", e),
                severity: ValidationSeverity::Critical,
            });
            return ValidationResult {
                file_path: path_str.to_string(),
                file_size: 0,
                format_type: "Unknown".to_string(),
                format_confidence: 0.0,
                is_valid: false,
                integrity_status: IntegrityStatus {
                    is_valid: false,
                    issues: vec!["File not accessible".to_string()],
                    readable_percentage: 0.0,
                },
                validation_checks: checks,
                overall_score: 0.0,
            };
        }
    };
    
    let file_size = metadata.len();
    
    // Check file size if requested
    if args.check_size {
        let size_valid = check_file_size(file_size, args.min_size.as_deref(), args.max_size.as_deref(), &mut checks);
        if !size_valid {
            is_valid = false;
        }
    }
    
    // Analyze file format
    let analysis = match analyzer.analyze_file(path) {
        Ok(a) => a,
        Err(e) => {
            checks.push(ValidationCheck {
                name: "Format Analysis".to_string(),
                passed: false,
                message: format!("Cannot analyze file: {}", e),
                severity: ValidationSeverity::Critical,
            });
            return ValidationResult {
                file_path: path_str.to_string(),
                file_size,
                format_type: "Unknown".to_string(),
                format_confidence: 0.0,
                is_valid: false,
                integrity_status: IntegrityStatus {
                    is_valid: false,
                    issues: vec!["Analysis failed".to_string()],
                    readable_percentage: 0.0,
                },
                validation_checks: checks,
                overall_score: 0.0,
            };
        }
    };
    
    // Check format validity
    if args.check_format {
        let format_valid = validate_format(&analysis, &mut checks);
        if !format_valid {
            is_valid = false;
        }
    }
    
    // Check file integrity
    if args.check_integrity {
        if !analysis.integrity_status.is_valid {
            is_valid = false;
            checks.push(ValidationCheck {
                name: "File Integrity".to_string(),
                passed: false,
                message: analysis.integrity_status.issues.join("; "),
                severity: ValidationSeverity::Critical,
            });
        } else {
            checks.push(ValidationCheck {
                name: "File Integrity".to_string(),
                passed: true,
                message: "File structure is valid".to_string(),
                severity: ValidationSeverity::Info,
            });
        }
    }
    
    // Check file structure if requested
    if args.check_structure {
        let structure_valid = validate_structure(&analysis, format_manager, &mut checks);
        if !structure_valid {
            is_valid = false;
        }
    }
    
    // Calculate overall score
    let total_checks = checks.len() as f32;
    let passed_checks = checks.iter().filter(|c| c.passed).count() as f32;
    let overall_score = if total_checks > 0.0 {
        passed_checks / total_checks
    } else {
        0.0
    };
    
    ValidationResult {
        file_path: path_str.to_string(),
        file_size,
        format_type: analysis.format_type,
        format_confidence: analysis.confidence,
        is_valid,
        integrity_status: analysis.integrity_status,
        validation_checks: checks,
        overall_score,
    }
}

/// Check file size constraints
fn check_file_size(size: u64, min_size: Option<&str>, max_size: Option<&str>, checks: &mut Vec<ValidationCheck>) -> bool {
    let mut size_valid = true;
    
    if let Some(min) = min_size {
        if let Ok(min_bytes) = parse_size(min) {
            if size < min_bytes {
                checks.push(ValidationCheck {
                    name: "Minimum Size".to_string(),
                    passed: false,
                    message: format!("File size {} is below minimum {}", format_file_size(size), min),
                    severity: ValidationSeverity::Warning,
                });
                size_valid = false;
            }
        }
    }
    
    if let Some(max) = max_size {
        if let Ok(max_bytes) = parse_size(max) {
            if size > max_bytes {
                checks.push(ValidationCheck {
                    name: "Maximum Size".to_string(),
                    passed: false,
                    message: format!("File size {} exceeds maximum {}", format_file_size(size), max),
                    severity: ValidationSeverity::Warning,
                });
                size_valid = false;
            }
        }
    }
    
    if size_valid && (min_size.is_some() || max_size.is_some()) {
        checks.push(ValidationCheck {
            name: "File Size".to_string(),
            passed: true,
            message: format!("File size {} is within limits", format_file_size(size)),
            severity: ValidationSeverity::Info,
        });
    }
    
    size_valid
}

/// Validate format detection
fn validate_format(analysis: &FormatAnalysis, checks: &mut Vec<ValidationCheck>) -> bool {
    if analysis.format_type == "Unknown" {
        checks.push(ValidationCheck {
            name: "Format Detection".to_string(),
            passed: false,
            message: "Unable to determine file format".to_string(),
            severity: ValidationSeverity::Critical,
        });
        false
    } else if analysis.confidence < 0.5 {
        checks.push(ValidationCheck {
            name: "Format Detection".to_string(),
            passed: false,
            message: format!("Low confidence ({:.0}%) in format detection", analysis.confidence * 100.0),
            severity: ValidationSeverity::Warning,
        });
        false
    } else {
        checks.push(ValidationCheck {
            name: "Format Detection".to_string(),
            passed: true,
            message: format!("{} format detected with {:.0}% confidence", analysis.format_type, analysis.confidence * 100.0),
            severity: ValidationSeverity::Info,
        });
        true
    }
}

/// Validate file structure
fn validate_structure(analysis: &FormatAnalysis, _format_manager: &FormatManager, checks: &mut Vec<ValidationCheck>) -> bool {
    // Check if file has expected structure based on format
    let mut structure_valid = true;
    
    // Check readable percentage
    if analysis.integrity_status.readable_percentage < 50.0 {
        checks.push(ValidationCheck {
            name: "File Readability".to_string(),
            passed: false,
            message: format!("Only {:.1}% of file content is readable", analysis.integrity_status.readable_percentage),
            severity: ValidationSeverity::Warning,
        });
        structure_valid = false;
    }
    
    // Format-specific structure checks
    match analysis.format_type.as_str() {
        "Doc" => {
            if analysis.metadata.get("ole2_signature").is_none() {
                checks.push(ValidationCheck {
                    name: "DOC Structure".to_string(),
                    passed: false,
                    message: "Missing OLE2 compound document structure".to_string(),
                    severity: ValidationSeverity::Critical,
                });
                structure_valid = false;
            }
        }
        "DBase" => {
            if let Some(record_count) = analysis.metadata.get("record_count") {
                if let Ok(count) = record_count.parse::<u32>() {
                    if count == 0 {
                        checks.push(ValidationCheck {
                            name: "dBase Structure".to_string(),
                            passed: false,
                            message: "Database contains no records".to_string(),
                            severity: ValidationSeverity::Warning,
                        });
                    }
                }
            }
        }
        _ => {
            // Generic structure check
            if analysis.integrity_status.is_valid {
                checks.push(ValidationCheck {
                    name: "File Structure".to_string(),
                    passed: true,
                    message: "File structure appears valid".to_string(),
                    severity: ValidationSeverity::Info,
                });
            }
        }
    }
    
    structure_valid
}

/// Parse size string (e.g., "100MB", "2.5GB")
fn parse_size(size_str: &str) -> Result<u64, ConversionError> {
    let size_str = size_str.trim().to_uppercase();
    
    if let Ok(bytes) = size_str.parse::<u64>() {
        return Ok(bytes);
    }
    
    let (number_part, unit_part) = size_str
        .chars()
        .partition::<String, _>(|c| c.is_numeric() || *c == '.');
    
    let number: f64 = number_part.parse()
        .map_err(|_| ConversionError::InvalidInput(format!("Invalid size: {}", size_str)))?;
    
    let multiplier = match unit_part.as_str() {
        "B" | "" => 1.0,
        "KB" | "K" => 1024.0,
        "MB" | "M" => 1024.0 * 1024.0,
        "GB" | "G" => 1024.0 * 1024.0 * 1024.0,
        "TB" | "T" => 1024.0 * 1024.0 * 1024.0 * 1024.0,
        _ => return Err(ConversionError::InvalidInput(format!("Unknown size unit: {}", unit_part))),
    };
    
    Ok((number * multiplier) as u64)
}

/// Display results in table format
fn display_table_output(results: &[ValidationResult], show_checks: bool) {
    let headers = vec!["File", "Format", "Confidence", "Size", "Valid", "Score"];
    let mut rows = Vec::new();
    
    for result in results {
        rows.push(vec![
            Path::new(&result.file_path).file_name()
                .unwrap_or_default()
                .to_string_lossy()
                .to_string(),
            result.format_type.clone(),
            format!("{:.0}%", result.format_confidence * 100.0),
            format_file_size(result.file_size),
            if result.is_valid { "✓" } else { "✗" }.to_string(),
            format!("{:.0}%", result.overall_score * 100.0),
        ]);
    }
    
    print_table(headers, rows);
    
    // Show detailed checks if requested
    if show_checks {
        println!("\n📋 Validation Details:");
        for result in results {
            if !result.validation_checks.is_empty() {
                println!("\n{}", result.file_path);
                for check in &result.validation_checks {
                    let icon = if check.passed { "✓" } else { "✗" };
                    let severity = match check.severity {
                        ValidationSeverity::Critical => "CRITICAL",
                        ValidationSeverity::Warning => "WARNING",
                        ValidationSeverity::Info => "INFO",
                    };
                    println!("  {} {} [{}]: {}", icon, check.name, severity, check.message);
                }
            }
        }
    }
}

/// Display results in JSON format
fn display_json_output(results: &[ValidationResult]) {
    print_json(results).expect("Failed to print JSON");
}

/// Display results in plain text format
fn display_plain_output(results: &[ValidationResult]) {
    for result in results {
        println!("{}: {} (valid: {}, score: {:.0}%)",
            result.file_path,
            result.format_type,
            if result.is_valid { "yes" } else { "no" },
            result.overall_score * 100.0
        );
        
        if !result.integrity_status.issues.is_empty() {
            println!("  Issues: {}", result.integrity_status.issues.join("; "));
        }
        
        for check in &result.validation_checks {
            if !check.passed {
                println!("  ✗ {}: {}", check.name, check.message);
            }
        }
    }
}

/// Display results in CSV format
fn display_csv_output(results: &[ValidationResult]) {
    println!("File,Format,Confidence,Size,Valid,Score,Issues");
    
    for result in results {
        let issues = result.integrity_status.issues.join("; ");
        println!("{},{},{:.2},{},{},{:.2},\"{}\"",
            result.file_path,
            result.format_type,
            result.format_confidence,
            result.file_size,
            result.is_valid,
            result.overall_score,
            issues
        );
    }
}

/// Save validation report
fn save_validation_report(results: &[ValidationResult], path: &Path) -> Result<(), ConversionError> {
    let json = serde_json::to_string_pretty(results)
        .map_err(|e| ConversionError::SerializationError(e.to_string()))?;
    
    std::fs::write(path, json)
        .map_err(|e| ConversionError::IoError(e.to_string()))?;
    
    Ok(())
}

/// Format file size in human-readable format
fn format_file_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    format!("{:.1}{}", size, UNITS[unit_index])
}