use std::path::Path;
use glob::{Pat<PERSON>, PatternError};
use crate::cli::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Cli<PERSON><PERSON>ult};

#[derive(Debug, <PERSON>lone)]
pub struct FileFilter {
    include_patterns: Vec<Pattern>,
    exclude_patterns: Vec<Pattern>,
    min_size: Option<u64>,
    max_size: Option<u64>,
}

impl FileFilter {
    pub fn new() -> Self {
        Self {
            include_patterns: Vec::new(),
            exclude_patterns: Vec::new(),
            min_size: None,
            max_size: None,
        }
    }

    pub fn with_include_patterns(mut self, patterns: &[String]) -> CliResult<Self> {
        for pattern in patterns {
            self.add_include_pattern(pattern)?;
        }
        Ok(self)
    }

    pub fn with_exclude_patterns(mut self, patterns: &[String]) -> CliResult<Self> {
        for pattern in patterns {
            self.add_exclude_pattern(pattern)?;
        }
        Ok(self)
    }

    pub fn with_size_range(mut self, min: Option<u64>, max: Option<u64>) -> Self {
        self.min_size = min;
        self.max_size = max;
        self
    }

    pub fn add_include_pattern(&mut self, pattern: &str) -> CliResult<()> {
        let glob_pattern = Pattern::new(pattern)
            .map_err(|e| CliError::Config(format!("Invalid include pattern '{}': {}", pattern, e)))?;
        self.include_patterns.push(glob_pattern);
        Ok(())
    }

    pub fn add_exclude_pattern(&mut self, pattern: &str) -> CliResult<()> {
        let glob_pattern = Pattern::new(pattern)
            .map_err(|e| CliError::Config(format!("Invalid exclude pattern '{}': {}", pattern, e)))?;
        self.exclude_patterns.push(glob_pattern);
        Ok(())
    }

    pub fn matches(&self, path: &Path, size: Option<u64>) -> bool {
        // Check exclude patterns first
        if self.is_excluded(path) {
            return false;
        }

        // Check include patterns
        if !self.is_included(path) {
            return false;
        }

        // Check size constraints
        if let Some(file_size) = size {
            if let Some(min) = self.min_size {
                if file_size < min {
                    return false;
                }
            }
            if let Some(max) = self.max_size {
                if file_size > max {
                    return false;
                }
            }
        }

        true
    }

    fn is_included(&self, path: &Path) -> bool {
        if self.include_patterns.is_empty() {
            return true;
        }

        let path_str = path.to_string_lossy();
        let file_name = path.file_name()
            .map(|n| n.to_string_lossy())
            .unwrap_or_default();

        self.include_patterns.iter().any(|pattern| {
            pattern.matches(&path_str) || pattern.matches(&file_name)
        })
    }

    fn is_excluded(&self, path: &Path) -> bool {
        if self.exclude_patterns.is_empty() {
            return false;
        }

        let path_str = path.to_string_lossy();
        let file_name = path.file_name()
            .map(|n| n.to_string_lossy())
            .unwrap_or_default();

        self.exclude_patterns.iter().any(|pattern| {
            pattern.matches(&path_str) || pattern.matches(&file_name)
        })
    }

    pub fn parse_patterns(pattern_str: &str) -> Vec<String> {
        pattern_str
            .split(',')
            .map(|s| s.trim())
            .filter(|s| !s.is_empty())
            .map(|s| s.to_string())
            .collect()
    }

    pub fn parse_size(size_str: &str) -> CliResult<u64> {
        let size_str = size_str.trim().to_uppercase();
        
        if let Some(num_str) = size_str.strip_suffix("KB") {
            let num: f64 = num_str.trim().parse()
                .map_err(|_| CliError::Config(format!("Invalid size: {}", size_str)))?;
            Ok((num * 1024.0) as u64)
        } else if let Some(num_str) = size_str.strip_suffix("MB") {
            let num: f64 = num_str.trim().parse()
                .map_err(|_| CliError::Config(format!("Invalid size: {}", size_str)))?;
            Ok((num * 1024.0 * 1024.0) as u64)
        } else if let Some(num_str) = size_str.strip_suffix("GB") {
            let num: f64 = num_str.trim().parse()
                .map_err(|_| CliError::Config(format!("Invalid size: {}", size_str)))?;
            Ok((num * 1024.0 * 1024.0 * 1024.0) as u64)
        } else if let Some(num_str) = size_str.strip_suffix("B") {
            num_str.trim().parse()
                .map_err(|_| CliError::Config(format!("Invalid size: {}", size_str)))
        } else {
            // Assume bytes if no unit
            size_str.parse()
                .map_err(|_| CliError::Config(format!("Invalid size: {}", size_str)))
        }
    }
}

#[derive(Debug, Clone)]
pub struct FileFilterBuilder {
    filter: FileFilter,
}

impl FileFilterBuilder {
    pub fn new() -> Self {
        Self {
            filter: FileFilter::new(),
        }
    }

    pub fn include_patterns(mut self, patterns: &str) -> CliResult<Self> {
        let pattern_list = FileFilter::parse_patterns(patterns);
        self.filter = self.filter.with_include_patterns(&pattern_list)?;
        Ok(self)
    }

    pub fn exclude_patterns(mut self, patterns: &str) -> CliResult<Self> {
        let pattern_list = FileFilter::parse_patterns(patterns);
        self.filter = self.filter.with_exclude_patterns(&pattern_list)?;
        Ok(self)
    }

    pub fn min_size(mut self, size: Option<String>) -> CliResult<Self> {
        if let Some(size_str) = size {
            self.filter.min_size = Some(FileFilter::parse_size(&size_str)?);
        }
        Ok(self)
    }

    pub fn max_size(mut self, size: Option<String>) -> CliResult<Self> {
        if let Some(size_str) = size {
            self.filter.max_size = Some(FileFilter::parse_size(&size_str)?);
        }
        Ok(self)
    }

    pub fn build(self) -> FileFilter {
        self.filter
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_include_patterns() {
        let filter = FileFilter::new()
            .with_include_patterns(&["*.txt".to_string(), "*.doc".to_string()])
            .unwrap();

        assert!(filter.matches(&PathBuf::from("test.txt"), None));
        assert!(filter.matches(&PathBuf::from("document.doc"), None));
        assert!(!filter.matches(&PathBuf::from("image.png"), None));
    }

    #[test]
    fn test_exclude_patterns() {
        let filter = FileFilter::new()
            .with_exclude_patterns(&["*.tmp".to_string(), "~*".to_string()])
            .unwrap();

        assert!(!filter.matches(&PathBuf::from("test.tmp"), None));
        assert!(!filter.matches(&PathBuf::from("~backup.doc"), None));
        assert!(filter.matches(&PathBuf::from("document.txt"), None));
    }

    #[test]
    fn test_size_filter() {
        let filter = FileFilter::new()
            .with_size_range(Some(1024), Some(1024 * 1024));

        assert!(!filter.matches(&PathBuf::from("small.txt"), Some(512)));
        assert!(filter.matches(&PathBuf::from("medium.txt"), Some(2048)));
        assert!(!filter.matches(&PathBuf::from("large.txt"), Some(2 * 1024 * 1024)));
    }

    #[test]
    fn test_parse_size() {
        assert_eq!(FileFilter::parse_size("100").unwrap(), 100);
        assert_eq!(FileFilter::parse_size("100B").unwrap(), 100);
        assert_eq!(FileFilter::parse_size("1KB").unwrap(), 1024);
        assert_eq!(FileFilter::parse_size("2.5MB").unwrap(), 2621440);
        assert_eq!(FileFilter::parse_size("1GB").unwrap(), 1073741824);
    }

    #[test]
    fn test_parse_patterns() {
        let patterns = FileFilter::parse_patterns("*.txt, *.doc, *.pdf");
        assert_eq!(patterns, vec!["*.txt", "*.doc", "*.pdf"]);

        let empty = FileFilter::parse_patterns("");
        assert!(empty.is_empty());

        let single = FileFilter::parse_patterns("*.md");
        assert_eq!(single, vec!["*.md"]);
    }
}