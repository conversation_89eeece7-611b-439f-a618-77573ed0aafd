use crate::cli::app::SearchArgs;
use crate::cli::commands::search::{
    execute_search_command, SearchResult, Match, MatchType,
    SearchMetadata, SearchSummary
};
use crate::cli::output::OutputFormat;
use std::path::PathBuf;
use tempfile::TempDir;
use tokio::fs;

#[tokio::test]
async fn test_search_literal_pattern() {
    let temp_dir = TempDir::new().unwrap();
    
    // Create test files
    let file1 = temp_dir.path().join("test1.txt");
    fs::write(&file1, "This is a test file with search term").await.unwrap();
    
    let file2 = temp_dir.path().join("test2.txt");
    fs::write(&file2, "Another file without the term").await.unwrap();
    
    let args = SearchArgs {
        query: "search term".to_string(),
        content: true,
        metadata: false,
        files: vec![],
        directory: temp_dir.path().to_path_buf(),
        recursive: false,
        case_sensitive: false,
        regex: false,
        max_results: 100,
        include_hidden: false,
        output_format: OutputFormat::Json,
        export: None,
    };
    
    let result = execute_search_command(args).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_search_case_insensitive() {
    let temp_dir = TempDir::new().unwrap();
    
    let file = temp_dir.path().join("case_test.txt");
    fs::write(&file, "This has UPPERCASE and lowercase text").await.unwrap();
    
    let args = SearchArgs {
        query: "uppercase".to_string(),
        content: true,
        metadata: false,
        files: vec![],
        directory: temp_dir.path().to_path_buf(),
        recursive: false,
        case_sensitive: false,
        regex: false,
        max_results: 100,
        include_hidden: false,
        output_format: OutputFormat::Plain,
        export: None,
    };
    
    let result = execute_search_command(args).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_search_regex_pattern() {
    let temp_dir = TempDir::new().unwrap();
    
    let file = temp_dir.path().join("regex_test.txt");
    fs::write(&file, "Phone: ************ and ************").await.unwrap();
    
    let args = SearchArgs {
        query: r"\d{3}-\d{3}-\d{4}".to_string(),
        content: true,
        metadata: false,
        files: vec![],
        directory: temp_dir.path().to_path_buf(),
        recursive: false,
        case_sensitive: false,
        regex: true,
        max_results: 100,
        include_hidden: false,
        output_format: OutputFormat::Plain,
        export: None,
    };
    
    let result = execute_search_command(args).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_search_file_patterns() {
    let temp_dir = TempDir::new().unwrap();
    
    // Create files with different extensions
    fs::write(temp_dir.path().join("doc1.txt"), "search content").await.unwrap();
    fs::write(temp_dir.path().join("doc2.md"), "search content").await.unwrap();
    fs::write(temp_dir.path().join("doc3.rs"), "other content").await.unwrap();
    
    let args = SearchArgs {
        query: "search".to_string(),
        content: true,
        metadata: false,
        files: vec!["*.txt".to_string(), "*.md".to_string()],
        directory: temp_dir.path().to_path_buf(),
        recursive: false,
        case_sensitive: false,
        regex: false,
        max_results: 100,
        include_hidden: false,
        output_format: OutputFormat::Plain,
        export: None,
    };
    
    let result = execute_search_command(args).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_search_recursive() {
    let temp_dir = TempDir::new().unwrap();
    
    // Create nested directory structure
    let sub_dir = temp_dir.path().join("subdir");
    fs::create_dir(&sub_dir).await.unwrap();
    
    fs::write(temp_dir.path().join("root.txt"), "search term").await.unwrap();
    fs::write(sub_dir.join("nested.txt"), "search term").await.unwrap();
    
    let args = SearchArgs {
        query: "search term".to_string(),
        content: true,
        metadata: false,
        files: vec![],
        directory: temp_dir.path().to_path_buf(),
        recursive: true,
        case_sensitive: false,
        regex: false,
        max_results: 100,
        include_hidden: false,
        output_format: OutputFormat::Plain,
        export: None,
    };
    
    let result = execute_search_command(args).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_search_export_results() {
    let temp_dir = TempDir::new().unwrap();
    
    fs::write(temp_dir.path().join("test.txt"), "export test").await.unwrap();
    
    let export_path = temp_dir.path().join("results.json");
    
    let args = SearchArgs {
        query: "export".to_string(),
        content: true,
        metadata: false,
        files: vec![],
        directory: temp_dir.path().to_path_buf(),
        recursive: false,
        case_sensitive: false,
        regex: false,
        max_results: 100,
        include_hidden: false,
        output_format: OutputFormat::Plain,
        export: Some(export_path.clone()),
    };
    
    let result = execute_search_command(args).await;
    assert!(result.is_ok());
    assert!(export_path.exists());
}

#[test]
fn test_search_result_structure() {
    let result = SearchResult {
        file_path: "/path/to/file.txt".to_string(),
        file_name: "file.txt".to_string(),
        format: "text".to_string(),
        matches: vec![
            Match {
                line_number: Some(10),
                column: Some(5),
                context: "This is the matching line".to_string(),
                match_type: MatchType::Content,
            },
            Match {
                line_number: None,
                column: None,
                context: "Title: Test Document".to_string(),
                match_type: MatchType::Metadata,
            },
        ],
        metadata: Some(SearchMetadata {
            title: Some("Test Document".to_string()),
            author: Some("Test Author".to_string()),
            creation_date: None,
            modification_date: None,
            keywords: vec!["test".to_string(), "document".to_string()],
        }),
    };
    
    assert_eq!(result.matches.len(), 2);
    assert_eq!(result.file_name, "file.txt");
    assert!(result.metadata.is_some());
}

#[test]
fn test_search_summary() {
    let summary = SearchSummary {
        total_files_searched: 100,
        files_with_matches: 25,
        total_matches: 150,
        search_duration_ms: 500,
        formats_found: {
            let mut map = std::collections::HashMap::new();
            map.insert("txt".to_string(), 15);
            map.insert("md".to_string(), 10);
            map
        },
    };
    
    assert_eq!(summary.total_files_searched, 100);
    assert_eq!(summary.files_with_matches, 25);
    assert_eq!(summary.total_matches, 150);
    assert_eq!(summary.formats_found.len(), 2);
}

#[test]
fn test_file_pattern_matching() {
    use crate::cli::commands::search::matches_patterns;
    use std::path::Path;
    
    // Test exact match
    assert!(matches_patterns(Path::new("test.txt"), &vec!["test.txt".to_string()]));
    
    // Test wildcard patterns
    assert!(matches_patterns(Path::new("document.doc"), &vec!["*.doc".to_string()]));
    assert!(matches_patterns(Path::new("report.pdf"), &vec!["report.*".to_string()]));
    assert!(matches_patterns(Path::new("file123.txt"), &vec!["file???.txt".to_string()]));
    
    // Test no match
    assert!(!matches_patterns(Path::new("test.txt"), &vec!["*.doc".to_string()]));
    
    // Test contains pattern
    assert!(matches_patterns(Path::new("important_document.txt"), &vec!["important".to_string()]));
}

#[test]
fn test_match_type_serialization() {
    let content_match = MatchType::Content;
    let json = serde_json::to_string(&content_match).unwrap();
    assert_eq!(json, "\"content\"");
    
    let metadata_match = MatchType::Metadata;
    let json = serde_json::to_string(&metadata_match).unwrap();
    assert_eq!(json, "\"metadata\"");
    
    let filename_match = MatchType::Filename;
    let json = serde_json::to_string(&filename_match).unwrap();
    assert_eq!(json, "\"filename\"");
}