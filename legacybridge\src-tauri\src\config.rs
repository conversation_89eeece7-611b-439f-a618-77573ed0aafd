// Configuration Module
// Comprehensive configuration management for LegacyBridge MCP server

use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::{info, warn, error};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub features: FeatureFlags,
    pub processing: ProcessingConfig,
    pub mcp_server: McpServerConfig,
    pub legacy_formats: LegacyFormatsConfig,
    pub security: SecurityConfig,
    pub performance: PerformanceConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureFlags {
    pub format_doc: bool,
    pub format_wordperfect: bool,
    pub format_dbase: bool,
    pub format_wordstar: bool,
    pub format_lotus: bool,
    pub dll_building: bool,
    pub parallel_processing: bool,
    pub advanced_formatting: bool,
    pub batch_conversion: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingConfig {
    pub max_file_size_mb: u64,
    pub parallel_jobs: usize,
    pub timeout_seconds: u64,
    pub temp_dir: Option<PathBuf>,
    pub enable_simd: bool,
    pub memory_limit_mb: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpServerConfig {
    pub protocol_version: String,
    pub transport: TransportType,
    pub enable_logging: bool,
    pub log_level: String,
    pub enable_tracing: bool,
    pub server_name: String,
    pub server_version: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransportType {
    Stdio,
    Http { port: u16 },
    Websocket { port: u16 },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyFormatsConfig {
    pub enable_doc: bool,
    pub enable_wordperfect: bool,
    pub enable_dbase: bool,
    pub enable_lotus: bool,
    pub enable_wordstar: bool,
    pub libreoffice_path: Option<PathBuf>,
    pub fallback_to_text_extraction: bool,
    pub preserve_metadata: bool,
    pub extract_images: bool,
    pub image_output_dir: Option<PathBuf>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub max_file_size: u64,
    pub allowed_formats: Vec<String>,
    pub blocked_formats: Vec<String>,
    pub enable_sandbox: bool,
    pub validate_inputs: bool,
    pub enable_rate_limiting: bool,
    pub max_requests_per_minute: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub cache_size_mb: u64,
    pub enable_compression: bool,
    pub optimize_for_memory: bool,
    pub optimize_for_speed: bool,
    pub enable_metrics: bool,
    pub metrics_interval_seconds: u64,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            features: FeatureFlags::default(),
            processing: ProcessingConfig::default(),
            mcp_server: McpServerConfig::default(),
            legacy_formats: LegacyFormatsConfig::default(),
            security: SecurityConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

impl Default for FeatureFlags {
    fn default() -> Self {
        Self {
            format_doc: true,
            format_wordperfect: true,
            format_dbase: true,
            format_wordstar: true,
            format_lotus: true,
            dll_building: cfg!(target_os = "windows"),
            parallel_processing: true,
            advanced_formatting: true,
            batch_conversion: true,
        }
    }
}

impl Default for ProcessingConfig {
    fn default() -> Self {
        Self {
            max_file_size_mb: 100,
            parallel_jobs: num_cpus::get(),
            timeout_seconds: 300, // 5 minutes
            temp_dir: None, // Use system temp
            enable_simd: cfg!(any(target_arch = "x86", target_arch = "x86_64")),
            memory_limit_mb: None,
        }
    }
}

impl Default for McpServerConfig {
    fn default() -> Self {
        Self {
            protocol_version: "2024-11-05".to_string(),
            transport: TransportType::Stdio,
            enable_logging: true,
            log_level: "info".to_string(),
            enable_tracing: true,
            server_name: "LegacyBridge MCP Server".to_string(),
            server_version: "2.0.0".to_string(),
        }
    }
}

impl Default for LegacyFormatsConfig {
    fn default() -> Self {
        Self {
            enable_doc: true,
            enable_wordperfect: true,
            enable_dbase: true,
            enable_lotus: true,
            enable_wordstar: true,
            libreoffice_path: None, // Auto-detect
            fallback_to_text_extraction: true,
            preserve_metadata: true,
            extract_images: false, // Disabled by default for security
            image_output_dir: None,
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            max_file_size: 100 * 1024 * 1024, // 100MB
            allowed_formats: vec![
                "rtf".to_string(), "md".to_string(), "txt".to_string(),
                "html".to_string(), "xml".to_string(), "json".to_string(),
                "csv".to_string(), "doc".to_string(), "wpd".to_string(),
                "dbf".to_string(), "wk1".to_string(), "ws".to_string(),
            ],
            blocked_formats: vec![], // None blocked by default
            enable_sandbox: true,
            validate_inputs: true,
            enable_rate_limiting: false, // Disabled by default for MCP
            max_requests_per_minute: 60,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            cache_size_mb: 50,
            enable_compression: true,
            optimize_for_memory: false,
            optimize_for_speed: true,
            enable_metrics: true,
            metrics_interval_seconds: 60,
        }
    }
}

impl Config {
    /// Load configuration from file or environment
    pub fn load() -> Result<Self, ConfigError> {
        // Try to load from file first
        if let Ok(config) = Self::load_from_file() {
            info!("Loaded configuration from file");
            return Ok(config);
        }
        
        // Try to load from environment
        if let Ok(config) = Self::load_from_env() {
            info!("Loaded configuration from environment");
            return Ok(config);
        }
        
        // Use defaults
        info!("Using default configuration");
        Ok(Self::default())
    }
    
    /// Load configuration from file
    pub fn load_from_file() -> Result<Self, ConfigError> {
        let config_paths = [
            "legacybridge.toml",
            "config/legacybridge.toml",
            "~/.config/legacybridge/config.toml",
            "/etc/legacybridge/config.toml",
        ];
        
        for path in &config_paths {
            let expanded_path = shellexpand::tilde(path);
            let path = std::path::Path::new(expanded_path.as_ref());
            
            if path.exists() {
                let content = std::fs::read_to_string(path)
                    .map_err(|e| ConfigError::IoError(format!("Failed to read config file {}: {}", path.display(), e)))?;
                
                let config: Config = toml::from_str(&content)
                    .map_err(|e| ConfigError::ParseError(format!("Failed to parse config file {}: {}", path.display(), e)))?;
                
                return Ok(config);
            }
        }
        
        Err(ConfigError::NotFound("No configuration file found".to_string()))
    }
    
    /// Load configuration from environment variables
    pub fn load_from_env() -> Result<Self, ConfigError> {
        let mut config = Self::default();
        
        // Feature flags
        if let Ok(val) = std::env::var("LEGACYBRIDGE_FORMAT_DOC") {
            config.features.format_doc = val.parse().unwrap_or(config.features.format_doc);
        }
        if let Ok(val) = std::env::var("LEGACYBRIDGE_FORMAT_WORDPERFECT") {
            config.features.format_wordperfect = val.parse().unwrap_or(config.features.format_wordperfect);
        }
        if let Ok(val) = std::env::var("LEGACYBRIDGE_FORMAT_DBASE") {
            config.features.format_dbase = val.parse().unwrap_or(config.features.format_dbase);
        }
        if let Ok(val) = std::env::var("LEGACYBRIDGE_FORMAT_LOTUS") {
            config.features.format_lotus = val.parse().unwrap_or(config.features.format_lotus);
        }
        if let Ok(val) = std::env::var("LEGACYBRIDGE_FORMAT_WORDSTAR") {
            config.features.format_wordstar = val.parse().unwrap_or(config.features.format_wordstar);
        }
        
        // Processing config
        if let Ok(val) = std::env::var("LEGACYBRIDGE_MAX_FILE_SIZE_MB") {
            config.processing.max_file_size_mb = val.parse().unwrap_or(config.processing.max_file_size_mb);
        }
        if let Ok(val) = std::env::var("LEGACYBRIDGE_PARALLEL_JOBS") {
            config.processing.parallel_jobs = val.parse().unwrap_or(config.processing.parallel_jobs);
        }
        if let Ok(val) = std::env::var("LEGACYBRIDGE_TIMEOUT_SECONDS") {
            config.processing.timeout_seconds = val.parse().unwrap_or(config.processing.timeout_seconds);
        }
        
        // MCP server config
        if let Ok(val) = std::env::var("LEGACYBRIDGE_MCP_LOG_LEVEL") {
            config.mcp_server.log_level = val;
        }
        if let Ok(val) = std::env::var("LEGACYBRIDGE_MCP_ENABLE_LOGGING") {
            config.mcp_server.enable_logging = val.parse().unwrap_or(config.mcp_server.enable_logging);
        }
        
        // Security config
        if let Ok(val) = std::env::var("LEGACYBRIDGE_MAX_FILE_SIZE") {
            config.security.max_file_size = val.parse().unwrap_or(config.security.max_file_size);
        }
        if let Ok(val) = std::env::var("LEGACYBRIDGE_ENABLE_SANDBOX") {
            config.security.enable_sandbox = val.parse().unwrap_or(config.security.enable_sandbox);
        }
        
        Ok(config)
    }
    
    /// Save configuration to file
    pub fn save_to_file(&self, path: &std::path::Path) -> Result<(), ConfigError> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| ConfigError::SerializeError(format!("Failed to serialize config: {}", e)))?;
        
        if let Some(parent) = path.parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| ConfigError::IoError(format!("Failed to create config directory: {}", e)))?;
        }
        
        std::fs::write(path, content)
            .map_err(|e| ConfigError::IoError(format!("Failed to write config file: {}", e)))?;
        
        info!("Configuration saved to {}", path.display());
        Ok(())
    }
    
    /// Validate configuration
    pub fn validate(&self) -> Result<(), ConfigError> {
        // Validate file size limits
        if self.processing.max_file_size_mb == 0 {
            return Err(ConfigError::ValidationError("max_file_size_mb must be greater than 0".to_string()));
        }
        
        if self.security.max_file_size == 0 {
            return Err(ConfigError::ValidationError("security.max_file_size must be greater than 0".to_string()));
        }
        
        // Validate parallel jobs
        if self.processing.parallel_jobs == 0 {
            return Err(ConfigError::ValidationError("parallel_jobs must be greater than 0".to_string()));
        }
        
        // Validate timeout
        if self.processing.timeout_seconds == 0 {
            return Err(ConfigError::ValidationError("timeout_seconds must be greater than 0".to_string()));
        }
        
        // Validate protocol version
        let valid_versions = ["2024-11-05", "2025-03-26", "2025-06-18"];
        if !valid_versions.contains(&self.mcp_server.protocol_version.as_str()) {
            warn!("Protocol version {} may not be supported", self.mcp_server.protocol_version);
        }
        
        // Validate log level
        let valid_log_levels = ["error", "warn", "info", "debug", "trace"];
        if !valid_log_levels.contains(&self.mcp_server.log_level.as_str()) {
            return Err(ConfigError::ValidationError(format!("Invalid log level: {}", self.mcp_server.log_level)));
        }
        
        // Check for conflicting optimization settings
        if self.performance.optimize_for_memory && self.performance.optimize_for_speed {
            warn!("Both memory and speed optimization enabled - speed will take precedence");
        }
        
        // Validate temp directory if specified
        if let Some(temp_dir) = &self.processing.temp_dir {
            if !temp_dir.exists() && std::fs::create_dir_all(temp_dir).is_err() {
                return Err(ConfigError::ValidationError(format!("Cannot create temp directory: {}", temp_dir.display())));
            }
        }
        
        // Check LibreOffice path if specified
        if let Some(libreoffice_path) = &self.legacy_formats.libreoffice_path {
            if !libreoffice_path.exists() {
                warn!("LibreOffice path does not exist: {}", libreoffice_path.display());
            }
        }
        
        Ok(())
    }
    
    /// Get effective temp directory
    pub fn get_temp_dir(&self) -> PathBuf {
        self.processing.temp_dir.clone()
            .unwrap_or_else(|| std::env::temp_dir().join("legacybridge"))
    }
    
    /// Check if a format is enabled
    pub fn is_format_enabled(&self, format: &str) -> bool {
        match format {
            "doc" => self.features.format_doc && self.legacy_formats.enable_doc,
            "wpd" => self.features.format_wordperfect && self.legacy_formats.enable_wordperfect,
            "dbf" => self.features.format_dbase && self.legacy_formats.enable_dbase,
            "wk1" | "wks" | "123" => self.features.format_lotus && self.legacy_formats.enable_lotus,
            "ws" | "wsd" => self.features.format_wordstar && self.legacy_formats.enable_wordstar,
            _ => true, // Modern formats are always enabled
        }
    }
    
    /// Check if a format is allowed by security policy
    pub fn is_format_allowed(&self, format: &str) -> bool {
        if !self.security.blocked_formats.is_empty() && self.security.blocked_formats.contains(&format.to_string()) {
            return false;
        }
        
        if !self.security.allowed_formats.is_empty() {
            return self.security.allowed_formats.contains(&format.to_string());
        }
        
        true
    }
    
    /// Get memory limit in bytes
    pub fn get_memory_limit_bytes(&self) -> Option<u64> {
        self.processing.memory_limit_mb.map(|mb| mb * 1024 * 1024)
    }
    
    /// Create a sample configuration file
    pub fn create_sample_config(path: &std::path::Path) -> Result<(), ConfigError> {
        let sample_config = Self::default();
        sample_config.save_to_file(path)
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ConfigError {
    #[error("Configuration not found: {0}")]
    NotFound(String),
    
    #[error("IO error: {0}")]
    IoError(String),
    
    #[error("Parse error: {0}")]
    ParseError(String),
    
    #[error("Serialization error: {0}")]
    SerializeError(String),
    
    #[error("Validation error: {0}")]
    ValidationError(String),
}

// Feature detection utilities
pub mod feature_detection {
    use super::*;
    
    /// Check if LibreOffice is available
    pub async fn check_libreoffice_available() -> bool {
        tokio::process::Command::new("libreoffice")
            .arg("--version")
            .output()
            .await
            .map(|output| output.status.success())
            .unwrap_or(false)
    }
    
    /// Detect available features based on system capabilities
    pub async fn detect_features() -> FeatureFlags {
        let mut features = FeatureFlags::default();
        
        // Check if LibreOffice is available for legacy format conversion
        if !check_libreoffice_available().await {
            warn!("LibreOffice not found - some legacy format conversions may fail");
            // Don't disable features, just warn
        }
        
        // Check for compiler availability for DLL building
        if cfg!(target_os = "windows") {
            let has_compiler = tokio::process::Command::new("cl")
                .arg("/?")
                .output()
                .await
                .map(|output| output.status.success())
                .unwrap_or(false);
            
            if !has_compiler {
                warn!("No MSVC compiler found - DLL building disabled");
                features.dll_building = false;
            }
        } else {
            features.dll_building = false;
        }
        
        features
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_config() {
        let config = Config::default();
        assert!(config.features.format_doc);
        assert!(config.features.format_wordperfect);
        assert_eq!(config.mcp_server.protocol_version, "2024-11-05");
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_format_enabled() {
        let config = Config::default();
        assert!(config.is_format_enabled("doc"));
        assert!(config.is_format_enabled("rtf"));
        assert!(config.is_format_enabled("md"));
    }
    
    #[test]
    fn test_format_allowed() {
        let config = Config::default();
        assert!(config.is_format_allowed("doc"));
        assert!(config.is_format_allowed("rtf"));
        
        let mut restricted_config = config.clone();
        restricted_config.security.allowed_formats = vec!["rtf".to_string()];
        assert!(!restricted_config.is_format_allowed("doc"));
        assert!(restricted_config.is_format_allowed("rtf"));
    }
    
    #[test]
    fn test_config_validation() {
        let mut config = Config::default();
        
        // Valid config should pass
        assert!(config.validate().is_ok());
        
        // Invalid file size should fail
        config.processing.max_file_size_mb = 0;
        assert!(config.validate().is_err());
        
        // Invalid log level should fail
        config.processing.max_file_size_mb = 100;
        config.mcp_server.log_level = "invalid".to_string();
        assert!(config.validate().is_err());
    }
}