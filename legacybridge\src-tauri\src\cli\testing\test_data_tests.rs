use super::*;
use std::fs;
use std::path::PathBuf;
use tempfile::TempDir;
use chrono::Utc;

#[cfg(test)]
mod test_data_manager_tests {
    use super::*;

    fn create_test_manager() -> (TestDataManager, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let manager = TestDataManager::new(&temp_dir.path()).unwrap();
        (manager, temp_dir)
    }

    #[test]
    fn test_manager_creation() {
        let temp_dir = TempDir::new().unwrap();
        let manager = TestDataManager::new(&temp_dir.path()).unwrap();
        
        // Verify directory structure was created
        assert!(temp_dir.path().join("samples").exists());
        assert!(temp_dir.path().join("baselines").exists());
        assert!(temp_dir.path().join("exports").exists());
    }

    #[test]
    fn test_generate_sample_files() {
        let (mut manager, _temp_dir) = create_test_manager();
        
        // Generate RTF sample files
        let result = manager.generate_sample_files("rtf", 3);
        assert!(result.is_ok());
        
        // Check that files were created with correct tags
        let rtf_files = manager.get_by_tags(&["rtf".to_string()]);
        assert!(rtf_files.len() >= 3);
        
        // Verify file sizes
        let small_files = manager.get_by_tags(&["rtf".to_string(), "small".to_string()]);
        assert!(!small_files.is_empty());
        
        for file in small_files {
            let size = fs::metadata(&file.path).unwrap().len();
            assert!(size <= 1024 * 1024); // Less than 1MB
        }
    }

    #[test]
    fn test_store_and_retrieve_baseline() {
        let (mut manager, _temp_dir) = create_test_manager();
        
        let metrics = PerformanceMetrics {
            duration_ms: 123.45,
            memory_bytes: 1024 * 1024,
            cpu_percent: 25.5,
            throughput_mbps: 10.0,
            timestamp: Utc::now(),
        };
        
        // Store baseline
        manager.store_baseline("test_conversion", metrics.clone()).unwrap();
        
        // Retrieve baseline
        let retrieved = manager.get_baseline("test_conversion").unwrap();
        assert!(retrieved.is_some());
        
        let baseline = retrieved.unwrap();
        assert_eq!(baseline.duration_ms, metrics.duration_ms);
        assert_eq!(baseline.memory_bytes, metrics.memory_bytes);
    }

    #[test]
    fn test_add_test_item() {
        let (mut manager, temp_dir) = create_test_manager();
        
        // Create a test file
        let test_file = temp_dir.path().join("test.rtf");
        fs::write(&test_file, b"test content").unwrap();
        
        // Add test item
        let item = manager.add_test_item(
            test_file.clone(),
            "Test RTF file".to_string(),
            vec!["rtf".to_string(), "manual".to_string()],
        ).unwrap();
        
        assert_eq!(item.path, test_file);
        assert_eq!(item.description, "Test RTF file");
        assert!(item.tags.contains(&"rtf".to_string()));
        assert!(item.tags.contains(&"manual".to_string()));
    }

    #[test]
    fn test_get_by_tags() {
        let (mut manager, _temp_dir) = create_test_manager();
        
        // Generate files with different tags
        manager.generate_sample_files("rtf", 2).unwrap();
        manager.generate_sample_files("doc", 2).unwrap();
        
        // Test single tag filtering
        let rtf_files = manager.get_by_tags(&["rtf".to_string()]);
        assert!(rtf_files.len() >= 2);
        
        let doc_files = manager.get_by_tags(&["doc".to_string()]);
        assert!(doc_files.len() >= 2);
        
        // Test multiple tag filtering (AND operation)
        let small_rtf = manager.get_by_tags(&["rtf".to_string(), "small".to_string()]);
        assert!(!small_rtf.is_empty());
        for item in small_rtf {
            assert!(item.tags.contains(&"rtf".to_string()));
            assert!(item.tags.contains(&"small".to_string()));
        }
    }

    #[test]
    fn test_compare_baseline() {
        let (mut manager, _temp_dir) = create_test_manager();
        
        let baseline = PerformanceMetrics {
            duration_ms: 100.0,
            memory_bytes: 1024 * 1024,
            cpu_percent: 20.0,
            throughput_mbps: 10.0,
            timestamp: Utc::now(),
        };
        
        manager.store_baseline("test_op", baseline).unwrap();
        
        // Test performance improvement
        let current_good = PerformanceMetrics {
            duration_ms: 90.0,
            memory_bytes: 1024 * 1024,
            cpu_percent: 18.0,
            throughput_mbps: 11.0,
            timestamp: Utc::now(),
        };
        
        let comparison_good = manager.compare_baseline("test_op", &current_good).unwrap();
        assert!(comparison_good.is_some());
        let comp = comparison_good.unwrap();
        assert!(comp.percent_change < 0.0); // Negative means improvement
        assert!(comp.improvement_detected);
        assert!(!comp.regression_detected);
        
        // Test performance regression
        let current_bad = PerformanceMetrics {
            duration_ms: 150.0,
            memory_bytes: 2 * 1024 * 1024,
            cpu_percent: 30.0,
            throughput_mbps: 8.0,
            timestamp: Utc::now(),
        };
        
        let comparison_bad = manager.compare_baseline("test_op", &current_bad).unwrap();
        assert!(comparison_bad.is_some());
        let comp = comparison_bad.unwrap();
        assert!(comp.percent_change > 0.0); // Positive means regression
        assert!(!comp.improvement_detected);
        assert!(comp.regression_detected);
    }

    #[test]
    fn test_export_test_data() {
        let (mut manager, temp_dir) = create_test_manager();
        
        // Generate some test data
        manager.generate_sample_files("rtf", 2).unwrap();
        
        // Export test data
        let export_path = temp_dir.path().join("export.json");
        manager.export_test_data(&export_path).unwrap();
        
        assert!(export_path.exists());
        
        // Verify export content
        let content = fs::read_to_string(&export_path).unwrap();
        let data: serde_json::Value = serde_json::from_str(&content).unwrap();
        assert!(data["items"].is_array());
        assert!(data["baselines"].is_object());
    }

    #[test]
    fn test_clean_old_data() {
        let (mut manager, _temp_dir) = create_test_manager();
        
        // Generate test data
        manager.generate_sample_files("rtf", 5).unwrap();
        
        let initial_count = manager.list_test_data().len();
        assert!(initial_count >= 5);
        
        // Clean old data (should not remove anything since they're all new)
        let removed = manager.clean_old_data(30).unwrap();
        assert_eq!(removed, 0);
        
        // Verify nothing was removed
        assert_eq!(manager.list_test_data().len(), initial_count);
    }

    #[test]
    fn test_generate_rtf_content() {
        let content = TestDataManager::generate_rtf_content(100);
        
        // Verify RTF structure
        assert!(content.starts_with(r"{\rtf1"));
        assert!(content.ends_with("}"));
        assert!(content.contains(r"\par"));
        
        // Verify approximate size (RTF overhead makes it larger)
        assert!(content.len() > 100);
    }

    #[test]
    fn test_generate_markdown_content() {
        let content = TestDataManager::generate_markdown_content(100);
        
        // Verify Markdown structure
        assert!(content.contains('#'));
        assert!(content.contains("Lorem ipsum"));
        
        // Verify approximate size
        assert!(content.len() >= 100);
    }

    #[test]
    fn test_get_sample_file_for_format() {
        let (mut manager, _temp_dir) = create_test_manager();
        
        // Generate sample files
        manager.generate_sample_files("rtf", 3).unwrap();
        
        // Get sample file
        let sample = manager.get_sample_file("rtf", SizeCategory::Medium).unwrap();
        assert!(sample.is_some());
        
        let file = sample.unwrap();
        assert!(file.path.exists());
        assert!(file.tags.contains(&"rtf".to_string()));
        assert!(file.tags.contains(&"medium".to_string()));
    }

    #[test]
    fn test_baseline_comparison_edge_cases() {
        let (mut manager, _temp_dir) = create_test_manager();
        
        // Test comparison with no baseline
        let metrics = PerformanceMetrics {
            duration_ms: 100.0,
            memory_bytes: 1024,
            cpu_percent: 20.0,
            throughput_mbps: 10.0,
            timestamp: Utc::now(),
        };
        
        let comparison = manager.compare_baseline("nonexistent", &metrics).unwrap();
        assert!(comparison.is_none());
    }

    #[test]
    fn test_size_category_ordering() {
        assert!(SizeCategory::Tiny < SizeCategory::Small);
        assert!(SizeCategory::Small < SizeCategory::Medium);
        assert!(SizeCategory::Medium < SizeCategory::Large);
        assert!(SizeCategory::Large < SizeCategory::ExtraLarge);
    }

    #[test]
    fn test_size_category_string_conversion() {
        assert_eq!(SizeCategory::Tiny.as_str(), "tiny");
        assert_eq!(SizeCategory::Small.as_str(), "small");
        assert_eq!(SizeCategory::Medium.as_str(), "medium");
        assert_eq!(SizeCategory::Large.as_str(), "large");
        assert_eq!(SizeCategory::ExtraLarge.as_str(), "extra_large");
    }
}