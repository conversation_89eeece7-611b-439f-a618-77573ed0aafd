use std::path::{Path, PathBuf};
use std::time::{Duration, Instant};
use std::fs;
use std::io::Write;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Local};
use crate::cli::{<PERSON><PERSON><PERSON><PERSON><PERSON>, CliR<PERSON>ult};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchConversionResult {
    pub source_file: PathBuf,
    pub output_file: PathBuf,
    pub status: ConversionStatus,
    pub duration: Duration,
    pub file_size: u64,
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConversionStatus {
    Success,
    Failed,
    Skipped,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchReport {
    pub start_time: DateTime<Local>,
    pub end_time: Option<DateTime<Local>>,
    pub total_duration: Option<Duration>,
    pub input_directory: <PERSON><PERSON><PERSON>,
    pub output_directory: <PERSON><PERSON><PERSON>,
    pub total_files: usize,
    pub successful_conversions: usize,
    pub failed_conversions: usize,
    pub skipped_files: usize,
    pub total_input_size: u64,
    pub total_output_size: u64,
    pub conversion_results: Vec<BatchConversionResult>,
    pub configuration: BatchConfiguration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchConfiguration {
    pub pattern: String,
    pub output_format: String,
    pub recursive: bool,
    pub preserve_structure: bool,
    pub parallel_jobs: usize,
    pub continue_on_error: bool,
    pub filters: FilterConfiguration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilterConfiguration {
    pub include_patterns: Vec<String>,
    pub exclude_patterns: Vec<String>,
    pub min_size: Option<u64>,
    pub max_size: Option<u64>,
}

impl BatchReport {
    pub fn new(
        input_dir: PathBuf,
        output_dir: PathBuf,
        configuration: BatchConfiguration,
    ) -> Self {
        Self {
            start_time: Local::now(),
            end_time: None,
            total_duration: None,
            input_directory: input_dir,
            output_directory: output_dir,
            total_files: 0,
            successful_conversions: 0,
            failed_conversions: 0,
            skipped_files: 0,
            total_input_size: 0,
            total_output_size: 0,
            conversion_results: Vec::new(),
            configuration,
        }
    }

    pub fn add_result(&mut self, result: BatchConversionResult) {
        match result.status {
            ConversionStatus::Success => self.successful_conversions += 1,
            ConversionStatus::Failed => self.failed_conversions += 1,
            ConversionStatus::Skipped => self.skipped_files += 1,
        }

        self.total_input_size += result.file_size;
        
        if result.status == ConversionStatus::Success {
            if let Ok(metadata) = fs::metadata(&result.output_file) {
                self.total_output_size += metadata.len();
            }
        }

        self.conversion_results.push(result);
        self.total_files = self.conversion_results.len();
    }

    pub fn finalize(&mut self) {
        self.end_time = Some(Local::now());
        if let Some(end) = self.end_time {
            self.total_duration = Some(end.signed_duration_since(self.start_time).to_std().unwrap_or_default());
        }
    }

    pub fn save_to_file(&self, path: &Path) -> CliResult<()> {
        let json = serde_json::to_string_pretty(self)
            .map_err(|e| CliError::Io(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                format!("Failed to serialize report: {}", e)
            )))?;

        let mut file = fs::File::create(path)?;
        file.write_all(json.as_bytes())?;
        
        Ok(())
    }

    pub fn save_as_csv(&self, path: &Path) -> CliResult<()> {
        let mut writer = csv::Writer::from_path(path)?;

        // Write headers
        writer.write_record(&[
            "Source File",
            "Output File",
            "Status",
            "Duration (ms)",
            "File Size (bytes)",
            "Error Message",
        ])?;

        // Write data
        for result in &self.conversion_results {
            writer.write_record(&[
                result.source_file.to_string_lossy().to_string(),
                result.output_file.to_string_lossy().to_string(),
                format!("{:?}", result.status),
                result.duration.as_millis().to_string(),
                result.file_size.to_string(),
                result.error_message.as_deref().unwrap_or(""),
            ])?;
        }

        writer.flush()?;
        Ok(())
    }

    pub fn print_summary(&self) {
        println!("\n📊 Batch Processing Report");
        println!("═══════════════════════════════════════════════════════════════");

        println!("\n⏱️  Time Information:");
        println!("   Start time: {}", self.start_time.format("%Y-%m-%d %H:%M:%S"));
        if let Some(end_time) = &self.end_time {
            println!("   End time: {}", end_time.format("%Y-%m-%d %H:%M:%S"));
        }
        if let Some(duration) = &self.total_duration {
            println!("   Total duration: {:.2}s", duration.as_secs_f64());
        }

        println!("\n📁 Directory Information:");
        println!("   Input directory: {}", self.input_directory.display());
        println!("   Output directory: {}", self.output_directory.display());

        println!("\n📈 Conversion Statistics:");
        println!("   Total files processed: {}", self.total_files);
        println!("   ✅ Successful: {} ({:.1}%)", 
            self.successful_conversions,
            (self.successful_conversions as f64 / self.total_files as f64) * 100.0
        );
        println!("   ❌ Failed: {} ({:.1}%)", 
            self.failed_conversions,
            (self.failed_conversions as f64 / self.total_files as f64) * 100.0
        );
        println!("   ⏭️  Skipped: {} ({:.1}%)", 
            self.skipped_files,
            (self.skipped_files as f64 / self.total_files as f64) * 100.0
        );

        println!("\n💾 Size Information:");
        println!("   Total input size: {}", format_bytes(self.total_input_size));
        println!("   Total output size: {}", format_bytes(self.total_output_size));
        let compression_ratio = if self.total_input_size > 0 {
            (self.total_output_size as f64 / self.total_input_size as f64) * 100.0
        } else {
            0.0
        };
        println!("   Size ratio: {:.1}%", compression_ratio);

        if self.total_files > 0 && self.total_duration.is_some() {
            let duration = self.total_duration.unwrap();
            let files_per_second = self.total_files as f64 / duration.as_secs_f64();
            let mb_per_second = (self.total_input_size as f64 / 1_048_576.0) / duration.as_secs_f64();
            
            println!("\n⚡ Performance:");
            println!("   Files per second: {:.2}", files_per_second);
            println!("   Throughput: {:.2} MB/s", mb_per_second);
            println!("   Average time per file: {:.2}ms", 
                duration.as_millis() as f64 / self.total_files as f64
            );
        }

        if self.failed_conversions > 0 {
            println!("\n❌ Failed Conversions:");
            for (i, result) in self.conversion_results.iter()
                .filter(|r| r.status == ConversionStatus::Failed)
                .take(5)
                .enumerate() 
            {
                println!("   {}. {}: {}", 
                    i + 1,
                    result.source_file.display(),
                    result.error_message.as_deref().unwrap_or("Unknown error")
                );
            }
            
            if self.failed_conversions > 5 {
                println!("   ... and {} more failures", self.failed_conversions - 5);
            }
        }

        println!("\n═══════════════════════════════════════════════════════════════");
    }
}

fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}

pub struct BatchReportBuilder {
    report: BatchReport,
}

impl BatchReportBuilder {
    pub fn new(
        input_dir: PathBuf,
        output_dir: PathBuf,
        pattern: String,
        output_format: String,
    ) -> Self {
        let configuration = BatchConfiguration {
            pattern,
            output_format,
            recursive: false,
            preserve_structure: true,
            parallel_jobs: 1,
            continue_on_error: false,
            filters: FilterConfiguration {
                include_patterns: Vec::new(),
                exclude_patterns: Vec::new(),
                min_size: None,
                max_size: None,
            },
        };

        Self {
            report: BatchReport::new(input_dir, output_dir, configuration),
        }
    }

    pub fn with_recursive(mut self, recursive: bool) -> Self {
        self.report.configuration.recursive = recursive;
        self
    }

    pub fn with_parallel_jobs(mut self, jobs: usize) -> Self {
        self.report.configuration.parallel_jobs = jobs;
        self
    }

    pub fn with_continue_on_error(mut self, continue_on_error: bool) -> Self {
        self.report.configuration.continue_on_error = continue_on_error;
        self
    }

    pub fn with_filters(
        mut self,
        include: Vec<String>,
        exclude: Vec<String>,
        min_size: Option<u64>,
        max_size: Option<u64>,
    ) -> Self {
        self.report.configuration.filters = FilterConfiguration {
            include_patterns: include,
            exclude_patterns: exclude,
            min_size,
            max_size,
        };
        self
    }

    pub fn build(self) -> BatchReport {
        self.report
    }
}