// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod commands;
mod commands_dll;
mod conversion;
mod pipeline;
mod security;
mod memory_pool_optimization;
mod panic_handler;
mod ffi;
mod ffi_error_bridge;
mod ffi_legacy;
mod formats;
mod dll;
mod performance;
mod enterprise;

use commands::{
    get_version_info, markdown_to_rtf, rtf_to_markdown, test_connection,
    read_rtf_file, write_markdown_file, read_file_base64, write_file_base64,
    batch_convert_rtf_to_markdown, rtf_to_markdown_pipeline, read_rtf_file_pipeline,
    markdown_to_rtf_pipeline, read_markdown_file_pipeline,
};

use commands_dll::{
    dll_validate_config, dll_build, dll_test, dll_generate_code,
    dll_create_package, dll_inspect, dll_run_performance_benchmarks,
    dll_run_security_checks, dll_validate_packaging, dll_download_package,
    dll_get_package_contents, dll_verify_package, DllBuildState,
};

use enterprise::{EnterpriseFeatures, EnterpriseConfig};

fn main() {
    // Initialize security configurations
    let rate_limiter = std::sync::Arc::new(security::RateLimiter::new());
    
    // Initialize performance optimizer for enterprise-grade monitoring
    let performance_optimizer = std::sync::Arc::new(
        performance::PerformanceOptimizer::new()
            .expect("Failed to initialize performance optimizer")
    );
    
    // Initialize enterprise features
    let enterprise_features = std::sync::Arc::new(tokio::sync::Mutex::new(
        EnterpriseFeatures::new(EnterpriseConfig::default())
    ));
    
    tauri::Builder::default()
        .manage(DllBuildState::default())
        .manage(performance_optimizer.clone())
        .manage(enterprise_features.clone())
        .setup(move |app| {
            // Start performance monitoring
            let perf_opt = performance_optimizer.clone();
            tauri::async_runtime::spawn(async move {
                // Monitor will start its own background task
                let _ = perf_opt.get_health_status().await;
            });
            
            // Start enterprise features
            let enterprise = enterprise_features.clone();
            tauri::async_runtime::spawn(async move {
                let mut enterprise_guard = enterprise.lock().await;
                if let Err(e) = enterprise_guard.start().await {
                    eprintln!("Failed to start enterprise features: {}", e);
                }
            });
            
            // Apply security headers if needed for web requests
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Existing commands
            rtf_to_markdown,
            markdown_to_rtf,
            test_connection,
            get_version_info,
            read_rtf_file,
            write_markdown_file,
            read_file_base64,
            write_file_base64,
            batch_convert_rtf_to_markdown,
            rtf_to_markdown_pipeline,
            read_rtf_file_pipeline,
            markdown_to_rtf_pipeline,
            read_markdown_file_pipeline,
            // DLL commands
            dll_validate_config,
            dll_build,
            dll_test,
            dll_generate_code,
            dll_create_package,
            dll_inspect,
            dll_run_performance_benchmarks,
            dll_run_security_checks,
            dll_validate_packaging,
            dll_download_package,
            dll_get_package_contents,
            dll_verify_package
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
