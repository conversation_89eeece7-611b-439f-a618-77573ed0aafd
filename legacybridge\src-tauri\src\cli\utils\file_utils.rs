use crate::cli::{C<PERSON><PERSON><PERSON><PERSON>, CliResult};
use std::fs;
use std::path::{Path, PathBuf};
use std::io::{Read, Write};

/// File utilities for CLI operations
pub struct FileUtils;

impl FileUtils {
    /// Read file contents safely with validation
    pub fn read_file_contents(path: &Path) -> CliResult<String> {
        // Validate file exists and is readable
        if !path.exists() {
            return Err(CliError::FileNotFound(path.to_path_buf()));
        }
        
        if !path.is_file() {
            return Err(CliError::IsDirectory(path.to_path_buf()));
        }
        
        // Check file size to prevent memory exhaustion
        let metadata = fs::metadata(path)
            .map_err(|e| CliError::IoError(format!("Failed to read file metadata: {}", e)))?;
        
        const MAX_FILE_SIZE: u64 = 100 * 1024 * 1024; // 100MB limit
        if metadata.len() > MAX_FILE_SIZE {
            return Err(CliError::FileTooLarge(path.to_path_buf(), metadata.len()));
        }
        
        // Read file contents
        fs::read_to_string(path)
            .map_err(|e| CliError::IoError(format!("Failed to read file {}: {}", path.display(), e)))
    }
    
    /// Write content to file safely with backup option
    pub fn write_file_contents(
        path: &Path, 
        content: &str, 
        force: bool, 
        create_backup: bool
    ) -> CliResult<()> {
        // Check if file exists and force is not set
        if path.exists() && !force {
            return Err(CliError::FileExists(path.to_path_buf()));
        }
        
        // Create backup if requested and file exists
        if create_backup && path.exists() {
            let backup_path = Self::generate_backup_path(path)?;
            fs::copy(path, &backup_path)
                .map_err(|e| CliError::IoError(format!("Failed to create backup: {}", e)))?;
            println!("📦 Created backup: {}", backup_path.display());
        }
        
        // Ensure parent directory exists
        if let Some(parent) = path.parent() {
            Self::ensure_directory_exists(parent)?;
        }
        
        // Write content
        fs::write(path, content)
            .map_err(|e| CliError::IoError(format!("Failed to write file {}: {}", path.display(), e)))?;
        
        Ok(())
    }
    
    /// Ensure directory exists, creating it if necessary
    pub fn ensure_directory_exists(dir: &Path) -> CliResult<()> {
        if dir.exists() {
            if !dir.is_dir() {
                return Err(CliError::NotADirectory(dir.to_path_buf()));
            }
        } else {
            fs::create_dir_all(dir)
                .map_err(|e| CliError::IoError(format!("Failed to create directory {}: {}", dir.display(), e)))?;
        }
        Ok(())
    }
    
    /// Generate backup file path
    fn generate_backup_path(original: &Path) -> CliResult<PathBuf> {
        let parent = original.parent().unwrap_or(Path::new("."));
        let filename = original.file_name()
            .ok_or_else(|| CliError::InvalidPath(original.to_path_buf()))?;
        
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let backup_name = format!("{}.backup.{}", 
            filename.to_string_lossy(), 
            timestamp
        );
        
        Ok(parent.join(backup_name))
    }
    
    /// Detect file format from content and extension
    pub fn detect_file_format(path: &Path) -> CliResult<String> {
        // First try by extension
        if let Some(ext) = path.extension() {
            let ext_str = ext.to_string_lossy().to_lowercase();
            match ext_str.as_str() {
                "rtf" => return Ok("rtf".to_string()),
                "md" | "markdown" => return Ok("markdown".to_string()),
                "html" | "htm" => return Ok("html".to_string()),
                "txt" => return Ok("text".to_string()),
                _ => {}
            }
        }
        
        // Try to detect by content
        let content = Self::read_file_contents(path)?;
        
        // RTF detection
        if content.trim_start().starts_with("{\\rtf") {
            return Ok("rtf".to_string());
        }
        
        // Markdown detection (basic heuristics)
        if content.contains("# ") || content.contains("## ") || 
           content.contains("**") || content.contains("*") ||
           content.contains("```") || content.contains("[") {
            return Ok("markdown".to_string());
        }
        
        // HTML detection
        if content.trim_start().starts_with("<!DOCTYPE") || 
           content.trim_start().starts_with("<html") ||
           content.contains("<body>") || content.contains("<div>") {
            return Ok("html".to_string());
        }
        
        // Default to text
        Ok("text".to_string())
    }
    
    /// Get file metadata information
    pub fn get_file_info(path: &Path) -> CliResult<FileInfo> {
        let metadata = fs::metadata(path)
            .map_err(|e| CliError::IoError(format!("Failed to read metadata: {}", e)))?;
        
        Ok(FileInfo {
            path: path.to_path_buf(),
            size: metadata.len(),
            modified: metadata.modified().ok(),
            is_readonly: metadata.permissions().readonly(),
            format: Self::detect_file_format(path)?,
        })
    }
}

/// File information structure
#[derive(Debug, Clone)]
pub struct FileInfo {
    pub path: PathBuf,
    pub size: u64,
    pub modified: Option<std::time::SystemTime>,
    pub is_readonly: bool,
    pub format: String,
}

impl FileInfo {
    /// Get human readable file size
    pub fn size_human(&self) -> String {
        let size = self.size as f64;
        if size < 1024.0 {
            format!("{} B", size)
        } else if size < 1024.0 * 1024.0 {
            format!("{:.1} KB", size / 1024.0)
        } else if size < 1024.0 * 1024.0 * 1024.0 {
            format!("{:.1} MB", size / (1024.0 * 1024.0))
        } else {
            format!("{:.1} GB", size / (1024.0 * 1024.0 * 1024.0))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::tempdir;
    
    #[test]
    fn test_file_operations() {
        let temp_dir = tempdir().unwrap();
        let test_file = temp_dir.path().join("test.txt");
        
        // Test write
        let content = "Hello, World!";
        FileUtils::write_file_contents(&test_file, content, false, false).unwrap();
        
        // Test read
        let read_content = FileUtils::read_file_contents(&test_file).unwrap();
        assert_eq!(content, read_content);
        
        // Test file info
        let info = FileUtils::get_file_info(&test_file).unwrap();
        assert_eq!(info.size, content.len() as u64);
    }
    
    #[test]
    fn test_format_detection() {
        let temp_dir = tempdir().unwrap();
        
        // RTF file
        let rtf_file = temp_dir.path().join("test.rtf");
        fs::write(&rtf_file, "{\\rtf1 Hello World}").unwrap();
        assert_eq!(FileUtils::detect_file_format(&rtf_file).unwrap(), "rtf");
        
        // Markdown file
        let md_file = temp_dir.path().join("test.md");
        fs::write(&md_file, "# Header\n\nContent").unwrap();
        assert_eq!(FileUtils::detect_file_format(&md_file).unwrap(), "markdown");
    }
}