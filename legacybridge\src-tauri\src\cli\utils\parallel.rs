use rayon::prelude::*;
use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex};
use tokio::sync::Semaphore;
use indicatif::{ProgressBar, ParallelProgressIterator};
use crate::cli::CliError;
use super::file_utils::FileUtils;
use super::path_utils::PathUtils;
use crate::cli::output::progress::{ProgressReporter, parallel_progress_style};

/// Result of a parallel conversion operation
#[derive(Debug, Clone)]
pub struct ParallelResult<T> {
    pub path: PathBuf,
    pub result: Result<T, String>,
    pub duration_ms: u128,
}

/// Configuration for parallel processing
#[derive(Debug, Clone)]
pub struct ParallelConfig {
    /// Maximum number of concurrent operations
    pub max_concurrency: usize,
    /// Whether to continue on error
    pub continue_on_error: bool,
    /// Progress reporter
    pub progress: Option<Arc<ProgressReporter>>,
}

impl Default for ParallelConfig {
    fn default() -> Self {
        Self {
            max_concurrency: num_cpus::get(),
            continue_on_error: false,
            progress: None,
        }
    }
}

impl ParallelConfig {
    /// Create a new parallel configuration with sensible defaults
    pub fn new() -> Self {
        Self::default()
    }
    
    /// Set the maximum concurrency level
    pub fn with_max_concurrency(mut self, max: usize) -> Self {
        self.max_concurrency = max.max(1);
        self
    }
    
    /// Set whether to continue processing on errors
    pub fn with_continue_on_error(mut self, continue_on_error: bool) -> Self {
        self.continue_on_error = continue_on_error;
        self
    }
    
    /// Set the progress reporter
    pub fn with_progress(mut self, progress: Arc<ProgressReporter>) -> Self {
        self.progress = Some(progress);
        self
    }
    
    /// Calculate optimal number of threads based on file count and system resources
    pub fn optimal_thread_count(&self, file_count: usize) -> usize {
        // Don't use more threads than files
        let max_useful = file_count.min(self.max_concurrency);
        
        // For small file counts, use fewer threads to reduce overhead
        if file_count <= 4 {
            file_count.min(2)
        } else if file_count <= 10 {
            max_useful.min(4)
        } else {
            max_useful
        }
    }
}

/// Utilities for parallel file processing
pub struct ParallelProcessor;

impl ParallelProcessor {
    /// Process files in parallel with a custom operation
    pub fn process_files<F, T>(
        files: Vec<PathBuf>,
        config: ParallelConfig,
        operation: F,
    ) -> Vec<ParallelResult<T>>
    where
        F: Fn(&Path) -> Result<T, CliError> + Sync + Send,
        T: Send,
    {
        let start_time = std::time::Instant::now();
        let thread_count = config.optimal_thread_count(files.len());
        
        // Set up thread pool
        let pool = rayon::ThreadPoolBuilder::new()
            .num_threads(thread_count)
            .build()
            .unwrap_or_else(|_| rayon::ThreadPool::global());
        
        // Create results collector
        let results = Arc::new(Mutex::new(Vec::with_capacity(files.len())));
        let results_clone = Arc::clone(&results);
        
        // Process files in parallel
        pool.install(|| {
            let iter = files.into_par_iter();
            
            // Add progress tracking if available
            let iter = if let Some(ref progress) = config.progress {
                progress.set_message(format!("Processing with {} threads...", thread_count));
                iter.progress_with(progress.overall_progress.clone())
            } else {
                iter.progress_count(0) // No-op progress
            };
            
            iter.for_each(|path| {
                let file_start = std::time::Instant::now();
                
                // Create per-file progress if available
                let file_progress = config.progress.as_ref().and_then(|p| {
                    let file_size = FileUtils::get_file_info(&path)
                        .ok()
                        .map(|info| info.size)
                        .unwrap_or(0);
                    p.create_file_progress(&path, file_size)
                });
                
                // Process the file
                let result = operation(&path);
                
                // Update progress
                if let Some(pb) = file_progress {
                    if result.is_ok() {
                        pb.finish_with_message("✓");
                    } else {
                        pb.abandon_with_message("✗");
                    }
                }
                
                let duration_ms = file_start.elapsed().as_millis();
                
                // Store result
                let mut results_guard = results_clone.lock().unwrap();
                results_guard.push(ParallelResult {
                    path: path.clone(),
                    result: result.map_err(|e| e.to_string()),
                    duration_ms,
                });
                
                // Handle errors
                if !config.continue_on_error && result.is_err() {
                    // Signal early termination (Rayon doesn't have a built-in way)
                    // In practice, we'd need a more sophisticated approach
                }
            });
        });
        
        // Extract and sort results by original order
        let mut final_results = Arc::try_unwrap(results)
            .unwrap_or_else(|arc| (*arc.lock().unwrap()).clone());
        
        // Log summary
        if let Some(ref progress) = config.progress {
            let total_duration = start_time.elapsed();
            let success_count = final_results.iter().filter(|r| r.result.is_ok()).count();
            progress.set_message(format!(
                "Completed: {}/{} files in {:.2}s ({} threads)",
                success_count,
                final_results.len(),
                total_duration.as_secs_f64(),
                thread_count
            ));
        }
        
        final_results
    }
    
    /// Process files in parallel with async operations using Tokio
    pub async fn process_files_async<F, Fut, T>(
        files: Vec<PathBuf>,
        config: ParallelConfig,
        operation: F,
    ) -> Vec<ParallelResult<T>>
    where
        F: Fn(PathBuf) -> Fut + Clone,
        Fut: std::future::Future<Output = Result<T, CliError>> + Send,
        T: Send + 'static,
    {
        let semaphore = Arc::new(Semaphore::new(config.max_concurrency));
        let mut handles = Vec::new();
        
        for (idx, path) in files.into_iter().enumerate() {
            let permit = semaphore.clone();
            let op = operation.clone();
            let progress = config.progress.clone();
            
            let handle = tokio::spawn(async move {
                let _permit = permit.acquire().await.unwrap();
                let start_time = std::time::Instant::now();
                
                // Update progress
                if let Some(ref p) = progress {
                    p.set_message(format!("Processing file {}", idx + 1));
                }
                
                let result = op(path.clone()).await;
                let duration_ms = start_time.elapsed().as_millis();
                
                // Update progress
                if let Some(ref p) = progress {
                    p.increment();
                }
                
                ParallelResult {
                    path,
                    result: result.map_err(|e| e.to_string()),
                    duration_ms,
                }
            });
            
            handles.push(handle);
        }
        
        // Collect all results
        let mut results = Vec::new();
        for handle in handles {
            if let Ok(result) = handle.await {
                results.push(result);
            }
        }
        
        results
    }
    
    /// Group files by size for optimal parallel processing
    pub fn group_files_by_size(files: &[PathBuf], group_count: usize) -> Vec<Vec<PathBuf>> {
        // Get file sizes
        let mut file_sizes: Vec<(PathBuf, u64)> = files
            .iter()
            .filter_map(|path| {
                FileUtils::get_file_info(path)
                    .ok()
                    .map(|info| (path.clone(), info.size))
            })
            .collect();
        
        // Sort by size (largest first)
        file_sizes.sort_by(|a, b| b.1.cmp(&a.1));
        
        // Distribute files evenly across groups
        let mut groups: Vec<Vec<PathBuf>> = (0..group_count).map(|_| Vec::new()).collect();
        let mut group_sizes: Vec<u64> = vec![0; group_count];
        
        for (path, size) in file_sizes {
            // Find the group with the smallest total size
            let min_group = group_sizes
                .iter()
                .enumerate()
                .min_by_key(|(_, &size)| size)
                .map(|(idx, _)| idx)
                .unwrap_or(0);
            
            groups[min_group].push(path);
            group_sizes[min_group] += size;
        }
        
        groups
    }
    
    /// Create a progress bar for parallel operations
    pub fn create_parallel_progress(total: u64, message: &str) -> ProgressBar {
        let pb = ProgressBar::new(total);
        pb.set_style(parallel_progress_style());
        pb.set_message(message.to_string());
        pb
    }
}

/// Statistics for parallel operations
#[derive(Debug, Default)]
pub struct ParallelStats {
    pub total_files: usize,
    pub successful: usize,
    pub failed: usize,
    pub total_duration_ms: u128,
    pub average_duration_ms: f64,
    pub min_duration_ms: u128,
    pub max_duration_ms: u128,
}

impl ParallelStats {
    /// Calculate statistics from parallel results
    pub fn from_results<T>(results: &[ParallelResult<T>]) -> Self {
        let total_files = results.len();
        let successful = results.iter().filter(|r| r.result.is_ok()).count();
        let failed = total_files - successful;
        
        let durations: Vec<u128> = results.iter().map(|r| r.duration_ms).collect();
        let total_duration_ms: u128 = durations.iter().sum();
        let average_duration_ms = if total_files > 0 {
            total_duration_ms as f64 / total_files as f64
        } else {
            0.0
        };
        
        let min_duration_ms = durations.iter().min().copied().unwrap_or(0);
        let max_duration_ms = durations.iter().max().copied().unwrap_or(0);
        
        Self {
            total_files,
            successful,
            failed,
            total_duration_ms,
            average_duration_ms,
            min_duration_ms,
            max_duration_ms,
        }
    }
    
    /// Get a formatted summary of the statistics
    pub fn summary(&self) -> String {
        format!(
            "Processed {} files: {} successful, {} failed. \
             Total time: {:.2}s, Average: {:.2}ms/file, \
             Min: {}ms, Max: {}ms",
            self.total_files,
            self.successful,
            self.failed,
            self.total_duration_ms as f64 / 1000.0,
            self.average_duration_ms,
            self.min_duration_ms,
            self.max_duration_ms
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;
    
    #[test]
    fn test_parallel_config() {
        let config = ParallelConfig::new()
            .with_max_concurrency(4)
            .with_continue_on_error(true);
        
        assert_eq!(config.max_concurrency, 4);
        assert!(config.continue_on_error);
        
        // Test optimal thread count
        assert_eq!(config.optimal_thread_count(2), 2);
        assert_eq!(config.optimal_thread_count(8), 4);
        assert_eq!(config.optimal_thread_count(20), 4);
    }
    
    #[test]
    fn test_parallel_stats() {
        let results = vec![
            ParallelResult {
                path: PathBuf::from("file1.txt"),
                result: Ok(()),
                duration_ms: 100,
            },
            ParallelResult {
                path: PathBuf::from("file2.txt"),
                result: Err("error".to_string()),
                duration_ms: 50,
            },
            ParallelResult {
                path: PathBuf::from("file3.txt"),
                result: Ok(()),
                duration_ms: 150,
            },
        ];
        
        let stats = ParallelStats::from_results(&results);
        assert_eq!(stats.total_files, 3);
        assert_eq!(stats.successful, 2);
        assert_eq!(stats.failed, 1);
        assert_eq!(stats.total_duration_ms, 300);
        assert_eq!(stats.average_duration_ms, 100.0);
        assert_eq!(stats.min_duration_ms, 50);
        assert_eq!(stats.max_duration_ms, 150);
    }
    
    #[test]
    fn test_group_files_by_size() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();
        
        // Create test files with different sizes
        let files: Vec<PathBuf> = (0..6).map(|i| {
            let path = base_path.join(format!("file{}.txt", i));
            let content = "x".repeat(i * 100);
            fs::write(&path, content).unwrap();
            path
        }).collect();
        
        let groups = ParallelProcessor::group_files_by_size(&files, 2);
        assert_eq!(groups.len(), 2);
        
        // Verify distribution (larger files should be distributed evenly)
        let group1_size: usize = groups[0].len();
        let group2_size: usize = groups[1].len();
        assert!((group1_size as i32 - group2_size as i32).abs() <= 1);
    }
}