use clap::{Command, CommandFactory};
use colored::*;
use std::collections::HashMap;
use std::io::Write;
use crate::cli::app::Cli;

pub struct HelpSystem {
    context_hints: HashMap<String, Vec<String>>,
    examples: HashMap<String, Vec<Example>>,
}

#[derive(Clone)]
pub struct Example {
    pub command: String,
    pub description: String,
    pub output_hint: Option<String>,
}

impl HelpSystem {
    pub fn new() -> Self {
        let mut system = Self {
            context_hints: HashMap::new(),
            examples: HashMap::new(),
        };
        system.init_context_hints();
        system.init_examples();
        system
    }

    fn init_context_hints(&mut self) {
        // Convert command hints
        self.context_hints.insert("convert".to_string(), vec![
            "Use --preview to see what would be converted without making changes".to_string(),
            "Add --parallel for faster processing of multiple files".to_string(),
            "Use --verbose to see detailed conversion progress".to_string(),
            "Common formats: rtf, doc, wpd, md, txt, html".to_string(),
        ]);

        // Batch command hints
        self.context_hints.insert("batch".to_string(), vec![
            "Use --recursive to process subdirectories".to_string(),
            "Filter files with --pattern '*.doc,*.rtf'".to_string(),
            "Add --preserve-structure to maintain directory hierarchy".to_string(),
            "Use --max-size to limit processing to smaller files".to_string(),
        ]);

        // Detect command hints
        self.context_hints.insert("detect".to_string(), vec![
            "Use --detailed for comprehensive format analysis".to_string(),
            "Add --hex to see binary file headers".to_string(),
            "Use --suggest to get conversion recommendations".to_string(),
        ]);

        // Validate command hints
        self.context_hints.insert("validate".to_string(), vec![
            "Checks file integrity and readability".to_string(),
            "Use --strict for comprehensive validation".to_string(),
            "Add --repair to attempt fixing minor issues".to_string(),
        ]);

        // DLL command hints
        self.context_hints.insert("dll".to_string(), vec![
            "Build DLLs for VB6/VFP9 integration".to_string(),
            "Use --arch both to build x86 and x64 versions".to_string(),
            "Add --generate-vb6 for VB6 integration code".to_string(),
            "Use --test to verify DLL compatibility".to_string(),
        ]);

        // Serve command hints
        self.context_hints.insert("serve".to_string(), vec![
            "Start HTTP API server for web integration".to_string(),
            "Use --port to specify custom port (default: 8080)".to_string(),
            "Add --cors to enable cross-origin requests".to_string(),
            "Use --auth to require API key authentication".to_string(),
        ]);

        // Config command hints
        self.context_hints.insert("config".to_string(), vec![
            "Manage configuration profiles and settings".to_string(),
            "Use 'config init' to create default configuration".to_string(),
            "Use 'config set' to update specific values".to_string(),
            "Add --profile to work with named profiles".to_string(),
        ]);

        // Test command hints
        self.context_hints.insert("test".to_string(), vec![
            "Run conversion tests to verify functionality".to_string(),
            "Use --pattern to filter specific tests".to_string(),
            "Add --ci for continuous integration mode".to_string(),
            "Use --baseline to compare with previous results".to_string(),
        ]);

        // Benchmark command hints
        self.context_hints.insert("benchmark".to_string(), vec![
            "Measure conversion performance and throughput".to_string(),
            "Use --iterations to increase accuracy".to_string(),
            "Add --compare to show baseline comparison".to_string(),
            "Use --export to save results in various formats".to_string(),
        ]);
    }

    fn init_examples(&mut self) {
        // Convert examples
        self.examples.insert("convert".to_string(), vec![
            Example {
                command: "legacybridge convert document.doc --output-format md".to_string(),
                description: "Convert a DOC file to Markdown".to_string(),
                output_hint: Some("Creates document.md in the same directory".to_string()),
            },
            Example {
                command: "legacybridge convert *.rtf --output-format html --parallel".to_string(),
                description: "Convert all RTF files to HTML in parallel".to_string(),
                output_hint: Some("Processes files using all CPU cores".to_string()),
            },
            Example {
                command: "legacybridge convert report.wpd --output ./converted/report.md --preview".to_string(),
                description: "Preview conversion without creating files".to_string(),
                output_hint: Some("Shows what would be converted".to_string()),
            },
        ]);

        // Batch examples
        self.examples.insert("batch".to_string(), vec![
            Example {
                command: "legacybridge batch --input-dir ./docs --output-dir ./converted --recursive".to_string(),
                description: "Convert all files in a directory recursively".to_string(),
                output_hint: Some("Maintains directory structure in output".to_string()),
            },
            Example {
                command: "legacybridge batch -i ./legacy -o ./modern --pattern '*.doc,*.wpd' --parallel 4".to_string(),
                description: "Convert specific file types with 4 parallel workers".to_string(),
                output_hint: None,
            },
        ]);

        // Detect examples
        self.examples.insert("detect".to_string(), vec![
            Example {
                command: "legacybridge detect unknown_file --detailed".to_string(),
                description: "Detect file format with detailed analysis".to_string(),
                output_hint: Some("Shows confidence score and metadata".to_string()),
            },
            Example {
                command: "legacybridge detect *.* --output-format json > formats.json".to_string(),
                description: "Detect all file formats and export as JSON".to_string(),
                output_hint: None,
            },
        ]);

        // DLL examples
        self.examples.insert("dll".to_string(), vec![
            Example {
                command: "legacybridge dll build --arch both --generate-vb6".to_string(),
                description: "Build DLLs for both architectures with VB6 code".to_string(),
                output_hint: Some("Creates x86/x64 DLLs and VB6 module".to_string()),
            },
            Example {
                command: "legacybridge dll test --dll-path ./legacybridge.dll".to_string(),
                description: "Test DLL compatibility".to_string(),
                output_hint: None,
            },
        ]);

        // Serve examples
        self.examples.insert("serve".to_string(), vec![
            Example {
                command: "legacybridge serve --port 3000 --cors --auth".to_string(),
                description: "Start API server with CORS and authentication".to_string(),
                output_hint: Some("Server available at http://localhost:3000".to_string()),
            },
            Example {
                command: "legacybridge serve --workers 8 --websocket".to_string(),
                description: "Start server with 8 workers and WebSocket support".to_string(),
                output_hint: None,
            },
        ]);

        // Config examples
        self.examples.insert("config".to_string(), vec![
            Example {
                command: "legacybridge config init".to_string(),
                description: "Create default configuration file".to_string(),
                output_hint: Some("Creates legacybridge.toml".to_string()),
            },
            Example {
                command: "legacybridge config set default.output_format markdown".to_string(),
                description: "Set default output format".to_string(),
                output_hint: None,
            },
        ]);

        // Test examples
        self.examples.insert("test".to_string(), vec![
            Example {
                command: "legacybridge test --pattern 'rtf_*' --verbose".to_string(),
                description: "Run RTF-related tests with verbose output".to_string(),
                output_hint: None,
            },
            Example {
                command: "legacybridge test --ci --export junit.xml".to_string(),
                description: "Run tests in CI mode with JUnit output".to_string(),
                output_hint: None,
            },
        ]);

        // Benchmark examples
        self.examples.insert("benchmark".to_string(), vec![
            Example {
                command: "legacybridge benchmark conversion --iterations 100".to_string(),
                description: "Benchmark conversion speed with 100 iterations".to_string(),
                output_hint: Some("Shows throughput and latency statistics".to_string()),
            },
            Example {
                command: "legacybridge benchmark all --compare baseline.json".to_string(),
                description: "Run all benchmarks and compare with baseline".to_string(),
                output_hint: None,
            },
        ]);
    }

    pub fn show_command_help(&self, command: &str, writer: &mut dyn Write) -> std::io::Result<()> {
        // Show standard help
        let app = Cli::command();
        if let Some(subcmd) = app.find_subcommand(command) {
            writeln!(writer, "{}", subcmd.render_help())?;
        }

        // Add context hints
        if let Some(hints) = self.context_hints.get(command) {
            writeln!(writer, "\n{}", "💡 Tips:".bold().cyan())?;
            for hint in hints {
                writeln!(writer, "  • {}", hint.dimmed())?;
            }
        }

        // Add examples
        if let Some(examples) = self.examples.get(command) {
            writeln!(writer, "\n{}", "📚 Examples:".bold().green())?;
            for example in examples {
                writeln!(writer, "\n  {}", example.command.bold())?;
                writeln!(writer, "  {}", example.description.italic())?;
                if let Some(hint) = &example.output_hint {
                    writeln!(writer, "  → {}", hint.dimmed())?;
                }
            }
        }

        // Add related commands
        self.show_related_commands(command, writer)?;

        Ok(())
    }

    pub fn show_interactive_help(&self, writer: &mut dyn Write) -> std::io::Result<()> {
        writeln!(writer, "{}", "🚀 LegacyBridge CLI - Interactive Help".bold().blue())?;
        writeln!(writer, "{}", "=====================================".dimmed())?;
        
        writeln!(writer, "\n{}", "Common Tasks:".bold())?;
        
        writeln!(writer, "\n  {}", "📄 Convert a single file:".green())?;
        writeln!(writer, "     legacybridge convert file.doc --output-format md")?;
        
        writeln!(writer, "\n  {}", "📁 Convert a directory:".green())?;
        writeln!(writer, "     legacybridge batch -i ./docs -o ./converted --recursive")?;
        
        writeln!(writer, "\n  {}", "🔍 Detect file format:".green())?;
        writeln!(writer, "     legacybridge detect unknown_file --detailed")?;
        
        writeln!(writer, "\n  {}", "🌐 Start API server:".green())?;
        writeln!(writer, "     legacybridge serve --port 8080")?;
        
        writeln!(writer, "\n  {}", "🔧 Build DLL for VB6:".green())?;
        writeln!(writer, "     legacybridge dll build --arch x86 --generate-vb6")?;
        
        writeln!(writer, "\n{}", "Quick Tips:".bold())?;
        writeln!(writer, "  • Use {} to see what would happen without making changes", "--preview".yellow())?;
        writeln!(writer, "  • Add {} for faster processing of multiple files", "--parallel".yellow())?;
        writeln!(writer, "  • Use {} (-v, -vv, -vvv) for detailed output", "--verbose".yellow())?;
        writeln!(writer, "  • Export results with {} table|json|csv", "--output-format".yellow())?;
        
        writeln!(writer, "\n{}", "Getting Started:".bold())?;
        writeln!(writer, "  1. Run {} to see all commands", "legacybridge --help".cyan())?;
        writeln!(writer, "  2. Run {} for command details", "legacybridge <command> --help".cyan())?;
        writeln!(writer, "  3. Use {} completion for your shell", "legacybridge".cyan())?;
        
        Ok(())
    }

    fn show_related_commands(&self, command: &str, writer: &mut dyn Write) -> std::io::Result<()> {
        let related = match command {
            "convert" => vec!["batch", "detect", "validate"],
            "batch" => vec!["convert", "workflow", "report"],
            "detect" => vec!["validate", "convert"],
            "validate" => vec!["detect", "test"],
            "dll" => vec!["test", "benchmark"],
            "serve" => vec!["config", "test"],
            "config" => vec!["workflow", "serve"],
            "test" => vec!["benchmark", "validate"],
            "benchmark" => vec!["test", "report"],
            _ => vec![],
        };

        if !related.is_empty() {
            writeln!(writer, "\n{}", "🔗 Related commands:".bold().yellow())?;
            for cmd in related {
                writeln!(writer, "  • {}", cmd.cyan())?;
            }
        }

        Ok(())
    }

    pub fn suggest_command(&self, input: &str) -> Vec<String> {
        let commands = vec![
            "convert", "batch", "detect", "validate", "extract",
            "merge", "split", "compress", "serve", "dll",
            "config", "workflow", "search", "report", "test", "benchmark"
        ];

        let mut suggestions: Vec<(String, usize)> = commands
            .iter()
            .map(|&cmd| {
                let distance = levenshtein_distance(input, cmd);
                (cmd.to_string(), distance)
            })
            .filter(|(_, dist)| *dist <= 3)
            .collect();

        suggestions.sort_by_key(|(_, dist)| *dist);
        suggestions.into_iter().map(|(cmd, _)| cmd).take(3).collect()
    }
}

fn levenshtein_distance(s1: &str, s2: &str) -> usize {
    let len1 = s1.len();
    let len2 = s2.len();
    let mut matrix = vec![vec![0; len2 + 1]; len1 + 1];

    for i in 0..=len1 {
        matrix[i][0] = i;
    }
    for j in 0..=len2 {
        matrix[0][j] = j;
    }

    for i in 1..=len1 {
        for j in 1..=len2 {
            let cost = if s1.chars().nth(i - 1) == s2.chars().nth(j - 1) { 0 } else { 1 };
            matrix[i][j] = std::cmp::min(
                std::cmp::min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1),
                matrix[i - 1][j - 1] + cost,
            );
        }
    }

    matrix[len1][len2]
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_help_system_creation() {
        let help = HelpSystem::new();
        assert!(!help.context_hints.is_empty());
        assert!(!help.examples.is_empty());
    }

    #[test]
    fn test_command_suggestions() {
        let help = HelpSystem::new();
        
        let suggestions = help.suggest_command("convetr");
        assert!(suggestions.contains(&"convert".to_string()));
        
        let suggestions = help.suggest_command("btach");
        assert!(suggestions.contains(&"batch".to_string()));
    }

    #[test]
    fn test_levenshtein_distance() {
        assert_eq!(levenshtein_distance("convert", "convert"), 0);
        assert_eq!(levenshtein_distance("convert", "convetr"), 1);
        assert_eq!(levenshtein_distance("batch", "btach"), 2);
    }
}