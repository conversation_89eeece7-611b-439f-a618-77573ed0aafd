//! WebSocket handler for real-time updates
//!
//! This module integrates the WebSocket functionality with the API handlers.

use axum::{
    extract::{ws::WebSocketUpgrade, State},
    response::IntoResponse,
};
use std::sync::Arc;

use crate::api::websocket::{websocket_handler as ws_handler, WsState};

/// WebSocket endpoint handler that upgrades HTTP connections to WebSocket
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(ws_state): State<Arc<WsState>>,
) -> impl IntoResponse {
    ws_handler(ws, State(ws_state)).await
}