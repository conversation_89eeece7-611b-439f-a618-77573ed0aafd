use clap::{Command, CommandFactory};
use clap_complete::{generate, Generator, Shell};
use std::io::Write;
use crate::cli::app::Cli;

pub struct CompletionGenerator;

impl CompletionGenerator {
    pub fn generate_completion<W: Write>(shell: Shell, writer: &mut W) -> std::io::Result<()> {
        let mut cmd = Cli::command();
        generate(shell, &mut cmd, "legacybridge", writer);
        Ok(())
    }

    pub fn generate_all_completions(output_dir: &str) -> std::io::Result<()> {
        let shells = vec![
            (Shell::Bash, "legacybridge.bash"),
            (Shell::Zsh, "_legacybridge"),
            (Shell::Fish, "legacybridge.fish"),
            (Shell::PowerShell, "_legacybridge.ps1"),
            (Shell::Elvish, "legacybridge.elv"),
        ];

        for (shell, filename) in shells {
            let path = std::path::Path::new(output_dir).join(filename);
            let mut file = std::fs::File::create(&path)?;
            Self::generate_completion(shell, &mut file)?;
            println!("Generated {} completion: {}", shell, path.display());
        }

        Ok(())
    }

    pub fn print_install_instructions(shell: Shell) {
        match shell {
            Shell::Bash => {
                println!("# Bash completion installation:");
                println!("# Add to ~/.bashrc or ~/.bash_profile:");
                println!("source <(legacybridge completion bash)");
                println!();
                println!("# Or save to file:");
                println!("legacybridge completion bash > /etc/bash_completion.d/legacybridge");
            }
            Shell::Zsh => {
                println!("# Zsh completion installation:");
                println!("# Add to ~/.zshrc:");
                println!("autoload -U compinit && compinit");
                println!("source <(legacybridge completion zsh)");
                println!();
                println!("# Or save to file:");
                println!("legacybridge completion zsh > ~/.zsh/completions/_legacybridge");
            }
            Shell::Fish => {
                println!("# Fish completion installation:");
                println!("legacybridge completion fish > ~/.config/fish/completions/legacybridge.fish");
            }
            Shell::PowerShell => {
                println!("# PowerShell completion installation:");
                println!("# Add to your PowerShell profile:");
                println!("legacybridge completion powershell | Out-String | Invoke-Expression");
                println!();
                println!("# To find your profile location:");
                println!("echo $PROFILE");
            }
            Shell::Elvish => {
                println!("# Elvish completion installation:");
                println!("# Add to ~/.elvish/rc.elv:");
                println!("eval (legacybridge completion elvish | slurp)");
            }
        }
    }

    pub fn get_dynamic_completions(context: &str) -> Vec<String> {
        match context {
            "output-format" => vec![
                "markdown".to_string(),
                "md".to_string(),
                "rtf".to_string(),
                "html".to_string(),
                "txt".to_string(),
                "text".to_string(),
                "json".to_string(),
                "xml".to_string(),
            ],
            "input-format" => vec![
                "rtf".to_string(),
                "doc".to_string(),
                "docx".to_string(),
                "wpd".to_string(),
                "wps".to_string(),
                "odt".to_string(),
                "markdown".to_string(),
                "md".to_string(),
            ],
            "profile" => vec![
                "default".to_string(),
                "fast".to_string(),
                "accurate".to_string(),
                "minimal".to_string(),
                "comprehensive".to_string(),
            ],
            "test-type" => vec![
                "unit".to_string(),
                "integration".to_string(),
                "format".to_string(),
                "performance".to_string(),
                "memory".to_string(),
                "accuracy".to_string(),
            ],
            "benchmark-type" => vec![
                "conversion".to_string(),
                "throughput".to_string(),
                "latency".to_string(),
                "memory".to_string(),
                "concurrency".to_string(),
            ],
            _ => vec![],
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Cursor;

    #[test]
    fn test_bash_completion_generation() {
        let mut buffer = Cursor::new(Vec::new());
        let result = CompletionGenerator::generate_completion(Shell::Bash, &mut buffer);
        assert!(result.is_ok());
        
        let output = String::from_utf8(buffer.into_inner()).unwrap();
        assert!(output.contains("legacybridge"));
        assert!(output.contains("complete"));
    }

    #[test]
    fn test_dynamic_completions() {
        let formats = CompletionGenerator::get_dynamic_completions("output-format");
        assert!(formats.contains(&"markdown".to_string()));
        assert!(formats.contains(&"rtf".to_string()));
        
        let test_types = CompletionGenerator::get_dynamic_completions("test-type");
        assert!(test_types.contains(&"unit".to_string()));
        assert!(test_types.contains(&"integration".to_string()));
    }

    #[test]
    fn test_all_shells() {
        let shells = vec![Shell::Bash, Shell::Zsh, Shell::Fish, Shell::PowerShell, Shell::Elvish];
        
        for shell in shells {
            let mut buffer = Cursor::new(Vec::new());
            let result = CompletionGenerator::generate_completion(shell, &mut buffer);
            assert!(result.is_ok(), "Failed to generate completion for {:?}", shell);
        }
    }
}