// Enhanced Format Analysis Module
// Provides comprehensive file format detection and analysis capabilities

use std::collections::HashMap;
use std::fs;
use std::io::Read;
use std::path::Path;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use crate::conversion::error::ConversionError;
use crate::formats::{FormatManager, FormatType, FormatDetection};

/// Extended format analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatAnalysis {
    pub format_type: String,
    pub confidence: f32,
    pub version: Option<String>,
    pub file_size: u64,
    pub file_path: String,
    pub metadata: HashMap<String, String>,
    pub magic_bytes: Vec<u8>,
    pub header_hex: String,
    pub suggested_formats: Vec<SuggestedFormat>,
    pub integrity_status: IntegrityStatus,
    pub analysis_timestamp: DateTime<Utc>,
}

/// Suggested alternative format
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SuggestedFormat {
    pub format: String,
    pub confidence: f32,
    pub reason: String,
}

/// File integrity status
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct IntegrityStatus {
    pub is_valid: bool,
    pub issues: Vec<String>,
    pub readable_percentage: f32,
}

/// Format analyzer with enhanced capabilities
pub struct FormatAnalyzer {
    format_manager: FormatManager,
    analyze_depth: usize,
}

impl FormatAnalyzer {
    /// Create new format analyzer
    pub fn new() -> Self {
        Self {
            format_manager: FormatManager::new(),
            analyze_depth: 512, // Default header analysis depth
        }
    }

    /// Analyze a single file
    pub fn analyze_file(&self, path: &Path) -> Result<FormatAnalysis, ConversionError> {
        // Read file metadata
        let metadata = fs::metadata(path)
            .map_err(|e| ConversionError::IoError(e.to_string()))?;
        
        let file_size = metadata.len();
        
        // Read file content (limited for analysis)
        let mut file = fs::File::open(path)
            .map_err(|e| ConversionError::IoError(e.to_string()))?;
        
        let mut content = Vec::new();
        let read_size = std::cmp::min(file_size as usize, 1024 * 1024); // Max 1MB for analysis
        file.by_ref().take(read_size as u64).read_to_end(&mut content)
            .map_err(|e| ConversionError::IoError(e.to_string()))?;
        
        // Detect format
        let detection = self.detect_with_fallback(&content, path)?;
        
        // Extract magic bytes
        let magic_bytes = self.extract_magic_bytes(&content);
        
        // Generate hex dump of header
        let header_hex = self.generate_hex_dump(&content, self.analyze_depth);
        
        // Extract metadata
        let mut metadata = detection.metadata.clone();
        self.extract_additional_metadata(&mut metadata, &content, &detection.format_type);
        
        // Suggest alternative formats
        let suggested_formats = self.suggest_alternatives(&content, &detection);
        
        // Check file integrity
        let integrity_status = self.check_integrity(&content, &detection.format_type);
        
        Ok(FormatAnalysis {
            format_type: format!("{:?}", detection.format_type),
            confidence: detection.confidence,
            version: detection.version,
            file_size,
            file_path: path.to_string_lossy().to_string(),
            metadata,
            magic_bytes,
            header_hex,
            suggested_formats,
            integrity_status,
            analysis_timestamp: Utc::now(),
        })
    }

    /// Detect format with fallback strategies
    fn detect_with_fallback(&self, content: &[u8], path: &Path) -> Result<FormatDetection, ConversionError> {
        // Try content-based detection first
        let content_detection = self.format_manager.detect_format(content)?;
        
        // If content detection has high confidence, use it
        if content_detection.confidence > 0.7 {
            return Ok(content_detection);
        }
        
        // Try path-based detection
        let path_detection = self.format_manager.detect_format_from_path(path)?;
        
        // Choose the one with higher confidence
        if path_detection.confidence > content_detection.confidence {
            Ok(path_detection)
        } else {
            Ok(content_detection)
        }
    }

    /// Extract magic bytes from file content
    fn extract_magic_bytes(&self, content: &[u8]) -> Vec<u8> {
        let magic_size = std::cmp::min(32, content.len());
        content[..magic_size].to_vec()
    }

    /// Generate hex dump of file header
    fn generate_hex_dump(&self, content: &[u8], max_bytes: usize) -> String {
        let dump_size = std::cmp::min(max_bytes, content.len());
        let mut hex_dump = String::new();
        
        for (offset, chunk) in content[..dump_size].chunks(16).enumerate() {
            // Offset
            hex_dump.push_str(&format!("{:08X}  ", offset * 16));
            
            // Hex bytes
            for (i, byte) in chunk.iter().enumerate() {
                if i == 8 {
                    hex_dump.push(' ');
                }
                hex_dump.push_str(&format!("{:02X} ", byte));
            }
            
            // Padding
            if chunk.len() < 16 {
                for i in chunk.len()..16 {
                    if i == 8 {
                        hex_dump.push(' ');
                    }
                    hex_dump.push_str("   ");
                }
            }
            
            hex_dump.push_str(" |");
            
            // ASCII representation
            for byte in chunk {
                if byte.is_ascii_graphic() || *byte == b' ' {
                    hex_dump.push(*byte as char);
                } else {
                    hex_dump.push('.');
                }
            }
            
            hex_dump.push_str("|\n");
        }
        
        hex_dump
    }

    /// Extract additional metadata based on format type
    fn extract_additional_metadata(&self, metadata: &mut HashMap<String, String>, content: &[u8], format_type: &FormatType) {
        // Add common metadata
        metadata.insert("analyzed_bytes".to_string(), content.len().to_string());
        
        // Format-specific metadata extraction
        match format_type {
            FormatType::Doc => self.extract_doc_metadata(metadata, content),
            FormatType::WordPerfect => self.extract_wpd_metadata(metadata, content),
            FormatType::DBase => self.extract_dbase_metadata(metadata, content),
            FormatType::WordStar => self.extract_wordstar_metadata(metadata, content),
            FormatType::Lotus123 => self.extract_lotus_metadata(metadata, content),
            FormatType::Unknown => {
                metadata.insert("format_status".to_string(), "unrecognized".to_string());
            }
        }
    }

    /// Extract DOC-specific metadata
    fn extract_doc_metadata(&self, metadata: &mut HashMap<String, String>, content: &[u8]) {
        if content.len() >= 8 {
            let signature = &content[0..8];
            metadata.insert("ole2_signature".to_string(), 
                signature.iter().map(|b| format!("{:02X}", b)).collect::<Vec<_>>().join(" "));
        }
        
        // Check for compound document structure
        if content.len() >= 76 {
            metadata.insert("doc_type".to_string(), "compound_document".to_string());
        }
    }

    /// Extract WordPerfect metadata
    fn extract_wpd_metadata(&self, metadata: &mut HashMap<String, String>, content: &[u8]) {
        if content.len() >= 4 {
            let signature = &content[0..4];
            metadata.insert("wpd_signature".to_string(), 
                signature.iter().map(|b| format!("{:02X}", b)).collect::<Vec<_>>().join(" "));
        }
    }

    /// Extract dBase metadata
    fn extract_dbase_metadata(&self, metadata: &mut HashMap<String, String>, content: &[u8]) {
        if content.len() >= 32 {
            // dBase header analysis
            let version = content[0];
            metadata.insert("dbase_version".to_string(), format!("{:02X}", version));
            
            if content.len() >= 4 {
                let record_count = u32::from_le_bytes([content[4], content[5], content[6], content[7]]);
                metadata.insert("record_count".to_string(), record_count.to_string());
            }
        }
    }

    /// Extract WordStar metadata
    fn extract_wordstar_metadata(&self, metadata: &mut HashMap<String, String>, content: &[u8]) {
        // WordStar uses high-bit formatting
        let mut high_bit_count = 0;
        for byte in content.iter().take(1024) {
            if byte & 0x80 != 0 {
                high_bit_count += 1;
            }
        }
        metadata.insert("high_bit_percentage".to_string(), 
            format!("{:.1}%", (high_bit_count as f32 / content.len().min(1024) as f32) * 100.0));
    }

    /// Extract Lotus 1-2-3 metadata
    fn extract_lotus_metadata(&self, metadata: &mut HashMap<String, String>, content: &[u8]) {
        if content.len() >= 6 {
            let version_bytes = &content[0..2];
            metadata.insert("lotus_version".to_string(), 
                format!("{:02X}{:02X}", version_bytes[0], version_bytes[1]));
        }
    }

    /// Suggest alternative formats based on content analysis
    fn suggest_alternatives(&self, content: &[u8], detection: &FormatDetection) -> Vec<SuggestedFormat> {
        let mut suggestions = Vec::new();
        
        // If confidence is low, suggest other formats
        if detection.confidence < 0.8 {
            // Try all formats and collect those with reasonable confidence
            let formats = vec![
                FormatType::Doc,
                FormatType::WordPerfect,
                FormatType::DBase,
                FormatType::WordStar,
                FormatType::Lotus123,
            ];
            
            for format in formats {
                if format != detection.format_type {
                    // Simple heuristic check for each format
                    let confidence = self.calculate_format_confidence(content, &format);
                    if confidence > 0.3 {
                        suggestions.push(SuggestedFormat {
                            format: format!("{:?}", format),
                            confidence,
                            reason: self.get_suggestion_reason(&format, confidence),
                        });
                    }
                }
            }
            
            // Sort by confidence
            suggestions.sort_by(|a, b| b.confidence.partial_cmp(&a.confidence).unwrap());
        }
        
        suggestions
    }

    /// Calculate confidence for a specific format
    fn calculate_format_confidence(&self, content: &[u8], format: &FormatType) -> f32 {
        match format {
            FormatType::Doc => {
                // Check for OLE2 signature
                if content.len() >= 8 && content[0..8] == [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1] {
                    0.9
                } else {
                    0.1
                }
            }
            FormatType::WordPerfect => {
                // Check for WPD signatures
                if content.len() >= 4 {
                    match &content[0..4] {
                        [0xFF, 0x57, 0x50, 0x43] => 0.9, // WPC
                        [0xFF, 0x57, 0x50, 0x44] => 0.9, // WPD
                        _ => 0.1
                    }
                } else {
                    0.1
                }
            }
            FormatType::DBase => {
                // Check for dBase version markers
                if content.len() > 0 {
                    match content[0] {
                        0x03 | 0x83 | 0x8B | 0xF5 => 0.8,
                        _ => 0.1
                    }
                } else {
                    0.1
                }
            }
            FormatType::WordStar => {
                // Check for high-bit patterns typical of WordStar
                let high_bit_ratio = content.iter().take(256)
                    .filter(|&&b| b & 0x80 != 0)
                    .count() as f32 / content.len().min(256) as f32;
                
                if high_bit_ratio > 0.1 && high_bit_ratio < 0.5 {
                    0.6
                } else {
                    0.1
                }
            }
            FormatType::Lotus123 => {
                // Check for Lotus signatures
                if content.len() >= 4 {
                    match &content[0..4] {
                        [0x00, 0x00, 0x02, 0x00] => 0.8, // WKS
                        [0x00, 0x00, 0x1A, 0x00] => 0.8, // WK1
                        _ => 0.1
                    }
                } else {
                    0.1
                }
            }
            FormatType::Unknown => 0.0,
        }
    }

    /// Get suggestion reason based on format and confidence
    fn get_suggestion_reason(&self, format: &FormatType, confidence: f32) -> String {
        match format {
            FormatType::Doc => {
                if confidence > 0.7 {
                    "Contains OLE2 compound document structure".to_string()
                } else {
                    "Possible Microsoft Word document based on structure".to_string()
                }
            }
            FormatType::WordPerfect => "Contains WordPerfect signature markers".to_string(),
            FormatType::DBase => "Header matches dBase file structure".to_string(),
            FormatType::WordStar => "Contains high-bit formatting typical of WordStar".to_string(),
            FormatType::Lotus123 => "Header matches Lotus 1-2-3 worksheet format".to_string(),
            FormatType::Unknown => "Unable to determine format".to_string(),
        }
    }

    /// Check file integrity
    fn check_integrity(&self, content: &[u8], format_type: &FormatType) -> IntegrityStatus {
        let mut issues = Vec::new();
        let mut is_valid = true;
        
        // Basic checks
        if content.is_empty() {
            issues.push("File is empty".to_string());
            is_valid = false;
        }
        
        // Format-specific integrity checks
        match format_type {
            FormatType::Doc => {
                if content.len() >= 8 {
                    let signature = &content[0..8];
                    if signature != [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1] {
                        issues.push("Invalid OLE2 signature for DOC file".to_string());
                        is_valid = false;
                    }
                } else {
                    issues.push("File too small for valid DOC format".to_string());
                    is_valid = false;
                }
            }
            FormatType::DBase => {
                if content.len() < 32 {
                    issues.push("File too small for valid dBase header".to_string());
                    is_valid = false;
                }
            }
            _ => {
                // Generic validation for other formats
                if content.len() < 4 {
                    issues.push("File too small for format detection".to_string());
                }
            }
        }
        
        // Calculate readable percentage (simplified)
        let readable_bytes = content.iter()
            .filter(|&&b| b.is_ascii() || b >= 128)
            .count();
        let readable_percentage = (readable_bytes as f32 / content.len() as f32) * 100.0;
        
        IntegrityStatus {
            is_valid,
            issues,
            readable_percentage,
        }
    }

    /// Batch analyze multiple files
    pub fn analyze_files(&self, paths: &[&Path]) -> Vec<Result<FormatAnalysis, ConversionError>> {
        paths.iter()
            .map(|path| self.analyze_file(path))
            .collect()
    }
}

impl Default for FormatAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}