use crate::cli::app::WorkflowCommands;
use crate::cli::commands::workflow::{
    execute_workflow_command, Workflow, WorkflowStep, StepType, 
    ErrorHandler, WorkflowParameter, RetryConfig
};
use crate::cli::output::OutputFormat;
use std::collections::HashMap;
use std::path::PathBuf;
use tempfile::TempDir;
use tokio::fs;

#[tokio::test]
async fn test_workflow_create_command() {
    let temp_dir = TempDir::new().unwrap();
    std::env::set_var("HOME", temp_dir.path());
    
    let result = execute_workflow_command(
        WorkflowCommands::Create {
            name: "test_workflow".to_string(),
            file: None,
            template: None,
            description: Some("Test workflow".to_string()),
        },
        OutputFormat::Plain,
    ).await;
    
    assert!(result.is_ok());
    
    // Verify workflow file was created
    let workflow_dir = temp_dir.path().join(".config/legacybridge/workflows");
    let workflow_file = workflow_dir.join("test_workflow.yaml");
    assert!(workflow_file.exists());
}

#[tokio::test]
async fn test_workflow_create_from_template() {
    let temp_dir = TempDir::new().unwrap();
    std::env::set_var("HOME", temp_dir.path());
    
    let result = execute_workflow_command(
        WorkflowCommands::Create {
            name: "conversion_workflow".to_string(),
            file: None,
            template: Some("conversion".to_string()),
            description: None,
        },
        OutputFormat::Plain,
    ).await;
    
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_workflow_list_command() {
    let temp_dir = TempDir::new().unwrap();
    std::env::set_var("HOME", temp_dir.path());
    
    // Create a workflow first
    let _ = execute_workflow_command(
        WorkflowCommands::Create {
            name: "list_test".to_string(),
            file: None,
            template: None,
            description: None,
        },
        OutputFormat::Plain,
    ).await;
    
    // List workflows
    let result = execute_workflow_command(
        WorkflowCommands::List,
        OutputFormat::Json,
    ).await;
    
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_workflow_validate_command() {
    let temp_dir = TempDir::new().unwrap();
    std::env::set_var("HOME", temp_dir.path());
    
    // Create a workflow
    let _ = execute_workflow_command(
        WorkflowCommands::Create {
            name: "validate_test".to_string(),
            file: None,
            template: None,
            description: None,
        },
        OutputFormat::Plain,
    ).await;
    
    // Validate it
    let result = execute_workflow_command(
        WorkflowCommands::Validate {
            workflow: "validate_test".to_string(),
        },
        OutputFormat::Plain,
    ).await;
    
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_workflow_dry_run() {
    let temp_dir = TempDir::new().unwrap();
    std::env::set_var("HOME", temp_dir.path());
    
    // Create a simple workflow
    let workflow = Workflow {
        name: "dry_run_test".to_string(),
        description: Some("Test workflow for dry run".to_string()),
        version: "1.0.0".to_string(),
        author: None,
        parameters: vec![],
        environment: HashMap::new(),
        steps: vec![
            WorkflowStep {
                name: "echo_step".to_string(),
                description: Some("Echo test".to_string()),
                step_type: StepType::Command {
                    command: "echo".to_string(),
                    args: vec!["Hello, World!".to_string()],
                    working_dir: None,
                },
                condition: None,
                retry: None,
                on_error: None,
            }
        ],
        on_error: Some(ErrorHandler::Stop),
    };
    
    // Save workflow
    let workflow_dir = temp_dir.path().join(".config/legacybridge/workflows");
    fs::create_dir_all(&workflow_dir).await.unwrap();
    let workflow_path = workflow_dir.join("dry_run_test.yaml");
    let yaml = serde_yaml::to_string(&workflow).unwrap();
    fs::write(&workflow_path, yaml).await.unwrap();
    
    // Run with dry_run
    let result = execute_workflow_command(
        WorkflowCommands::Run {
            name: "dry_run_test".to_string(),
            params: vec![],
            dry_run: true,
            continue_on_error: false,
            env: vec![],
        },
        OutputFormat::Plain,
    ).await;
    
    assert!(result.is_ok());
}

#[test]
fn test_workflow_serialization() {
    let workflow = Workflow {
        name: "test".to_string(),
        description: Some("Test workflow".to_string()),
        version: "1.0.0".to_string(),
        author: Some("Test Author".to_string()),
        parameters: vec![
            WorkflowParameter {
                name: "input".to_string(),
                description: Some("Input parameter".to_string()),
                param_type: "string".to_string(),
                default: Some("default_value".to_string()),
                required: false,
            }
        ],
        environment: HashMap::new(),
        steps: vec![
            WorkflowStep {
                name: "test_step".to_string(),
                description: Some("Test step".to_string()),
                step_type: StepType::Command {
                    command: "echo".to_string(),
                    args: vec!["test".to_string()],
                    working_dir: None,
                },
                condition: None,
                retry: Some(RetryConfig {
                    max_attempts: 3,
                    delay_seconds: 5,
                    backoff_multiplier: Some(2.0),
                }),
                on_error: Some(ErrorHandler::Retry),
            }
        ],
        on_error: Some(ErrorHandler::Stop),
    };
    
    // To YAML
    let yaml = serde_yaml::to_string(&workflow).unwrap();
    let from_yaml: Workflow = serde_yaml::from_str(&yaml).unwrap();
    assert_eq!(from_yaml.name, workflow.name);
    
    // To TOML
    let toml = toml::to_string(&workflow).unwrap();
    let from_toml: Workflow = toml::from_str(&toml).unwrap();
    assert_eq!(from_toml.name, workflow.name);
}

#[test]
fn test_variable_expansion() {
    use crate::cli::commands::workflow::expand_variables;
    
    let mut params = HashMap::new();
    params.insert("name".to_string(), "test_name".to_string());
    params.insert("version".to_string(), "1.0.0".to_string());
    
    let mut env = HashMap::new();
    env.insert("HOME".to_string(), "/home/<USER>".to_string());
    env.insert("USER".to_string(), "testuser".to_string());
    
    // Test parameter expansion
    assert_eq!(
        expand_variables("Hello ${name}!", &params, &env),
        "Hello test_name!"
    );
    
    // Test environment variable expansion
    assert_eq!(
        expand_variables("Home: ${HOME}", &params, &env),
        "Home: /home/<USER>"
    );
    
    // Test mixed expansion
    assert_eq!(
        expand_variables("${USER} - ${name} v${version}", &params, &env),
        "testuser - test_name v1.0.0"
    );
    
    // Test no expansion needed
    assert_eq!(
        expand_variables("No variables here", &params, &env),
        "No variables here"
    );
}

#[test]
fn test_step_type_variants() {
    // Test Command step
    let command_step = StepType::Command {
        command: "ls".to_string(),
        args: vec!["-la".to_string()],
        working_dir: Some("/tmp".to_string()),
    };
    assert!(matches!(command_step, StepType::Command { .. }));
    
    // Test Convert step
    let convert_step = StepType::Convert {
        input: "input.doc".to_string(),
        output: "output.md".to_string(),
        format: "markdown".to_string(),
        options: HashMap::new(),
    };
    assert!(matches!(convert_step, StepType::Convert { .. }));
    
    // Test Batch step
    let batch_step = StepType::Batch {
        input_dir: "./input".to_string(),
        output_dir: "./output".to_string(),
        pattern: "*.doc".to_string(),
        format: "md".to_string(),
    };
    assert!(matches!(batch_step, StepType::Batch { .. }));
    
    // Test Script step
    let script_step = StepType::Script {
        language: "bash".to_string(),
        code: "echo 'Hello'".to_string(),
    };
    assert!(matches!(script_step, StepType::Script { .. }));
    
    // Test Parallel step
    let parallel_step = StepType::Parallel {
        steps: vec![],
        max_parallel: Some(4),
    };
    assert!(matches!(parallel_step, StepType::Parallel { .. }));
    
    // Test ForEach step
    let foreach_step = StepType::ForEach {
        items: "file1,file2,file3".to_string(),
        variable: "file".to_string(),
        steps: vec![],
    };
    assert!(matches!(foreach_step, StepType::ForEach { .. }));
}