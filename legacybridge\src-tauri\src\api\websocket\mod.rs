//! WebSocket support for real-time conversion updates
//!
//! This module provides WebSocket endpoints and handlers for clients to receive
//! real-time updates about conversion progress, job status, and system events.

use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        State,
    },
    response::Response,
};
use futures::{sink::SinkExt, stream::StreamExt};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::Arc,
};
use tokio::sync::{broadcast, RwLock};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// WebSocket connection state
pub struct WsState {
    /// Active WebSocket connections
    pub connections: Arc<RwLock<HashMap<String, ConnectionInfo>>>,
    /// Broadcast channel for system-wide events
    pub broadcast_tx: broadcast::Sender<WsMessage>,
    /// Job-specific channels for targeted updates
    pub job_channels: Arc<RwLock<HashMap<String, broadcast::Sender<WsMessage>>>>,
}

impl WsState {
    /// Create a new WebSocket state
    pub fn new() -> Self {
        let (broadcast_tx, _) = broadcast::channel(1024);
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            broadcast_tx,
            job_channels: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Register a new job channel
    pub async fn create_job_channel(&self, job_id: String) -> broadcast::Sender<WsMessage> {
        let (tx, _) = broadcast::channel(256);
        self.job_channels.write().await.insert(job_id.clone(), tx.clone());
        tx
    }

    /// Get a job channel
    pub async fn get_job_channel(&self, job_id: &str) -> Option<broadcast::Sender<WsMessage>> {
        self.job_channels.read().await.get(job_id).cloned()
    }

    /// Remove a job channel
    pub async fn remove_job_channel(&self, job_id: &str) {
        self.job_channels.write().await.remove(job_id);
    }
}

impl Default for WsState {
    fn default() -> Self {
        Self::new()
    }
}

/// Information about a WebSocket connection
#[derive(Debug, Clone)]
pub struct ConnectionInfo {
    pub id: String,
    pub connected_at: chrono::DateTime<chrono::Utc>,
    pub subscriptions: Vec<String>,
}

/// WebSocket message types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "snake_case")]
pub enum WsMessage {
    /// Connection established
    Connected {
        connection_id: String,
        server_time: chrono::DateTime<chrono::Utc>,
    },
    /// Ping/pong for connection health
    Ping,
    Pong,
    /// Subscribe to job updates
    Subscribe { job_id: String },
    /// Unsubscribe from job updates
    Unsubscribe { job_id: String },
    /// Subscription confirmation
    Subscribed { job_id: String },
    /// Unsubscription confirmation
    Unsubscribed { job_id: String },
    /// Conversion job started
    JobStarted {
        job_id: String,
        total_files: usize,
        started_at: chrono::DateTime<chrono::Utc>,
    },
    /// Conversion progress update
    Progress {
        job_id: String,
        file_name: String,
        current_file: usize,
        total_files: usize,
        progress_percent: f32,
        status: String,
    },
    /// File conversion completed
    FileCompleted {
        job_id: String,
        file_name: String,
        success: bool,
        error: Option<String>,
        duration_ms: u64,
    },
    /// Job completed
    JobCompleted {
        job_id: String,
        total_files: usize,
        successful_files: usize,
        failed_files: usize,
        duration_ms: u64,
        completed_at: chrono::DateTime<chrono::Utc>,
    },
    /// Error message
    Error { message: String },
    /// System announcement
    Announcement { message: String },
}

/// WebSocket upgrade handler
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(state): State<Arc<WsState>>,
) -> Response {
    ws.on_upgrade(|socket| handle_socket(socket, state))
}

/// Handle a WebSocket connection
async fn handle_socket(socket: WebSocket, state: Arc<WsState>) {
    let connection_id = Uuid::new_v4().to_string();
    let connection_info = ConnectionInfo {
        id: connection_id.clone(),
        connected_at: chrono::Utc::now(),
        subscriptions: Vec::new(),
    };

    // Register connection
    state.connections.write().await.insert(connection_id.clone(), connection_info);
    info!("WebSocket client connected: {}", connection_id);

    let (mut sender, mut receiver) = socket.split();

    // Send connection confirmation
    let connect_msg = WsMessage::Connected {
        connection_id: connection_id.clone(),
        server_time: chrono::Utc::now(),
    };
    if let Ok(msg) = serde_json::to_string(&connect_msg) {
        let _ = sender.send(Message::Text(msg)).await;
    }

    // Subscribe to broadcast channel
    let mut broadcast_rx = state.broadcast_tx.subscribe();
    let state_clone = state.clone();
    let connection_id_clone = connection_id.clone();

    // Spawn task to handle outgoing messages
    let mut send_task = tokio::spawn(async move {
        loop {
            tokio::select! {
                // Broadcast messages
                Ok(msg) = broadcast_rx.recv() => {
                    if let Ok(text) = serde_json::to_string(&msg) {
                        if sender.send(Message::Text(text)).await.is_err() {
                            break;
                        }
                    }
                }
                // Job-specific messages
                _ = async {
                    // Check subscriptions and forward messages
                    let connections = state_clone.connections.read().await;
                    if let Some(conn_info) = connections.get(&connection_id_clone) {
                        for job_id in &conn_info.subscriptions {
                            if let Some(job_tx) = state_clone.get_job_channel(job_id).await {
                                let mut job_rx = job_tx.subscribe();
                                if let Ok(msg) = job_rx.recv().await {
                                    if let Ok(text) = serde_json::to_string(&msg) {
                                        if sender.send(Message::Text(text)).await.is_err() {
                                            return;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
                } => {}
            }
        }
    });

    // Handle incoming messages
    let mut recv_task = tokio::spawn(async move {
        while let Some(Ok(msg)) = receiver.next().await {
            match msg {
                Message::Text(text) => {
                    if let Ok(ws_msg) = serde_json::from_str::<WsMessage>(&text) {
                        handle_client_message(ws_msg, &connection_id, &state).await;
                    }
                }
                Message::Binary(_) => {
                    warn!("Binary messages not supported");
                }
                Message::Ping(data) => {
                    debug!("Received ping from {}", connection_id);
                    // Axum handles pong automatically
                }
                Message::Pong(_) => {
                    debug!("Received pong from {}", connection_id);
                }
                Message::Close(_) => {
                    info!("Client {} closing connection", connection_id);
                    break;
                }
            }
        }
    });

    // Wait for either task to complete
    tokio::select! {
        _ = send_task => {
            recv_task.abort();
        }
        _ = recv_task => {
            send_task.abort();
        }
    }

    // Clean up connection
    state.connections.write().await.remove(&connection_id);
    info!("WebSocket client disconnected: {}", connection_id);
}

/// Handle messages from WebSocket clients
async fn handle_client_message(msg: WsMessage, connection_id: &str, state: &Arc<WsState>) {
    match msg {
        WsMessage::Ping => {
            // Send pong response directly to this connection
            // This would need access to the sender, simplified here
            debug!("Received ping from {}", connection_id);
        }
        WsMessage::Subscribe { job_id } => {
            let mut connections = state.connections.write().await;
            if let Some(conn_info) = connections.get_mut(connection_id) {
                if !conn_info.subscriptions.contains(&job_id) {
                    conn_info.subscriptions.push(job_id.clone());
                    info!("Client {} subscribed to job {}", connection_id, job_id);
                }
            }
        }
        WsMessage::Unsubscribe { job_id } => {
            let mut connections = state.connections.write().await;
            if let Some(conn_info) = connections.get_mut(connection_id) {
                conn_info.subscriptions.retain(|id| id != &job_id);
                info!("Client {} unsubscribed from job {}", connection_id, job_id);
            }
        }
        _ => {
            warn!("Unexpected message type from client {}", connection_id);
        }
    }
}

/// Send a message to all clients subscribed to a job
pub async fn send_job_update(state: &Arc<WsState>, job_id: &str, message: WsMessage) {
    if let Some(tx) = state.get_job_channel(job_id).await {
        if let Err(e) = tx.send(message) {
            error!("Failed to send job update for {}: {}", job_id, e);
        }
    }
}

/// Send a broadcast message to all connected clients
pub async fn send_broadcast(state: &Arc<WsState>, message: WsMessage) {
    if let Err(e) = state.broadcast_tx.send(message) {
        error!("Failed to send broadcast message: {}", e);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_ws_state_creation() {
        let state = WsState::new();
        assert_eq!(state.connections.read().await.len(), 0);
        assert_eq!(state.job_channels.read().await.len(), 0);
    }

    #[tokio::test]
    async fn test_job_channel_lifecycle() {
        let state = WsState::new();
        let job_id = "test-job".to_string();
        
        // Create channel
        let tx = state.create_job_channel(job_id.clone()).await;
        assert!(state.get_job_channel(&job_id).await.is_some());
        
        // Remove channel
        state.remove_job_channel(&job_id).await;
        assert!(state.get_job_channel(&job_id).await.is_none());
    }

    #[test]
    fn test_message_serialization() {
        let msg = WsMessage::Progress {
            job_id: "test".to_string(),
            file_name: "test.doc".to_string(),
            current_file: 1,
            total_files: 10,
            progress_percent: 10.0,
            status: "Converting".to_string(),
        };
        
        let json = serde_json::to_string(&msg).unwrap();
        let deserialized: WsMessage = serde_json::from_str(&json).unwrap();
        
        match deserialized {
            WsMessage::Progress { job_id, .. } => assert_eq!(job_id, "test"),
            _ => panic!("Wrong message type"),
        }
    }
}