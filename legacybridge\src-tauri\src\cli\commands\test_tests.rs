use super::*;
use crate::cli::app::{Cli, TestArgs, TestExportFormat};
use tempfile::TempDir;
use std::path::PathBuf;

#[cfg(test)]
mod test_command_tests {
    use super::*;

    fn create_test_args() -> (TestArgs, Cli) {
        let args = TestArgs {
            suite: None,
            tags: None,
            name_pattern: None,
            parallel_jobs: None,
            continue_on_failure: false,
            verbose: false,
            generate_data: false,
            data_dir: PathBuf::from("./test-data"),
            export: None,
            export_path: None,
            baseline: false,
            update_baseline: false,
            timeout_multiplier: 1.0,
            skip_memory_tests: false,
            retry_count: 2,
            ci: false,
            list: false,
            clean: false,
        };
        
        let global_args = Cli {
            command: crate::cli::app::Commands::Test(args.clone()),
            verbose: 0,
            format: crate::cli::app::OutputFormat::Table,
            config: None,
            quiet: false,
            color: None,
            workdir: None,
        };
        
        (args, global_args)
    }

    #[tokio::test]
    async fn test_list_tests() {
        let (mut args, global_args) = create_test_args();
        args.list = true;
        
        // This should list tests without running them
        let result = handle_test_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_generate_data() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_test_args();
        args.data_dir = temp_dir.path().to_path_buf();
        args.generate_data = true;
        
        let result = handle_test_command(args, &global_args).await;
        assert!(result.is_ok());
        
        // Verify test data was generated
        assert!(temp_dir.path().join("samples").exists());
    }

    #[tokio::test]
    async fn test_run_by_suite() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_test_args();
        args.data_dir = temp_dir.path().to_path_buf();
        args.suite = Some("unit".to_string());
        
        // Generate test data first
        args.generate_data = true;
        let _ = handle_test_command(args.clone(), &global_args).await;
        
        // Run unit tests
        args.generate_data = false;
        let result = handle_test_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_run_by_tags() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_test_args();
        args.data_dir = temp_dir.path().to_path_buf();
        args.tags = Some(vec!["unit".to_string(), "fast".to_string()]);
        
        let result = handle_test_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_export_results() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_test_args();
        args.data_dir = temp_dir.path().to_path_buf();
        args.export = Some(TestExportFormat::Json);
        args.export_path = Some(temp_dir.path().join("results.json"));
        args.suite = Some("unit".to_string());
        
        let result = handle_test_command(args.clone(), &global_args).await;
        assert!(result.is_ok());
        
        // Verify export file was created
        if let Some(export_path) = args.export_path {
            assert!(export_path.exists());
        }
    }

    #[tokio::test]
    async fn test_ci_mode() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_test_args();
        args.data_dir = temp_dir.path().to_path_buf();
        args.ci = true;
        args.suite = Some("unit".to_string());
        
        let result = handle_test_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_parallel_execution() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_test_args();
        args.data_dir = temp_dir.path().to_path_buf();
        args.parallel_jobs = Some(4);
        args.suite = Some("unit".to_string());
        
        let result = handle_test_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_name_pattern_filter() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_test_args();
        args.data_dir = temp_dir.path().to_path_buf();
        args.name_pattern = Some(".*conversion.*".to_string());
        
        let result = handle_test_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_baseline_comparison() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_test_args();
        args.data_dir = temp_dir.path().to_path_buf();
        args.baseline = true;
        args.suite = Some("performance".to_string());
        
        // First run to establish baseline
        args.update_baseline = true;
        let _ = handle_test_command(args.clone(), &global_args).await;
        
        // Second run to compare with baseline
        args.update_baseline = false;
        let result = handle_test_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_clean_after_completion() {
        let temp_dir = TempDir::new().unwrap();
        let (mut args, global_args) = create_test_args();
        args.data_dir = temp_dir.path().to_path_buf();
        args.clean = true;
        args.generate_data = true;
        args.suite = Some("unit".to_string());
        
        let result = handle_test_command(args, &global_args).await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_determine_test_suite_type() {
        assert_eq!(determine_test_suite_type("unit"), TestSuiteType::Unit);
        assert_eq!(determine_test_suite_type("integration"), TestSuiteType::Integration);
        assert_eq!(determine_test_suite_type("performance"), TestSuiteType::Performance);
        assert_eq!(determine_test_suite_type("security"), TestSuiteType::Security);
        assert_eq!(determine_test_suite_type("e2e"), TestSuiteType::E2E);
        assert_eq!(determine_test_suite_type("custom_suite"), TestSuiteType::Custom("custom_suite".to_string()));
    }

    #[test]
    fn test_create_builtin_tests() {
        let tests = create_builtin_tests();
        
        // Verify we have tests for each category
        let unit_tests: Vec<_> = tests.iter().filter(|t| t.tags.contains(&"unit".to_string())).collect();
        assert!(!unit_tests.is_empty());
        
        let integration_tests: Vec<_> = tests.iter().filter(|t| t.tags.contains(&"integration".to_string())).collect();
        assert!(!integration_tests.is_empty());
        
        let performance_tests: Vec<_> = tests.iter().filter(|t| t.tags.contains(&"performance".to_string())).collect();
        assert!(!performance_tests.is_empty());
        
        // Verify all tests have required fields
        for test in &tests {
            assert!(!test.id.is_empty());
            assert!(!test.name.is_empty());
            assert!(test.timeout_ms > 0);
            assert!(!test.tags.is_empty());
        }
    }

    #[tokio::test]
    async fn test_export_formats() {
        let temp_dir = TempDir::new().unwrap();
        
        let formats = vec![
            TestExportFormat::Json,
            TestExportFormat::Junit,
            TestExportFormat::Html,
            TestExportFormat::Csv,
        ];
        
        for format in formats {
            let (mut args, global_args) = create_test_args();
            args.data_dir = temp_dir.path().to_path_buf();
            args.export = Some(format);
            args.export_path = Some(temp_dir.path().join(format!("results.{:?}", format).to_lowercase()));
            args.suite = Some("unit".to_string());
            
            let result = handle_test_command(args, &global_args).await;
            assert!(result.is_ok());
        }
    }
}