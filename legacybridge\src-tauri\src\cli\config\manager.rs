use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::env;
use crate::cli::CliError;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigProfile {
    pub name: String,
    pub description: Option<String>,
    pub settings: ProfileSettings,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProfileSettings {
    pub default_output_format: Option<String>,
    pub parallel_jobs: Option<usize>,
    pub preserve_formatting: Option<bool>,
    pub continue_on_error: Option<bool>,
    pub api_key: Option<String>,
    pub server_port: Option<u16>,
    pub server_host: Option<String>,
    pub max_file_size: Option<String>,
    pub temp_directory: Option<PathBuf>,
    pub log_level: Option<String>,
    pub compression_level: Option<u8>,
    pub quality_level: Option<u8>,
    pub default_template: Option<String>,
    pub cache_directory: Option<PathBuf>,
    pub enable_cache: Option<bool>,
    pub max_cache_size: Option<String>,
    pub default_language: Option<String>,
    pub plugins: Option<Vec<String>>,
    pub custom_options: Option<HashMap<String, String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigManager {
    pub version: String,
    pub active_profile: String,
    pub profiles: HashMap<String, ConfigProfile>,
    pub global_settings: ProfileSettings,
}

impl Default for ConfigManager {
    fn default() -> Self {
        let mut profiles = HashMap::new();
        
        // Default profile
        profiles.insert("default".to_string(), ConfigProfile {
            name: "default".to_string(),
            description: Some("Default configuration profile".to_string()),
            settings: ProfileSettings::default(),
        });
        
        // Development profile
        profiles.insert("development".to_string(), ConfigProfile {
            name: "development".to_string(),
            description: Some("Development configuration with verbose logging".to_string()),
            settings: ProfileSettings {
                log_level: Some("debug".to_string()),
                preserve_formatting: Some(true),
                continue_on_error: Some(true),
                ..Default::default()
            },
        });
        
        // Production profile
        profiles.insert("production".to_string(), ConfigProfile {
            name: "production".to_string(),
            description: Some("Production configuration with optimizations".to_string()),
            settings: ProfileSettings {
                log_level: Some("warn".to_string()),
                compression_level: Some(9),
                quality_level: Some(10),
                enable_cache: Some(true),
                ..Default::default()
            },
        });
        
        Self {
            version: "2.0.0".to_string(),
            active_profile: "default".to_string(),
            profiles,
            global_settings: ProfileSettings::default(),
        }
    }
}

impl Default for ProfileSettings {
    fn default() -> Self {
        Self {
            default_output_format: Some("md".to_string()),
            parallel_jobs: None,
            preserve_formatting: Some(true),
            continue_on_error: Some(false),
            api_key: None,
            server_port: Some(8080),
            server_host: Some("127.0.0.1".to_string()),
            max_file_size: Some("10MB".to_string()),
            temp_directory: None,
            log_level: Some("info".to_string()),
            compression_level: Some(5),
            quality_level: Some(8),
            default_template: None,
            cache_directory: None,
            enable_cache: Some(false),
            max_cache_size: Some("1GB".to_string()),
            default_language: Some("en".to_string()),
            plugins: None,
            custom_options: None,
        }
    }
}

impl ConfigManager {
    /// Load configuration from file
    pub fn load_from_file(path: &Path) -> Result<Self, CliError> {
        if !path.exists() {
            return Ok(Self::default());
        }
        
        let content = std::fs::read_to_string(path)
            .map_err(|e| CliError::Config(format!("Failed to read config file: {}", e)))?;
        
        let config: Self = toml::from_str(&content)
            .map_err(|e| CliError::Config(format!("Failed to parse config file: {}", e)))?;
        
        Ok(config)
    }
    
    /// Save configuration to file
    pub fn save_to_file(&self, path: &Path) -> Result<(), CliError> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| CliError::Config(format!("Failed to serialize config: {}", e)))?;
        
        if let Some(parent) = path.parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| CliError::Config(format!("Failed to create config directory: {}", e)))?;
        }
        
        std::fs::write(path, content)
            .map_err(|e| CliError::Config(format!("Failed to write config file: {}", e)))?;
        
        Ok(())
    }
    
    /// Get the default config file path
    pub fn default_config_path() -> Result<PathBuf, CliError> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| CliError::Config("Unable to determine config directory".to_string()))?;
        Ok(config_dir.join("legacybridge").join("config.toml"))
    }
    
    /// Get the active profile settings
    pub fn get_active_profile(&self) -> Result<&ConfigProfile, CliError> {
        self.profiles.get(&self.active_profile)
            .ok_or_else(|| CliError::Config(format!("Profile '{}' not found", self.active_profile)))
    }
    
    /// Get merged settings (global + active profile)
    pub fn get_merged_settings(&self) -> Result<ProfileSettings, CliError> {
        let profile = self.get_active_profile()?;
        let mut merged = self.global_settings.clone();
        
        // Merge profile settings over global settings
        if let Some(val) = &profile.settings.default_output_format {
            merged.default_output_format = Some(val.clone());
        }
        if let Some(val) = profile.settings.parallel_jobs {
            merged.parallel_jobs = Some(val);
        }
        if let Some(val) = profile.settings.preserve_formatting {
            merged.preserve_formatting = Some(val);
        }
        if let Some(val) = profile.settings.continue_on_error {
            merged.continue_on_error = Some(val);
        }
        if let Some(val) = &profile.settings.api_key {
            merged.api_key = Some(val.clone());
        }
        if let Some(val) = profile.settings.server_port {
            merged.server_port = Some(val);
        }
        if let Some(val) = &profile.settings.server_host {
            merged.server_host = Some(val.clone());
        }
        if let Some(val) = &profile.settings.max_file_size {
            merged.max_file_size = Some(val.clone());
        }
        if let Some(val) = &profile.settings.temp_directory {
            merged.temp_directory = Some(val.clone());
        }
        if let Some(val) = &profile.settings.log_level {
            merged.log_level = Some(val.clone());
        }
        if let Some(val) = profile.settings.compression_level {
            merged.compression_level = Some(val);
        }
        if let Some(val) = profile.settings.quality_level {
            merged.quality_level = Some(val);
        }
        if let Some(val) = &profile.settings.default_template {
            merged.default_template = Some(val.clone());
        }
        if let Some(val) = &profile.settings.cache_directory {
            merged.cache_directory = Some(val.clone());
        }
        if let Some(val) = profile.settings.enable_cache {
            merged.enable_cache = Some(val);
        }
        if let Some(val) = &profile.settings.max_cache_size {
            merged.max_cache_size = Some(val.clone());
        }
        if let Some(val) = &profile.settings.default_language {
            merged.default_language = Some(val.clone());
        }
        if let Some(val) = &profile.settings.plugins {
            merged.plugins = Some(val.clone());
        }
        if let Some(val) = &profile.settings.custom_options {
            merged.custom_options = Some(val.clone());
        }
        
        // Apply environment variable overrides
        self.apply_env_overrides(&mut merged);
        
        Ok(merged)
    }
    
    /// Apply environment variable overrides
    fn apply_env_overrides(&self, settings: &mut ProfileSettings) {
        if let Ok(val) = env::var("LEGACYBRIDGE_OUTPUT_FORMAT") {
            settings.default_output_format = Some(val);
        }
        if let Ok(val) = env::var("LEGACYBRIDGE_PARALLEL_JOBS") {
            if let Ok(jobs) = val.parse() {
                settings.parallel_jobs = Some(jobs);
            }
        }
        if let Ok(val) = env::var("LEGACYBRIDGE_LOG_LEVEL") {
            settings.log_level = Some(val);
        }
        if let Ok(val) = env::var("LEGACYBRIDGE_API_KEY") {
            settings.api_key = Some(val);
        }
        if let Ok(val) = env::var("LEGACYBRIDGE_SERVER_PORT") {
            if let Ok(port) = val.parse() {
                settings.server_port = Some(port);
            }
        }
        if let Ok(val) = env::var("LEGACYBRIDGE_SERVER_HOST") {
            settings.server_host = Some(val);
        }
        if let Ok(val) = env::var("LEGACYBRIDGE_CACHE_DIR") {
            settings.cache_directory = Some(PathBuf::from(val));
        }
        if let Ok(val) = env::var("LEGACYBRIDGE_TEMP_DIR") {
            settings.temp_directory = Some(PathBuf::from(val));
        }
    }
    
    /// Set active profile
    pub fn set_active_profile(&mut self, profile_name: &str) -> Result<(), CliError> {
        if !self.profiles.contains_key(profile_name) {
            return Err(CliError::Config(format!("Profile '{}' does not exist", profile_name)));
        }
        self.active_profile = profile_name.to_string();
        Ok(())
    }
    
    /// Create a new profile
    pub fn create_profile(&mut self, name: &str, description: Option<String>) -> Result<(), CliError> {
        if self.profiles.contains_key(name) {
            return Err(CliError::Config(format!("Profile '{}' already exists", name)));
        }
        
        self.profiles.insert(name.to_string(), ConfigProfile {
            name: name.to_string(),
            description,
            settings: ProfileSettings::default(),
        });
        
        Ok(())
    }
    
    /// Delete a profile
    pub fn delete_profile(&mut self, name: &str) -> Result<(), CliError> {
        if name == "default" {
            return Err(CliError::Config("Cannot delete the default profile".to_string()));
        }
        
        if self.active_profile == name {
            self.active_profile = "default".to_string();
        }
        
        self.profiles.remove(name)
            .ok_or_else(|| CliError::Config(format!("Profile '{}' not found", name)))?;
        
        Ok(())
    }
    
    /// Set a configuration value
    pub fn set_value(&mut self, key: &str, value: &str, profile: Option<&str>) -> Result<(), CliError> {
        let profile_name = profile.unwrap_or(&self.active_profile);
        let profile = self.profiles.get_mut(profile_name)
            .ok_or_else(|| CliError::Config(format!("Profile '{}' not found", profile_name)))?;
        
        match key {
            "default_output_format" => profile.settings.default_output_format = Some(value.to_string()),
            "parallel_jobs" => {
                let jobs = value.parse()
                    .map_err(|_| CliError::Config("Invalid parallel_jobs value".to_string()))?;
                profile.settings.parallel_jobs = Some(jobs);
            }
            "preserve_formatting" => {
                let val = value.parse()
                    .map_err(|_| CliError::Config("Invalid preserve_formatting value".to_string()))?;
                profile.settings.preserve_formatting = Some(val);
            }
            "continue_on_error" => {
                let val = value.parse()
                    .map_err(|_| CliError::Config("Invalid continue_on_error value".to_string()))?;
                profile.settings.continue_on_error = Some(val);
            }
            "api_key" => profile.settings.api_key = Some(value.to_string()),
            "server_port" => {
                let port = value.parse()
                    .map_err(|_| CliError::Config("Invalid server_port value".to_string()))?;
                profile.settings.server_port = Some(port);
            }
            "server_host" => profile.settings.server_host = Some(value.to_string()),
            "max_file_size" => profile.settings.max_file_size = Some(value.to_string()),
            "temp_directory" => profile.settings.temp_directory = Some(PathBuf::from(value)),
            "log_level" => profile.settings.log_level = Some(value.to_string()),
            "compression_level" => {
                let level = value.parse()
                    .map_err(|_| CliError::Config("Invalid compression_level value".to_string()))?;
                profile.settings.compression_level = Some(level);
            }
            "quality_level" => {
                let level = value.parse()
                    .map_err(|_| CliError::Config("Invalid quality_level value".to_string()))?;
                profile.settings.quality_level = Some(level);
            }
            "default_template" => profile.settings.default_template = Some(value.to_string()),
            "cache_directory" => profile.settings.cache_directory = Some(PathBuf::from(value)),
            "enable_cache" => {
                let val = value.parse()
                    .map_err(|_| CliError::Config("Invalid enable_cache value".to_string()))?;
                profile.settings.enable_cache = Some(val);
            }
            "max_cache_size" => profile.settings.max_cache_size = Some(value.to_string()),
            "default_language" => profile.settings.default_language = Some(value.to_string()),
            _ => {
                // Custom option
                if profile.settings.custom_options.is_none() {
                    profile.settings.custom_options = Some(HashMap::new());
                }
                if let Some(custom) = &mut profile.settings.custom_options {
                    custom.insert(key.to_string(), value.to_string());
                }
            }
        }
        
        Ok(())
    }
    
    /// Get a configuration value
    pub fn get_value(&self, key: &str, profile: Option<&str>) -> Result<Option<String>, CliError> {
        let profile_name = profile.unwrap_or(&self.active_profile);
        let settings = if profile_name == "merged" {
            self.get_merged_settings()?
        } else {
            let profile = self.profiles.get(profile_name)
                .ok_or_else(|| CliError::Config(format!("Profile '{}' not found", profile_name)))?;
            profile.settings.clone()
        };
        
        let value = match key {
            "default_output_format" => settings.default_output_format,
            "parallel_jobs" => settings.parallel_jobs.map(|v| v.to_string()),
            "preserve_formatting" => settings.preserve_formatting.map(|v| v.to_string()),
            "continue_on_error" => settings.continue_on_error.map(|v| v.to_string()),
            "api_key" => settings.api_key,
            "server_port" => settings.server_port.map(|v| v.to_string()),
            "server_host" => settings.server_host,
            "max_file_size" => settings.max_file_size,
            "temp_directory" => settings.temp_directory.map(|p| p.display().to_string()),
            "log_level" => settings.log_level,
            "compression_level" => settings.compression_level.map(|v| v.to_string()),
            "quality_level" => settings.quality_level.map(|v| v.to_string()),
            "default_template" => settings.default_template,
            "cache_directory" => settings.cache_directory.map(|p| p.display().to_string()),
            "enable_cache" => settings.enable_cache.map(|v| v.to_string()),
            "max_cache_size" => settings.max_cache_size,
            "default_language" => settings.default_language,
            _ => {
                // Check custom options
                settings.custom_options
                    .and_then(|custom| custom.get(key).cloned())
            }
        };
        
        Ok(value)
    }
    
    /// List all configuration keys
    pub fn list_keys() -> Vec<&'static str> {
        vec![
            "default_output_format",
            "parallel_jobs",
            "preserve_formatting",
            "continue_on_error",
            "api_key",
            "server_port",
            "server_host",
            "max_file_size",
            "temp_directory",
            "log_level",
            "compression_level",
            "quality_level",
            "default_template",
            "cache_directory",
            "enable_cache",
            "max_cache_size",
            "default_language",
        ]
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_config() {
        let config = ConfigManager::default();
        assert_eq!(config.active_profile, "default");
        assert_eq!(config.profiles.len(), 3);
        assert!(config.profiles.contains_key("default"));
        assert!(config.profiles.contains_key("development"));
        assert!(config.profiles.contains_key("production"));
    }
    
    #[test]
    fn test_profile_management() {
        let mut config = ConfigManager::default();
        
        // Create new profile
        assert!(config.create_profile("test", Some("Test profile".to_string())).is_ok());
        assert!(config.profiles.contains_key("test"));
        
        // Set active profile
        assert!(config.set_active_profile("test").is_ok());
        assert_eq!(config.active_profile, "test");
        
        // Delete profile
        assert!(config.delete_profile("test").is_ok());
        assert!(!config.profiles.contains_key("test"));
        assert_eq!(config.active_profile, "default");
    }
    
    #[test]
    fn test_set_get_value() {
        let mut config = ConfigManager::default();
        
        // Set value
        assert!(config.set_value("log_level", "debug", None).is_ok());
        
        // Get value
        let value = config.get_value("log_level", None).unwrap();
        assert_eq!(value, Some("debug".to_string()));
    }
}