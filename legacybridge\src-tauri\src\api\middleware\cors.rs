//! CORS middleware for cross-origin requests
//!
//! This module provides flexible CORS configuration for the API server,
//! allowing browser-based clients to access the API endpoints.

use axum::http::{
    header::{CONTENT_TYPE, AUTHORIZATION, ACCEPT},
    HeaderValue, Method,
};
use tower_http::cors::{AllowHeaders, AllowMethods, AllowOrigin, CorsLayer};
use tracing::info;

/// CORS configuration options
#[derive(Debug, Clone)]
pub struct CorsConfig {
    /// Allowed origins (empty = allow all)
    pub allowed_origins: Vec<String>,
    /// Allow credentials
    pub allow_credentials: bool,
    /// Max age for preflight cache (in seconds)
    pub max_age: u64,
}

impl Default for CorsConfig {
    fn default() -> Self {
        Self {
            allowed_origins: vec![],
            allow_credentials: false,
            max_age: 3600, // 1 hour
        }
    }
}

/// Create a configured CORS layer
pub fn create_cors_layer(config: CorsConfig) -> CorsLayer {
    info!("Configuring CORS with: {:?}", config);
    
    let mut cors = CorsLayer::new();
    
    // Configure allowed origins
    if config.allowed_origins.is_empty() {
        info!("CORS: Allowing all origins");
        cors = cors.allow_origin(AllowOrigin::any());
    } else {
        info!("CORS: Allowing specific origins: {:?}", config.allowed_origins);
        let origins: Vec<HeaderValue> = config.allowed_origins
            .into_iter()
            .filter_map(|origin| {
                match HeaderValue::from_str(&origin) {
                    Ok(value) => Some(value),
                    Err(e) => {
                        tracing::warn!("Invalid CORS origin '{}': {}", origin, e);
                        None
                    }
                }
            })
            .collect();
        
        if !origins.is_empty() {
            cors = cors.allow_origin(origins);
        } else {
            // Fallback to any origin if all provided origins were invalid
            cors = cors.allow_origin(AllowOrigin::any());
        }
    }
    
    // Configure allowed methods
    cors = cors.allow_methods(AllowMethods::list(vec![
        Method::GET,
        Method::POST,
        Method::PUT,
        Method::DELETE,
        Method::OPTIONS,
        Method::HEAD,
        Method::PATCH,
    ]));
    
    // Configure allowed headers
    cors = cors.allow_headers(AllowHeaders::list(vec![
        CONTENT_TYPE,
        AUTHORIZATION,
        ACCEPT,
        HeaderValue::from_static("x-api-key"),
        HeaderValue::from_static("x-request-id"),
        HeaderValue::from_static("x-correlation-id"),
    ]));
    
    // Configure exposed headers (headers that the browser can access)
    cors = cors.expose_headers(vec![
        HeaderValue::from_static("x-request-id"),
        HeaderValue::from_static("x-rate-limit-limit"),
        HeaderValue::from_static("x-rate-limit-remaining"),
        HeaderValue::from_static("x-rate-limit-reset"),
    ]);
    
    // Configure credentials
    if config.allow_credentials {
        cors = cors.allow_credentials(true);
    }
    
    // Configure max age for preflight requests
    cors = cors.max_age(std::time::Duration::from_secs(config.max_age));
    
    cors
}

/// Create a CORS layer from environment configuration
pub fn from_env() -> CorsLayer {
    let config = CorsConfig {
        allowed_origins: std::env::var("CORS_ALLOWED_ORIGINS")
            .unwrap_or_default()
            .split(',')
            .filter(|s| !s.is_empty())
            .map(|s| s.trim().to_string())
            .collect(),
        allow_credentials: std::env::var("CORS_ALLOW_CREDENTIALS")
            .unwrap_or_else(|_| "false".to_string())
            .parse()
            .unwrap_or(false),
        max_age: std::env::var("CORS_MAX_AGE")
            .unwrap_or_else(|_| "3600".to_string())
            .parse()
            .unwrap_or(3600),
    };
    
    create_cors_layer(config)
}

/// Create a permissive CORS layer for development
pub fn permissive() -> CorsLayer {
    info!("Creating permissive CORS layer for development");
    
    CorsLayer::new()
        .allow_origin(AllowOrigin::any())
        .allow_methods(AllowMethods::any())
        .allow_headers(AllowHeaders::any())
        .allow_credentials(true)
        .max_age(std::time::Duration::from_secs(3600))
}

/// Create a restrictive CORS layer for production
pub fn restrictive(allowed_origins: Vec<String>) -> CorsLayer {
    info!("Creating restrictive CORS layer for production");
    
    let config = CorsConfig {
        allowed_origins,
        allow_credentials: false,
        max_age: 86400, // 24 hours
    };
    
    create_cors_layer(config)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_config() {
        let config = CorsConfig::default();
        assert!(config.allowed_origins.is_empty());
        assert!(!config.allow_credentials);
        assert_eq!(config.max_age, 3600);
    }
    
    #[test]
    fn test_create_cors_layer_any_origin() {
        let config = CorsConfig::default();
        let _layer = create_cors_layer(config);
        // Layer created successfully
    }
    
    #[test]
    fn test_create_cors_layer_specific_origins() {
        let config = CorsConfig {
            allowed_origins: vec![
                "http://localhost:3000".to_string(),
                "https://example.com".to_string(),
            ],
            allow_credentials: true,
            max_age: 7200,
        };
        let _layer = create_cors_layer(config);
        // Layer created successfully
    }
    
    #[test]
    fn test_permissive_layer() {
        let _layer = permissive();
        // Layer created successfully
    }
    
    #[test]
    fn test_restrictive_layer() {
        let origins = vec!["https://app.example.com".to_string()];
        let _layer = restrictive(origins);
        // Layer created successfully
    }
}