use super::*;
use std::sync::Arc;
use tokio::sync::Mutex;
use tempfile::TempDir;
use chrono::Utc;

#[cfg(test)]
mod test_runner_tests {
    use super::*;

    async fn create_test_runner() -> (TestRunner, TempDir) {
        let temp_dir = TempDir::new().unwrap();
        let data_manager = TestDataManager::new(&temp_dir.path()).unwrap();
        let conversion_engine = ConversionEngine::new();
        let runner = TestRunner::new(data_manager, conversion_engine);
        (runner, temp_dir)
    }

    #[tokio::test]
    async fn test_runner_creation() {
        let (runner, _temp_dir) = create_test_runner().await;
        assert_eq!(runner.max_parallel_tests, num_cpus::get());
    }

    #[tokio::test]
    async fn test_add_test_case() {
        let (mut runner, _temp_dir) = create_test_runner().await;
        
        let test_case = TestCase {
            id: "test1".to_string(),
            name: "Test Case 1".to_string(),
            test_type: TestType::Unit,
            tags: vec!["unit".to_string()],
            timeout_ms: 5000,
            retry_count: 2,
            expected_outcome: None,
        };
        
        runner.add_test(test_case.clone());
        
        // Verify test was added
        let tests = runner.list_tests();
        assert_eq!(tests.len(), 1);
        assert_eq!(tests[0].id, "test1");
    }

    #[tokio::test]
    async fn test_run_single_test() {
        let (mut runner, _temp_dir) = create_test_runner().await;
        
        // Add a simple test case
        let test_case = TestCase {
            id: "unit_test".to_string(),
            name: "Simple Unit Test".to_string(),
            test_type: TestType::Unit,
            tags: vec!["unit".to_string()],
            timeout_ms: 5000,
            retry_count: 0,
            expected_outcome: None,
        };
        
        runner.add_test(test_case);
        
        // Run the test
        let result = runner.run_single_test("unit_test").await;
        assert!(result.is_some());
        
        let test_result = result.unwrap();
        assert_eq!(test_result.test_id, "unit_test");
        // Unit tests should pass in this mock scenario
        assert_eq!(test_result.status, TestStatus::Passed);
    }

    #[tokio::test]
    async fn test_run_all_tests() {
        let (mut runner, _temp_dir) = create_test_runner().await;
        
        // Add multiple test cases
        for i in 0..3 {
            let test_case = TestCase {
                id: format!("test_{}", i),
                name: format!("Test Case {}", i),
                test_type: TestType::Unit,
                tags: vec!["unit".to_string()],
                timeout_ms: 5000,
                retry_count: 0,
                expected_outcome: None,
            };
            runner.add_test(test_case);
        }
        
        // Run all tests
        let results = runner.run_all_tests().await;
        assert_eq!(results.len(), 3);
        
        // All should pass
        for result in &results {
            assert_eq!(result.status, TestStatus::Passed);
        }
    }

    #[tokio::test]
    async fn test_run_tests_by_tag() {
        let (mut runner, _temp_dir) = create_test_runner().await;
        
        // Add tests with different tags
        runner.add_test(TestCase {
            id: "unit1".to_string(),
            name: "Unit Test 1".to_string(),
            test_type: TestType::Unit,
            tags: vec!["unit".to_string(), "fast".to_string()],
            timeout_ms: 5000,
            retry_count: 0,
            expected_outcome: None,
        });
        
        runner.add_test(TestCase {
            id: "integration1".to_string(),
            name: "Integration Test 1".to_string(),
            test_type: TestType::Integration,
            tags: vec!["integration".to_string(), "slow".to_string()],
            timeout_ms: 10000,
            retry_count: 0,
            expected_outcome: None,
        });
        
        // Run only unit tests
        let unit_results = runner.run_tests_by_tag("unit").await;
        assert_eq!(unit_results.len(), 1);
        assert_eq!(unit_results[0].test_id, "unit1");
        
        // Run only integration tests
        let integration_results = runner.run_tests_by_tag("integration").await;
        assert_eq!(integration_results.len(), 1);
        assert_eq!(integration_results[0].test_id, "integration1");
    }

    #[tokio::test]
    async fn test_test_retry_mechanism() {
        let (mut runner, _temp_dir) = create_test_runner().await;
        
        // Add a test that might fail (simulated by memory leak test)
        let test_case = TestCase {
            id: "flaky_test".to_string(),
            name: "Flaky Test".to_string(),
            test_type: TestType::MemoryLeak,
            tags: vec!["memory".to_string()],
            timeout_ms: 5000,
            retry_count: 2, // Allow 2 retries
            expected_outcome: None,
        };
        
        runner.add_test(test_case);
        
        // Run the test
        let result = runner.run_single_test("flaky_test").await.unwrap();
        
        // Should have retry info if it failed initially
        assert!(result.retry_count <= 2);
    }

    #[tokio::test]
    async fn test_test_timeout() {
        let (runner, _temp_dir) = create_test_runner().await;
        
        // Test timeout detection
        let start = Instant::now();
        let timeout = Duration::from_millis(100);
        
        tokio::time::sleep(Duration::from_millis(150)).await;
        
        assert!(start.elapsed() > timeout);
    }

    #[tokio::test]
    async fn test_generate_test_report() {
        let (runner, _temp_dir) = create_test_runner().await;
        
        // Create test results
        let results = vec![
            TestResult {
                test_id: "test1".to_string(),
                test_name: "Test 1".to_string(),
                status: TestStatus::Passed,
                duration: Duration::from_millis(100),
                error_message: None,
                retry_count: 0,
                timestamp: Utc::now(),
            },
            TestResult {
                test_id: "test2".to_string(),
                test_name: "Test 2".to_string(),
                status: TestStatus::Failed,
                duration: Duration::from_millis(200),
                error_message: Some("Test failed".to_string()),
                retry_count: 1,
                timestamp: Utc::now(),
            },
        ];
        
        let report = runner.generate_test_report(results);
        
        assert_eq!(report.total_tests, 2);
        assert_eq!(report.passed, 1);
        assert_eq!(report.failed, 1);
        assert_eq!(report.skipped, 0);
        assert_eq!(report.success_rate, 50.0);
    }

    #[tokio::test]
    async fn test_export_junit_xml() {
        let (runner, temp_dir) = create_test_runner().await;
        
        let report = TestReport {
            total_tests: 3,
            passed: 2,
            failed: 1,
            skipped: 0,
            total_duration: Duration::from_secs(5),
            start_time: Utc::now(),
            end_time: Utc::now(),
            success_rate: 66.67,
            test_results: vec![
                TestResult {
                    test_id: "test1".to_string(),
                    test_name: "Test 1".to_string(),
                    status: TestStatus::Passed,
                    duration: Duration::from_millis(100),
                    error_message: None,
                    retry_count: 0,
                    timestamp: Utc::now(),
                },
            ],
        };
        
        let export_path = temp_dir.path().join("junit.xml");
        runner.export_junit_xml(&report, &export_path).await.unwrap();
        
        assert!(export_path.exists());
        
        // Verify XML structure
        let content = std::fs::read_to_string(&export_path).unwrap();
        assert!(content.contains("<testsuites"));
        assert!(content.contains("<testsuite"));
        assert!(content.contains("<testcase"));
    }

    #[tokio::test]
    async fn test_test_type_execution() {
        let (runner, _temp_dir) = create_test_runner().await;
        
        // Test each test type
        let test_types = vec![
            TestType::Unit,
            TestType::Integration,
            TestType::FormatValidation,
            TestType::PerformanceRegression,
            TestType::MemoryLeak,
            TestType::AccuracyValidation,
        ];
        
        for test_type in test_types {
            let test_case = TestCase {
                id: format!("{:?}_test", test_type),
                name: format!("{:?} Test", test_type),
                test_type: test_type.clone(),
                tags: vec![],
                timeout_ms: 5000,
                retry_count: 0,
                expected_outcome: None,
            };
            
            // Each test type should be executable
            let result = runner.execute_test(&test_case).await;
            assert!(result.duration.as_millis() > 0);
        }
    }

    #[tokio::test]
    async fn test_parallel_test_execution() {
        let (mut runner, _temp_dir) = create_test_runner().await;
        runner.max_parallel_tests = 2; // Limit parallelism for testing
        
        // Add multiple tests
        for i in 0..5 {
            runner.add_test(TestCase {
                id: format!("parallel_test_{}", i),
                name: format!("Parallel Test {}", i),
                test_type: TestType::Unit,
                tags: vec!["parallel".to_string()],
                timeout_ms: 1000,
                retry_count: 0,
                expected_outcome: None,
            });
        }
        
        let start = Instant::now();
        let results = runner.run_all_tests().await;
        let duration = start.elapsed();
        
        // All tests should complete
        assert_eq!(results.len(), 5);
        
        // With parallelism of 2, should be faster than sequential
        // (This is a rough check, actual timing may vary)
        assert!(duration.as_millis() < 5000);
    }

    #[test]
    fn test_status_ordering() {
        assert!(TestStatus::Passed < TestStatus::Failed);
        assert!(TestStatus::Failed < TestStatus::Skipped);
        assert!(TestStatus::Running < TestStatus::Passed);
    }

    #[test]
    fn test_type_display() {
        assert_eq!(format!("{:?}", TestType::Unit), "Unit");
        assert_eq!(format!("{:?}", TestType::Integration), "Integration");
        assert_eq!(format!("{:?}", TestType::FormatValidation), "FormatValidation");
    }
}