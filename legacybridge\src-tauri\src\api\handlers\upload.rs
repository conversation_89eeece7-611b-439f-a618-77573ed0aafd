use crate::api::models::{UploadRequest, UploadResponse, ApiError};
use axum::{
    extract::{Multipart, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use std::sync::Arc;
use tracing::{error, info};
use uuid::Uuid;

/// File upload endpoint handler
pub async fn upload_file(
    State(_state): State<Arc<crate::api::handlers::convert::AppState>>,
    mut multipart: Multipart,
) -> impl IntoResponse {
    info!("File upload request received");
    
    while let Some(field) = multipart.next_field().await.unwrap() {
        let name = field.name().unwrap_or("unknown").to_string();
        let file_name = field.file_name().unwrap_or("unknown").to_string();
        let content_type = field.content_type().unwrap_or("application/octet-stream").to_string();
        
        info!("Receiving file: {} ({})", file_name, content_type);
        
        // Read file data
        match field.bytes().await {
            Ok(bytes) => {
                let size = bytes.len() as u64;
                let upload_id = Uuid::new_v4().to_string();
                
                // TODO: Save file to temporary storage
                info!("File uploaded: {} ({} bytes)", file_name, size);
                
                let response = UploadResponse {
                    upload_id,
                    filename: file_name,
                    size,
                    expires_at: chrono::Utc::now().timestamp() as u64 + 3600, // 1 hour
                };
                
                return (StatusCode::OK, Json(response)).into_response();
            }
            Err(e) => {
                error!("Failed to read upload data: {}", e);
                return (
                    StatusCode::BAD_REQUEST,
                    Json(ApiError::new(
                        "upload_failed",
                        "Failed to read upload data"
                    )),
                ).into_response();
            }
        }
    }
    
    (
        StatusCode::BAD_REQUEST,
        Json(ApiError::new(
            "no_file",
            "No file found in upload request"
        )),
    ).into_response()
}