use glob::{glob_with, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Error};
use std::path::{Path, PathBuf};
use std::collections::HashSet;
use crate::cli::CliError;

/// Utilities for handling glob patterns and file discovery
pub struct GlobUtils;

impl GlobUtils {
    /// Expand a glob pattern into a list of matching file paths
    pub fn expand_glob(pattern: &str) -> Result<Vec<PathBuf>, CliError> {
        let options = MatchOptions {
            case_sensitive: false,
            require_literal_separator: false,
            require_literal_leading_dot: false,
        };
        
        let mut paths = Vec::new();
        let entries = glob_with(pattern, options)
            .map_err(|e| CliError::GlobError(format!("Invalid glob pattern '{}': {}", pattern, e)))?;
        
        for entry in entries {
            match entry {
                Ok(path) => {
                    if path.is_file() {
                        paths.push(path);
                    }
                }
                Err(e) => {
                    // Log but don't fail on individual path errors
                    eprintln!("Warning: Error reading path: {}", e);
                }
            }
        }
        
        Ok(paths)
    }
    
    /// Expand multiple glob patterns into a deduplicated list of file paths
    pub fn expand_multiple_globs(patterns: &[String]) -> Result<Vec<PathBuf>, CliError> {
        let mut all_paths = HashSet::new();
        
        for pattern in patterns {
            let paths = Self::expand_glob(pattern)?;
            for path in paths {
                all_paths.insert(path);
            }
        }
        
        let mut sorted_paths: Vec<_> = all_paths.into_iter().collect();
        sorted_paths.sort();
        Ok(sorted_paths)
    }
    
    /// Check if a string contains glob special characters
    pub fn is_glob_pattern(pattern: &str) -> bool {
        pattern.contains('*') || pattern.contains('?') || pattern.contains('[') || pattern.contains('{')
    }
    
    /// Filter files by extension from a list of paths
    pub fn filter_by_extensions(paths: &[PathBuf], extensions: &[&str]) -> Vec<PathBuf> {
        paths.iter()
            .filter(|path| {
                if let Some(ext) = path.extension() {
                    extensions.iter().any(|&e| ext == e)
                } else {
                    false
                }
            })
            .cloned()
            .collect()
    }
    
    /// Create a glob pattern for files with specific extensions in a directory
    pub fn create_extension_pattern(dir: &Path, extensions: &[&str]) -> String {
        if extensions.is_empty() {
            return format!("{}/*", dir.display());
        }
        
        if extensions.len() == 1 {
            format!("{}/*.{}", dir.display(), extensions[0])
        } else {
            format!("{}/**.{{{}}}", dir.display(), extensions.join(","))
        }
    }
    
    /// Recursively find all files matching patterns in a directory
    pub fn find_files_recursive(
        base_dir: &Path,
        patterns: &[&str],
        max_depth: Option<usize>,
    ) -> Result<Vec<PathBuf>, CliError> {
        let mut all_files = Vec::new();
        
        for pattern in patterns {
            let full_pattern = if base_dir.to_str().unwrap_or("").is_empty() {
                pattern.to_string()
            } else {
                format!("{}/{}", base_dir.display(), pattern)
            };
            
            let files = Self::expand_glob(&full_pattern)?;
            all_files.extend(files);
        }
        
        // Apply max depth filtering if specified
        if let Some(max_depth) = max_depth {
            all_files.retain(|path| {
                let depth = path.strip_prefix(base_dir).unwrap_or(path).components().count();
                depth <= max_depth
            });
        }
        
        // Remove duplicates and sort
        let unique_files: HashSet<_> = all_files.into_iter().collect();
        let mut sorted_files: Vec<_> = unique_files.into_iter().collect();
        sorted_files.sort();
        
        Ok(sorted_files)
    }
    
    /// Validate that a glob pattern is syntactically correct
    pub fn validate_pattern(pattern: &str) -> Result<(), PatternError> {
        Pattern::new(pattern)?;
        Ok(())
    }
    
    /// Convert a simple file pattern to a glob pattern
    /// Examples: "*.doc" stays the same, "docs" becomes "docs/*"
    pub fn normalize_pattern(pattern: &str) -> String {
        if Self::is_glob_pattern(pattern) {
            pattern.to_string()
        } else if Path::new(pattern).is_dir() {
            format!("{}/**/*", pattern)
        } else {
            pattern.to_string()
        }
    }
    
    /// Group files by their parent directory
    pub fn group_by_directory(paths: &[PathBuf]) -> std::collections::HashMap<PathBuf, Vec<PathBuf>> {
        let mut groups = std::collections::HashMap::new();
        
        for path in paths {
            if let Some(parent) = path.parent() {
                groups.entry(parent.to_path_buf())
                    .or_insert_with(Vec::new)
                    .push(path.clone());
            }
        }
        
        groups
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;
    
    #[test]
    fn test_is_glob_pattern() {
        assert!(GlobUtils::is_glob_pattern("*.txt"));
        assert!(GlobUtils::is_glob_pattern("docs/*.md"));
        assert!(GlobUtils::is_glob_pattern("file?.doc"));
        assert!(GlobUtils::is_glob_pattern("[abc].txt"));
        assert!(GlobUtils::is_glob_pattern("{a,b,c}.txt"));
        assert!(!GlobUtils::is_glob_pattern("file.txt"));
        assert!(!GlobUtils::is_glob_pattern("docs/file.md"));
    }
    
    #[test]
    fn test_create_extension_pattern() {
        let dir = Path::new("/docs");
        
        assert_eq!(
            GlobUtils::create_extension_pattern(dir, &["txt"]),
            "/docs/*.txt"
        );
        
        assert_eq!(
            GlobUtils::create_extension_pattern(dir, &["txt", "md", "doc"]),
            "/docs/**.{txt,md,doc}"
        );
        
        assert_eq!(
            GlobUtils::create_extension_pattern(dir, &[]),
            "/docs/*"
        );
    }
    
    #[test]
    fn test_normalize_pattern() {
        assert_eq!(GlobUtils::normalize_pattern("*.txt"), "*.txt");
        assert_eq!(GlobUtils::normalize_pattern("file.txt"), "file.txt");
    }
    
    #[test]
    fn test_filter_by_extensions() {
        let paths = vec![
            PathBuf::from("file1.txt"),
            PathBuf::from("file2.md"),
            PathBuf::from("file3.doc"),
            PathBuf::from("file4.txt"),
            PathBuf::from("file5"),
        ];
        
        let filtered = GlobUtils::filter_by_extensions(&paths, &["txt"]);
        assert_eq!(filtered.len(), 2);
        assert!(filtered.contains(&PathBuf::from("file1.txt")));
        assert!(filtered.contains(&PathBuf::from("file4.txt")));
        
        let filtered = GlobUtils::filter_by_extensions(&paths, &["md", "doc"]);
        assert_eq!(filtered.len(), 2);
        assert!(filtered.contains(&PathBuf::from("file2.md")));
        assert!(filtered.contains(&PathBuf::from("file3.doc")));
    }
    
    #[test]
    fn test_expand_glob_with_temp_files() -> Result<(), Box<dyn std::error::Error>> {
        let temp_dir = TempDir::new()?;
        let base_path = temp_dir.path();
        
        // Create test files
        fs::write(base_path.join("test1.txt"), "content")?;
        fs::write(base_path.join("test2.txt"), "content")?;
        fs::write(base_path.join("test.md"), "content")?;
        fs::create_dir(base_path.join("subdir"))?;
        fs::write(base_path.join("subdir/test3.txt"), "content")?;
        
        // Test simple glob
        let pattern = format!("{}/*.txt", base_path.display());
        let files = GlobUtils::expand_glob(&pattern)?;
        assert_eq!(files.len(), 2);
        
        // Test recursive glob
        let pattern = format!("{}/**/*.txt", base_path.display());
        let files = GlobUtils::expand_glob(&pattern)?;
        assert_eq!(files.len(), 3);
        
        Ok(())
    }
    
    #[test]
    fn test_group_by_directory() {
        let paths = vec![
            PathBuf::from("/docs/file1.txt"),
            PathBuf::from("/docs/file2.txt"),
            PathBuf::from("/src/file3.rs"),
            PathBuf::from("/src/file4.rs"),
            PathBuf::from("/root.txt"),
        ];
        
        let groups = GlobUtils::group_by_directory(&paths);
        assert_eq!(groups.len(), 3);
        assert_eq!(groups.get(&PathBuf::from("/docs")).unwrap().len(), 2);
        assert_eq!(groups.get(&PathBuf::from("/src")).unwrap().len(), 2);
        assert_eq!(groups.get(&PathBuf::from("/")).unwrap().len(), 1);
    }
}