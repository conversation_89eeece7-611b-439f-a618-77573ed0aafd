use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::fs;
use std::io;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use fake::{Fake, Faker};
use uuid::Uuid;

/// Test data types supported by the framework
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TestDataType {
    /// Sample input files for testing
    SampleFile {
        format: String,
        size_category: SizeCategory,
    },
    /// Expected output for validation
    ExpectedOutput {
        input_file: String,
        output_format: String,
    },
    /// Performance baseline data
    PerformanceBaseline {
        operation: String,
        metrics: PerformanceMetrics,
    },
    /// Test configuration
    Configuration {
        name: String,
        parameters: HashMap<String, serde_json::Value>,
    },
}

/// Size categories for test files
#[derive(Debug, <PERSON>lone, Copy, Serialize, Deserialize)]
pub enum SizeCategory {
    Tiny,      // < 1KB
    Small,     // 1KB - 100KB
    Medium,    // 100KB - 1MB
    Large,     // 1MB - 10MB
    Huge,      // > 10MB
}

/// Performance metrics for baseline comparison
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub duration_ms: f64,
    pub memory_bytes: u64,
    pub cpu_percent: f32,
    pub throughput_mbps: f64,
    pub timestamp: DateTime<Utc>,
}

/// Test data item with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestDataItem {
    pub id: Uuid,
    pub name: String,
    pub data_type: TestDataType,
    pub path: PathBuf,
    pub checksum: String,
    pub created_at: DateTime<Utc>,
    pub tags: Vec<String>,
}

/// Test data manager for handling test files and baselines
pub struct TestDataManager {
    data_dir: PathBuf,
    cache: HashMap<Uuid, TestDataItem>,
}

impl TestDataManager {
    /// Create a new test data manager
    pub fn new(data_dir: impl AsRef<Path>) -> io::Result<Self> {
        let data_dir = data_dir.as_ref().to_path_buf();
        fs::create_dir_all(&data_dir)?;
        
        let mut manager = Self {
            data_dir,
            cache: HashMap::new(),
        };
        
        manager.load_metadata()?;
        Ok(manager)
    }
    
    /// Generate sample test files for a format
    pub fn generate_sample_files(&mut self, format: &str, count: usize) -> io::Result<Vec<TestDataItem>> {
        let mut items = Vec::new();
        
        for _ in 0..count {
            for size in [SizeCategory::Tiny, SizeCategory::Small, SizeCategory::Medium] {
                let item = self.generate_sample_file(format, size)?;
                items.push(item);
            }
        }
        
        Ok(items)
    }
    
    /// Generate a single sample file
    fn generate_sample_file(&mut self, format: &str, size: SizeCategory) -> io::Result<TestDataItem> {
        let content = match format {
            "rtf" => self.generate_rtf_content(size),
            "doc" => self.generate_doc_content(size),
            "wpd" => self.generate_wpd_content(size),
            "txt" => self.generate_txt_content(size),
            "md" => self.generate_md_content(size),
            _ => self.generate_generic_content(size),
        };
        
        let filename = format!("sample_{}_{}_{}.{}", 
            format, 
            size.as_str(), 
            Uuid::new_v4().to_string().split('-').next().unwrap(),
            format
        );
        
        let path = self.data_dir.join("samples").join(format).join(&filename);
        fs::create_dir_all(path.parent().unwrap())?;
        fs::write(&path, &content)?;
        
        let checksum = self.calculate_checksum(&content);
        
        let item = TestDataItem {
            id: Uuid::new_v4(),
            name: filename,
            data_type: TestDataType::SampleFile {
                format: format.to_string(),
                size_category: size,
            },
            path,
            checksum,
            created_at: Utc::now(),
            tags: vec![format.to_string(), size.as_str().to_string()],
        };
        
        self.cache.insert(item.id, item.clone());
        self.save_metadata()?;
        
        Ok(item)
    }
    
    /// Generate RTF content
    fn generate_rtf_content(&self, size: SizeCategory) -> Vec<u8> {
        let paragraphs = match size {
            SizeCategory::Tiny => 1,
            SizeCategory::Small => 10,
            SizeCategory::Medium => 100,
            SizeCategory::Large => 1000,
            SizeCategory::Huge => 10000,
        };
        
        let mut content = String::from(r"{\rtf1\ansi\deff0 {\fonttbl{\f0 Times New Roman;}}");
        
        for i in 0..paragraphs {
            content.push_str(&format!(
                r"\par\f0\fs24 {} \par",
                Faker.fake::<String>()
            ));
            
            if i % 10 == 0 {
                // Add some formatting
                content.push_str(r"\b Bold text\b0 and \i italic text\i0. ");
            }
        }
        
        content.push_str("}");
        content.into_bytes()
    }
    
    /// Generate DOC content (simplified)
    fn generate_doc_content(&self, size: SizeCategory) -> Vec<u8> {
        // This is a simplified version - real DOC files are complex
        let mut content = Vec::new();
        
        // DOC header (simplified)
        content.extend_from_slice(&[0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]);
        
        // Add fake content based on size
        let bytes = match size {
            SizeCategory::Tiny => 512,
            SizeCategory::Small => 10_240,
            SizeCategory::Medium => 102_400,
            SizeCategory::Large => 1_048_576,
            SizeCategory::Huge => 10_485_760,
        };
        
        for _ in 0..bytes {
            content.push(Faker.fake::<u8>());
        }
        
        content
    }
    
    /// Generate WPD content (simplified)
    fn generate_wpd_content(&self, size: SizeCategory) -> Vec<u8> {
        // WPD header (simplified)
        let mut content = vec![0xFF, 0x57, 0x50, 0x43];
        
        let paragraphs = match size {
            SizeCategory::Tiny => 1,
            SizeCategory::Small => 10,
            SizeCategory::Medium => 100,
            SizeCategory::Large => 1000,
            SizeCategory::Huge => 10000,
        };
        
        for _ in 0..paragraphs {
            let text: String = Faker.fake();
            content.extend_from_slice(text.as_bytes());
            content.push(0x0D); // CR
            content.push(0x0A); // LF
        }
        
        content
    }
    
    /// Generate plain text content
    fn generate_txt_content(&self, size: SizeCategory) -> Vec<u8> {
        let words = match size {
            SizeCategory::Tiny => 10,
            SizeCategory::Small => 1000,
            SizeCategory::Medium => 10000,
            SizeCategory::Large => 100000,
            SizeCategory::Huge => 1000000,
        };
        
        let mut content = String::new();
        for i in 0..words {
            content.push_str(&Faker.fake::<String>());
            content.push(' ');
            if i % 10 == 9 {
                content.push('\n');
            }
        }
        
        content.into_bytes()
    }
    
    /// Generate markdown content
    fn generate_md_content(&self, size: SizeCategory) -> Vec<u8> {
        let sections = match size {
            SizeCategory::Tiny => 1,
            SizeCategory::Small => 5,
            SizeCategory::Medium => 20,
            SizeCategory::Large => 100,
            SizeCategory::Huge => 500,
        };
        
        let mut content = String::from("# Test Document\n\n");
        
        for i in 0..sections {
            content.push_str(&format!("## Section {}\n\n", i + 1));
            
            for _ in 0..5 {
                content.push_str(&format!("{}\n\n", Faker.fake::<String>()));
            }
            
            if i % 3 == 0 {
                content.push_str("### Subsection\n\n");
                content.push_str("* Item 1\n* Item 2\n* Item 3\n\n");
            }
        }
        
        content.into_bytes()
    }
    
    /// Generate generic binary content
    fn generate_generic_content(&self, size: SizeCategory) -> Vec<u8> {
        let bytes = match size {
            SizeCategory::Tiny => 512,
            SizeCategory::Small => 10_240,
            SizeCategory::Medium => 102_400,
            SizeCategory::Large => 1_048_576,
            SizeCategory::Huge => 10_485_760,
        };
        
        (0..bytes).map(|_| Faker.fake::<u8>()).collect()
    }
    
    /// Calculate checksum for content
    fn calculate_checksum(&self, content: &[u8]) -> String {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(content);
        format!("{:x}", hasher.finalize())
    }
    
    /// Store performance baseline
    pub fn store_baseline(&mut self, operation: &str, metrics: PerformanceMetrics) -> io::Result<TestDataItem> {
        let filename = format!("baseline_{}_{}.json", operation, Utc::now().timestamp());
        let path = self.data_dir.join("baselines").join(&filename);
        
        fs::create_dir_all(path.parent().unwrap())?;
        
        let json = serde_json::to_string_pretty(&metrics)?;
        fs::write(&path, &json)?;
        
        let item = TestDataItem {
            id: Uuid::new_v4(),
            name: filename,
            data_type: TestDataType::PerformanceBaseline {
                operation: operation.to_string(),
                metrics,
            },
            path,
            checksum: self.calculate_checksum(json.as_bytes()),
            created_at: Utc::now(),
            tags: vec!["baseline".to_string(), operation.to_string()],
        };
        
        self.cache.insert(item.id, item.clone());
        self.save_metadata()?;
        
        Ok(item)
    }
    
    /// Get test data by ID
    pub fn get(&self, id: &Uuid) -> Option<&TestDataItem> {
        self.cache.get(id)
    }
    
    /// Get test data by tags
    pub fn get_by_tags(&self, tags: &[String]) -> Vec<&TestDataItem> {
        self.cache.values()
            .filter(|item| tags.iter().all(|tag| item.tags.contains(tag)))
            .collect()
    }
    
    /// Get all test data of a specific type
    pub fn get_by_type(&self, data_type: &str) -> Vec<&TestDataItem> {
        self.cache.values()
            .filter(|item| match &item.data_type {
                TestDataType::SampleFile { .. } => data_type == "sample",
                TestDataType::ExpectedOutput { .. } => data_type == "expected",
                TestDataType::PerformanceBaseline { .. } => data_type == "baseline",
                TestDataType::Configuration { .. } => data_type == "config",
            })
            .collect()
    }
    
    /// Load metadata from disk
    fn load_metadata(&mut self) -> io::Result<()> {
        let metadata_path = self.data_dir.join("metadata.json");
        
        if metadata_path.exists() {
            let content = fs::read_to_string(&metadata_path)?;
            let items: Vec<TestDataItem> = serde_json::from_str(&content)
                .map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))?;
            
            for item in items {
                self.cache.insert(item.id, item);
            }
        }
        
        Ok(())
    }
    
    /// Save metadata to disk
    fn save_metadata(&self) -> io::Result<()> {
        let metadata_path = self.data_dir.join("metadata.json");
        let items: Vec<_> = self.cache.values().cloned().collect();
        let json = serde_json::to_string_pretty(&items)?;
        fs::write(&metadata_path, json)?;
        Ok(())
    }
    
    /// Clean up old test data
    pub fn cleanup_old_data(&mut self, days: i64) -> io::Result<usize> {
        let cutoff = Utc::now() - chrono::Duration::days(days);
        let mut removed = 0;
        
        let ids_to_remove: Vec<_> = self.cache.iter()
            .filter(|(_, item)| item.created_at < cutoff)
            .map(|(id, _)| *id)
            .collect();
        
        for id in ids_to_remove {
            if let Some(item) = self.cache.remove(&id) {
                if item.path.exists() {
                    fs::remove_file(&item.path)?;
                }
                removed += 1;
            }
        }
        
        self.save_metadata()?;
        Ok(removed)
    }
}

impl SizeCategory {
    /// Get string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            SizeCategory::Tiny => "tiny",
            SizeCategory::Small => "small",
            SizeCategory::Medium => "medium",
            SizeCategory::Large => "large",
            SizeCategory::Huge => "huge",
        }
    }
    
    /// Parse from string
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "tiny" => Some(SizeCategory::Tiny),
            "small" => Some(SizeCategory::Small),
            "medium" => Some(SizeCategory::Medium),
            "large" => Some(SizeCategory::Large),
            "huge" => Some(SizeCategory::Huge),
            _ => None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    
    #[test]
    fn test_test_data_manager_creation() {
        let temp_dir = TempDir::new().unwrap();
        let manager = TestDataManager::new(temp_dir.path()).unwrap();
        assert!(manager.cache.is_empty());
    }
    
    #[test]
    fn test_generate_sample_files() {
        let temp_dir = TempDir::new().unwrap();
        let mut manager = TestDataManager::new(temp_dir.path()).unwrap();
        
        let items = manager.generate_sample_files("rtf", 1).unwrap();
        assert_eq!(items.len(), 3); // One for each size category (tiny, small, medium)
        
        for item in &items {
            assert!(item.path.exists());
            assert!(!item.checksum.is_empty());
        }
    }
    
    #[test]
    fn test_store_baseline() {
        let temp_dir = TempDir::new().unwrap();
        let mut manager = TestDataManager::new(temp_dir.path()).unwrap();
        
        let metrics = PerformanceMetrics {
            duration_ms: 100.5,
            memory_bytes: 1_048_576,
            cpu_percent: 25.0,
            throughput_mbps: 10.5,
            timestamp: Utc::now(),
        };
        
        let item = manager.store_baseline("conversion", metrics).unwrap();
        assert!(item.path.exists());
        
        let retrieved = manager.get(&item.id).unwrap();
        assert_eq!(retrieved.id, item.id);
    }
    
    #[test]
    fn test_get_by_tags() {
        let temp_dir = TempDir::new().unwrap();
        let mut manager = TestDataManager::new(temp_dir.path()).unwrap();
        
        manager.generate_sample_files("rtf", 1).unwrap();
        
        let rtf_items = manager.get_by_tags(&["rtf".to_string()]);
        assert_eq!(rtf_items.len(), 3);
        
        let small_items = manager.get_by_tags(&["small".to_string()]);
        assert_eq!(small_items.len(), 1);
    }
    
    #[test]
    fn test_size_category_conversion() {
        assert_eq!(SizeCategory::from_str("tiny"), Some(SizeCategory::Tiny));
        assert_eq!(SizeCategory::from_str("SMALL"), Some(SizeCategory::Small));
        assert_eq!(SizeCategory::from_str("invalid"), None);
        
        assert_eq!(SizeCategory::Large.as_str(), "large");
    }
}