use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::time::{Duration, Instant};
use std::sync::Arc;
use std::fs;
use tokio::sync::{Mutex, Semaphore};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use indicatif::{ProgressBar, ProgressStyle, MultiProgress};
use uuid::Uuid;

use crate::conversion::{ConversionEngine, ConversionOptions};
use crate::formats::{FormatType, detect_format};
use super::test_data::{TestDataManager, TestDataItem, PerformanceMetrics};

/// Test types supported by the runner
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TestType {
    /// Unit test for a specific component
    Unit {
        component: String,
        function: String,
    },
    /// Integration test for conversion pipeline
    Integration {
        input_format: String,
        output_format: String,
    },
    /// Format-specific validation test
    FormatValidation {
        format: String,
    },
    /// Performance regression test
    PerformanceRegression {
        operation: String,
        baseline_id: Option<Uuid>,
    },
    /// Memory leak detection test
    MemoryLeak {
        operation: String,
        iterations: u32,
    },
    /// Accuracy validation test
    AccuracyValidation {
        input_file: PathBuf,
        expected_output: PathBuf,
    },
}

/// Test case definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestCase {
    pub id: Uuid,
    pub name: String,
    pub test_type: TestType,
    pub tags: Vec<String>,
    pub timeout: Duration,
    pub retry_count: u32,
}

/// Test result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestResult {
    pub test_id: Uuid,
    pub test_name: String,
    pub status: TestStatus,
    pub duration: Duration,
    pub metrics: Option<PerformanceMetrics>,
    pub error_message: Option<String>,
    pub timestamp: DateTime<Utc>,
}

/// Test execution status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TestStatus {
    Passed,
    Failed,
    Skipped,
    Timeout,
    Error,
}

/// Test suite for organizing tests
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestSuite {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub test_cases: Vec<TestCase>,
    pub tags: Vec<String>,
}

/// Test runner configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestRunnerConfig {
    pub parallel_jobs: usize,
    pub timeout_multiplier: f32,
    pub retry_failed: bool,
    pub continue_on_failure: bool,
    pub verbose: bool,
    pub capture_metrics: bool,
}

impl Default for TestRunnerConfig {
    fn default() -> Self {
        Self {
            parallel_jobs: num_cpus::get(),
            timeout_multiplier: 1.0,
            retry_failed: true,
            continue_on_failure: true,
            verbose: false,
            capture_metrics: true,
        }
    }
}

/// Test runner for executing test suites
pub struct TestRunner {
    config: TestRunnerConfig,
    data_manager: Arc<Mutex<TestDataManager>>,
    conversion_engine: Arc<ConversionEngine>,
    results: Arc<Mutex<Vec<TestResult>>>,
}

impl TestRunner {
    /// Create a new test runner
    pub fn new(
        config: TestRunnerConfig,
        data_manager: TestDataManager,
        conversion_engine: ConversionEngine,
    ) -> Self {
        Self {
            config,
            data_manager: Arc::new(Mutex::new(data_manager)),
            conversion_engine: Arc::new(conversion_engine),
            results: Arc::new(Mutex::new(Vec::new())),
        }
    }
    
    /// Run a test suite
    pub async fn run_suite(&self, suite: &TestSuite) -> Vec<TestResult> {
        let multi_progress = MultiProgress::new();
        let overall_progress = multi_progress.add(ProgressBar::new(suite.test_cases.len() as u64));
        overall_progress.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({eta})")
                .progress_chars("#>-")
        );
        overall_progress.set_message("Running tests...");
        
        let semaphore = Arc::new(Semaphore::new(self.config.parallel_jobs));
        let mut handles = Vec::new();
        
        for test_case in &suite.test_cases {
            let test_case = test_case.clone();
            let semaphore = semaphore.clone();
            let progress = overall_progress.clone();
            let runner = self.clone_runner();
            
            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                let result = runner.run_test(&test_case).await;
                progress.inc(1);
                result
            });
            
            handles.push(handle);
        }
        
        let mut results = Vec::new();
        for handle in handles {
            if let Ok(result) = handle.await {
                results.push(result);
                
                let mut stored_results = self.results.lock().await;
                stored_results.push(result.clone());
            }
        }
        
        overall_progress.finish_with_message("Tests completed");
        results
    }
    
    /// Run a single test case
    async fn run_test(&self, test_case: &TestCase) -> TestResult {
        let start_time = Instant::now();
        let mut attempts = 0;
        let max_attempts = if self.config.retry_failed { test_case.retry_count + 1 } else { 1 };
        
        loop {
            attempts += 1;
            
            let timeout = Duration::from_secs_f32(
                test_case.timeout.as_secs_f32() * self.config.timeout_multiplier
            );
            
            let result = tokio::time::timeout(timeout, self.execute_test(test_case)).await;
            
            match result {
                Ok(Ok((status, metrics, error))) => {
                    return TestResult {
                        test_id: test_case.id,
                        test_name: test_case.name.clone(),
                        status,
                        duration: start_time.elapsed(),
                        metrics,
                        error_message: error,
                        timestamp: Utc::now(),
                    };
                }
                Ok(Err(e)) => {
                    if attempts >= max_attempts {
                        return TestResult {
                            test_id: test_case.id,
                            test_name: test_case.name.clone(),
                            status: TestStatus::Error,
                            duration: start_time.elapsed(),
                            metrics: None,
                            error_message: Some(e.to_string()),
                            timestamp: Utc::now(),
                        };
                    }
                }
                Err(_) => {
                    return TestResult {
                        test_id: test_case.id,
                        test_name: test_case.name.clone(),
                        status: TestStatus::Timeout,
                        duration: start_time.elapsed(),
                        metrics: None,
                        error_message: Some("Test execution timed out".to_string()),
                        timestamp: Utc::now(),
                    };
                }
            }
        }
    }
    
    /// Execute the actual test logic
    async fn execute_test(
        &self,
        test_case: &TestCase,
    ) -> Result<(TestStatus, Option<PerformanceMetrics>, Option<String>), Box<dyn std::error::Error + Send + Sync>> {
        match &test_case.test_type {
            TestType::Integration { input_format, output_format } => {
                self.run_integration_test(input_format, output_format).await
            }
            TestType::FormatValidation { format } => {
                self.run_format_validation_test(format).await
            }
            TestType::PerformanceRegression { operation, baseline_id } => {
                self.run_performance_regression_test(operation, baseline_id.as_ref()).await
            }
            TestType::MemoryLeak { operation, iterations } => {
                self.run_memory_leak_test(operation, *iterations).await
            }
            TestType::AccuracyValidation { input_file, expected_output } => {
                self.run_accuracy_validation_test(input_file, expected_output).await
            }
            TestType::Unit { component, function } => {
                self.run_unit_test(component, function).await
            }
        }
    }
    
    /// Run an integration test
    async fn run_integration_test(
        &self,
        input_format: &str,
        output_format: &str,
    ) -> Result<(TestStatus, Option<PerformanceMetrics>, Option<String>), Box<dyn std::error::Error + Send + Sync>> {
        let data_manager = self.data_manager.lock().await;
        let test_files = data_manager.get_by_tags(&[input_format.to_string(), "small".to_string()]);
        
        if test_files.is_empty() {
            return Ok((
                TestStatus::Skipped,
                None,
                Some(format!("No test files found for format: {}", input_format)),
            ));
        }
        
        let test_file = test_files[0];
        let input_content = fs::read(&test_file.path)?;
        
        let start_time = Instant::now();
        let start_memory = self.get_memory_usage();
        
        let options = ConversionOptions {
            target_format: FormatType::from_extension(output_format).unwrap_or(FormatType::Markdown),
            quality: crate::conversion::ConversionQuality::Balanced,
            preserve_formatting: true,
            preserve_metadata: true,
            custom_options: HashMap::new(),
        };
        
        let result = self.conversion_engine.convert(&input_content, options).await?;
        
        let duration = start_time.elapsed();
        let end_memory = self.get_memory_usage();
        
        let metrics = if self.config.capture_metrics {
            Some(PerformanceMetrics {
                duration_ms: duration.as_secs_f64() * 1000.0,
                memory_bytes: end_memory.saturating_sub(start_memory),
                cpu_percent: 0.0, // TODO: Implement CPU tracking
                throughput_mbps: (input_content.len() as f64 / duration.as_secs_f64()) / 1_048_576.0,
                timestamp: Utc::now(),
            })
        } else {
            None
        };
        
        if result.data.is_empty() {
            Ok((
                TestStatus::Failed,
                metrics,
                Some("Conversion produced empty output".to_string()),
            ))
        } else {
            Ok((TestStatus::Passed, metrics, None))
        }
    }
    
    /// Run a format validation test
    async fn run_format_validation_test(
        &self,
        format: &str,
    ) -> Result<(TestStatus, Option<PerformanceMetrics>, Option<String>), Box<dyn std::error::Error + Send + Sync>> {
        let data_manager = self.data_manager.lock().await;
        let test_files = data_manager.get_by_tags(&[format.to_string()]);
        
        if test_files.is_empty() {
            return Ok((
                TestStatus::Skipped,
                None,
                Some(format!("No test files found for format: {}", format)),
            ));
        }
        
        let mut passed = 0;
        let mut failed = 0;
        
        for test_file in test_files {
            let content = fs::read(&test_file.path)?;
            match detect_format(&content) {
                Some((detected_format, confidence)) => {
                    if detected_format.to_string().to_lowercase() == format.to_lowercase() && confidence > 0.8 {
                        passed += 1;
                    } else {
                        failed += 1;
                    }
                }
                None => {
                    failed += 1;
                }
            }
        }
        
        if failed > 0 {
            Ok((
                TestStatus::Failed,
                None,
                Some(format!("Format validation failed: {} passed, {} failed", passed, failed)),
            ))
        } else {
            Ok((TestStatus::Passed, None, None))
        }
    }
    
    /// Run a performance regression test
    async fn run_performance_regression_test(
        &self,
        operation: &str,
        baseline_id: Option<&Uuid>,
    ) -> Result<(TestStatus, Option<PerformanceMetrics>, Option<String>), Box<dyn std::error::Error + Send + Sync>> {
        // Run the operation and capture metrics
        let metrics = match operation {
            "conversion" => {
                let (status, metrics, _) = self.run_integration_test("rtf", "md").await?;
                if status != TestStatus::Passed {
                    return Ok((status, metrics, Some("Base operation failed".to_string())));
                }
                metrics.unwrap()
            }
            _ => {
                return Ok((
                    TestStatus::Skipped,
                    None,
                    Some(format!("Unknown operation: {}", operation)),
                ));
            }
        };
        
        // Compare with baseline if provided
        if let Some(baseline_id) = baseline_id {
            let data_manager = self.data_manager.lock().await;
            if let Some(baseline_item) = data_manager.get(baseline_id) {
                if let super::test_data::TestDataType::PerformanceBaseline { 
                    operation: _, 
                    metrics: baseline_metrics 
                } = &baseline_item.data_type {
                    // Allow 10% regression
                    let threshold = 1.1;
                    if metrics.duration_ms > baseline_metrics.duration_ms * threshold {
                        return Ok((
                            TestStatus::Failed,
                            Some(metrics),
                            Some(format!(
                                "Performance regression detected: {:.2}ms vs baseline {:.2}ms",
                                metrics.duration_ms, baseline_metrics.duration_ms
                            )),
                        ));
                    }
                }
            }
        }
        
        Ok((TestStatus::Passed, Some(metrics), None))
    }
    
    /// Run a memory leak test
    async fn run_memory_leak_test(
        &self,
        operation: &str,
        iterations: u32,
    ) -> Result<(TestStatus, Option<PerformanceMetrics>, Option<String>), Box<dyn std::error::Error + Send + Sync>> {
        let mut memory_readings = Vec::new();
        
        for i in 0..iterations {
            // Force garbage collection (in production, this would be more sophisticated)
            let memory_before = self.get_memory_usage();
            
            // Run the operation
            match operation {
                "conversion" => {
                    let _ = self.run_integration_test("rtf", "md").await?;
                }
                _ => {
                    return Ok((
                        TestStatus::Skipped,
                        None,
                        Some(format!("Unknown operation: {}", operation)),
                    ));
                }
            }
            
            // Give time for cleanup
            tokio::time::sleep(Duration::from_millis(100)).await;
            
            let memory_after = self.get_memory_usage();
            memory_readings.push((i, memory_after.saturating_sub(memory_before)));
        }
        
        // Simple leak detection: check if memory usage is increasing
        let first_half_avg: u64 = memory_readings[..iterations as usize / 2]
            .iter()
            .map(|(_, mem)| *mem)
            .sum::<u64>() / (iterations as u64 / 2);
            
        let second_half_avg: u64 = memory_readings[iterations as usize / 2..]
            .iter()
            .map(|(_, mem)| *mem)
            .sum::<u64>() / (iterations as u64 / 2);
        
        if second_half_avg > first_half_avg * 2 {
            Ok((
                TestStatus::Failed,
                None,
                Some(format!(
                    "Potential memory leak detected: memory usage increased from {} to {} bytes",
                    first_half_avg, second_half_avg
                )),
            ))
        } else {
            Ok((TestStatus::Passed, None, None))
        }
    }
    
    /// Run an accuracy validation test
    async fn run_accuracy_validation_test(
        &self,
        input_file: &Path,
        expected_output: &Path,
    ) -> Result<(TestStatus, Option<PerformanceMetrics>, Option<String>), Box<dyn std::error::Error + Send + Sync>> {
        if !input_file.exists() {
            return Ok((
                TestStatus::Skipped,
                None,
                Some(format!("Input file not found: {:?}", input_file)),
            ));
        }
        
        if !expected_output.exists() {
            return Ok((
                TestStatus::Skipped,
                None,
                Some(format!("Expected output file not found: {:?}", expected_output)),
            ));
        }
        
        let input_content = fs::read(input_file)?;
        let expected_content = fs::read_to_string(expected_output)?;
        
        let output_format = expected_output
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("md");
        
        let options = ConversionOptions {
            target_format: FormatType::from_extension(output_format).unwrap_or(FormatType::Markdown),
            quality: crate::conversion::ConversionQuality::Balanced,
            preserve_formatting: true,
            preserve_metadata: true,
            custom_options: HashMap::new(),
        };
        
        let result = self.conversion_engine.convert(&input_content, options).await?;
        let actual_content = String::from_utf8_lossy(&result.data);
        
        // Simple similarity check (in production, this would be more sophisticated)
        let similarity = self.calculate_similarity(&actual_content, &expected_content);
        
        if similarity > 0.9 {
            Ok((TestStatus::Passed, None, None))
        } else {
            Ok((
                TestStatus::Failed,
                None,
                Some(format!("Output similarity only {:.2}%", similarity * 100.0)),
            ))
        }
    }
    
    /// Run a unit test (placeholder for actual unit test execution)
    async fn run_unit_test(
        &self,
        component: &str,
        function: &str,
    ) -> Result<(TestStatus, Option<PerformanceMetrics>, Option<String>), Box<dyn std::error::Error + Send + Sync>> {
        // In a real implementation, this would run actual unit tests
        Ok((
            TestStatus::Passed,
            None,
            Some(format!("Unit test placeholder: {}::{}", component, function)),
        ))
    }
    
    /// Get current memory usage
    fn get_memory_usage(&self) -> u64 {
        // This is a simplified version - in production, use proper memory profiling
        #[cfg(target_os = "linux")]
        {
            if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
                for line in status.lines() {
                    if line.starts_with("VmRSS:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<u64>() {
                                return kb * 1024;
                            }
                        }
                    }
                }
            }
        }
        
        // Fallback: return 0 if we can't determine memory usage
        0
    }
    
    /// Calculate similarity between two strings
    fn calculate_similarity(&self, a: &str, b: &str) -> f64 {
        // Simple character-based similarity (Jaccard index)
        let a_chars: std::collections::HashSet<_> = a.chars().collect();
        let b_chars: std::collections::HashSet<_> = b.chars().collect();
        
        let intersection = a_chars.intersection(&b_chars).count();
        let union = a_chars.union(&b_chars).count();
        
        if union == 0 {
            0.0
        } else {
            intersection as f64 / union as f64
        }
    }
    
    /// Clone the runner for parallel execution
    fn clone_runner(&self) -> Self {
        Self {
            config: self.config.clone(),
            data_manager: self.data_manager.clone(),
            conversion_engine: self.conversion_engine.clone(),
            results: self.results.clone(),
        }
    }
    
    /// Get all test results
    pub async fn get_results(&self) -> Vec<TestResult> {
        self.results.lock().await.clone()
    }
    
    /// Generate a test report
    pub async fn generate_report(&self) -> TestReport {
        let results = self.get_results().await;
        
        let total = results.len();
        let passed = results.iter().filter(|r| r.status == TestStatus::Passed).count();
        let failed = results.iter().filter(|r| r.status == TestStatus::Failed).count();
        let skipped = results.iter().filter(|r| r.status == TestStatus::Skipped).count();
        let errors = results.iter().filter(|r| r.status == TestStatus::Error).count();
        let timeouts = results.iter().filter(|r| r.status == TestStatus::Timeout).count();
        
        let total_duration: Duration = results.iter().map(|r| r.duration).sum();
        let avg_duration = if total > 0 {
            total_duration / total as u32
        } else {
            Duration::from_secs(0)
        };
        
        TestReport {
            total_tests: total,
            passed,
            failed,
            skipped,
            errors,
            timeouts,
            total_duration,
            average_duration: avg_duration,
            results,
            generated_at: Utc::now(),
        }
    }
}

/// Test execution report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestReport {
    pub total_tests: usize,
    pub passed: usize,
    pub failed: usize,
    pub skipped: usize,
    pub errors: usize,
    pub timeouts: usize,
    pub total_duration: Duration,
    pub average_duration: Duration,
    pub results: Vec<TestResult>,
    pub generated_at: DateTime<Utc>,
}

impl TestReport {
    /// Get pass rate as a percentage
    pub fn pass_rate(&self) -> f64 {
        if self.total_tests == 0 {
            0.0
        } else {
            (self.passed as f64 / self.total_tests as f64) * 100.0
        }
    }
    
    /// Export report to JSON
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string_pretty(self)
    }
    
    /// Export report to JUnit XML format
    pub fn to_junit_xml(&self) -> String {
        let mut xml = String::from("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        xml.push_str(&format!(
            "<testsuites tests=\"{}\" failures=\"{}\" errors=\"{}\" skipped=\"{}\" time=\"{:.3}\">\n",
            self.total_tests,
            self.failed,
            self.errors,
            self.skipped,
            self.total_duration.as_secs_f64()
        ));
        
        xml.push_str("  <testsuite name=\"LegacyBridge Tests\">\n");
        
        for result in &self.results {
            xml.push_str(&format!(
                "    <testcase name=\"{}\" time=\"{:.3}\"",
                result.test_name,
                result.duration.as_secs_f64()
            ));
            
            match result.status {
                TestStatus::Passed => {
                    xml.push_str("/>\n");
                }
                TestStatus::Failed => {
                    xml.push_str(">\n");
                    xml.push_str(&format!(
                        "      <failure message=\"{}\" />\n",
                        result.error_message.as_ref().unwrap_or(&"Test failed".to_string())
                    ));
                    xml.push_str("    </testcase>\n");
                }
                TestStatus::Skipped => {
                    xml.push_str(">\n");
                    xml.push_str("      <skipped />\n");
                    xml.push_str("    </testcase>\n");
                }
                TestStatus::Error => {
                    xml.push_str(">\n");
                    xml.push_str(&format!(
                        "      <error message=\"{}\" />\n",
                        result.error_message.as_ref().unwrap_or(&"Test error".to_string())
                    ));
                    xml.push_str("    </testcase>\n");
                }
                TestStatus::Timeout => {
                    xml.push_str(">\n");
                    xml.push_str("      <error message=\"Test timed out\" />\n");
                    xml.push_str("    </testcase>\n");
                }
            }
        }
        
        xml.push_str("  </testsuite>\n");
        xml.push_str("</testsuites>\n");
        
        xml
    }
}

/// Load test suites from YAML/JSON files
pub async fn load_test_suites(path: &Path) -> Result<Vec<TestSuite>, Box<dyn std::error::Error>> {
    let mut suites = Vec::new();
    
    if path.is_file() {
        let content = fs::read_to_string(path)?;
        if path.extension().and_then(|e| e.to_str()) == Some("yaml") || 
           path.extension().and_then(|e| e.to_str()) == Some("yml") {
            let suite: TestSuite = serde_yaml::from_str(&content)?;
            suites.push(suite);
        } else {
            let suite: TestSuite = serde_json::from_str(&content)?;
            suites.push(suite);
        }
    } else if path.is_dir() {
        for entry in fs::read_dir(path)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_file() && (
                path.extension().and_then(|e| e.to_str()) == Some("yaml") ||
                path.extension().and_then(|e| e.to_str()) == Some("yml") ||
                path.extension().and_then(|e| e.to_str()) == Some("json")
            ) {
                if let Ok(mut sub_suites) = Box::pin(load_test_suites(&path)).await {
                    suites.append(&mut sub_suites);
                }
            }
        }
    }
    
    Ok(suites)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    
    #[test]
    fn test_test_case_creation() {
        let test_case = TestCase {
            id: Uuid::new_v4(),
            name: "Test conversion".to_string(),
            test_type: TestType::Integration {
                input_format: "rtf".to_string(),
                output_format: "md".to_string(),
            },
            tags: vec!["integration".to_string(), "rtf".to_string()],
            timeout: Duration::from_secs(30),
            retry_count: 2,
        };
        
        assert_eq!(test_case.retry_count, 2);
        assert_eq!(test_case.tags.len(), 2);
    }
    
    #[test]
    fn test_test_report_pass_rate() {
        let report = TestReport {
            total_tests: 10,
            passed: 8,
            failed: 1,
            skipped: 1,
            errors: 0,
            timeouts: 0,
            total_duration: Duration::from_secs(60),
            average_duration: Duration::from_secs(6),
            results: Vec::new(),
            generated_at: Utc::now(),
        };
        
        assert_eq!(report.pass_rate(), 80.0);
    }
    
    #[test]
    fn test_status_equality() {
        assert_eq!(TestStatus::Passed, TestStatus::Passed);
        assert_ne!(TestStatus::Passed, TestStatus::Failed);
    }
}