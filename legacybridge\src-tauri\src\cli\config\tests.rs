#[cfg(test)]
mod manager_tests {
    use super::super::manager::*;
    use std::collections::HashMap;
    
    #[test]
    fn test_config_manager_default() {
        let manager = ConfigManager::default();
        assert_eq!(manager.version, "2.0.0");
        assert_eq!(manager.active_profile, "default");
        assert!(manager.profiles.contains_key("default"));
    }
    
    #[test]
    fn test_profile_settings_default() {
        let settings = ProfileSettings::default();
        assert_eq!(settings.conversion.parallel_jobs, 4);
        assert_eq!(settings.conversion.quality_level, 8);
        assert!(settings.conversion.preserve_formatting);
        assert_eq!(settings.paths.temp_dir, "/tmp/legacybridge");
    }
    
    #[test]
    fn test_set_and_get_value() {
        let mut manager = ConfigManager::new();
        
        // Test setting string value
        assert!(manager.set_value("test.key", "test_value").is_ok());
        assert_eq!(manager.get_value("test.key"), Some("test_value".to_string()));
        
        // Test setting numeric value
        assert!(manager.set_value("conversion.parallel_jobs", "8").is_ok());
        assert_eq!(manager.get_value("conversion.parallel_jobs"), Some("8".to_string()));
        
        // Test setting boolean value
        assert!(manager.set_value("conversion.preserve_formatting", "false").is_ok());
        assert_eq!(manager.get_value("conversion.preserve_formatting"), Some("false".to_string()));
    }
    
    #[test]
    fn test_env_variable_override() {
        let manager = ConfigManager::new();
        
        // Set environment variable
        std::env::set_var("LEGACYBRIDGE_CONVERSION_PARALLEL_JOBS", "16");
        
        // Should get env value instead of config value
        let value = manager.get_value_with_env("conversion.parallel_jobs");
        assert_eq!(value, Some("16".to_string()));
        
        // Clean up
        std::env::remove_var("LEGACYBRIDGE_CONVERSION_PARALLEL_JOBS");
    }
    
    #[test]
    fn test_profile_operations() {
        let mut manager = ConfigManager::new();
        
        // Create new profile
        assert!(manager.create_profile("test_profile", Some("Test profile")).is_ok());
        assert!(manager.profiles.contains_key("test_profile"));
        
        // Switch to new profile
        assert!(manager.switch_profile("test_profile").is_ok());
        assert_eq!(manager.active_profile, "test_profile");
        
        // Clone profile
        assert!(manager.clone_profile("test_profile", "cloned_profile").is_ok());
        assert!(manager.profiles.contains_key("cloned_profile"));
        
        // Delete profile (should fail for active profile)
        assert!(manager.delete_profile("test_profile").is_err());
        
        // Switch back and delete
        assert!(manager.switch_profile("default").is_ok());
        assert!(manager.delete_profile("test_profile").is_ok());
        assert!(!manager.profiles.contains_key("test_profile"));
    }
    
    #[test]
    fn test_invalid_operations() {
        let mut manager = ConfigManager::new();
        
        // Invalid key format
        assert!(manager.set_value("invalid..key", "value").is_err());
        assert!(manager.set_value("", "value").is_err());
        
        // Non-existent profile
        assert!(manager.switch_profile("non_existent").is_err());
        assert!(manager.delete_profile("non_existent").is_err());
        
        // Reserved profile names
        assert!(manager.create_profile("", None).is_err());
    }
    
    #[test]
    fn test_config_serialization() {
        let manager = ConfigManager::new();
        
        // Test TOML serialization
        let toml_result = manager.to_toml();
        assert!(toml_result.is_ok());
        let toml_str = toml_result.unwrap();
        assert!(toml_str.contains("version = \"2.0.0\""));
        
        // Test YAML serialization
        let yaml_result = manager.to_yaml();
        assert!(yaml_result.is_ok());
        let yaml_str = yaml_result.unwrap();
        assert!(yaml_str.contains("version: \"2.0.0\""));
        
        // Test round-trip
        let from_toml = ConfigManager::from_toml(&toml_str);
        assert!(from_toml.is_ok());
        assert_eq!(from_toml.unwrap().version, manager.version);
        
        let from_yaml = ConfigManager::from_yaml(&yaml_str);
        assert!(from_yaml.is_ok());
        assert_eq!(from_yaml.unwrap().version, manager.version);
    }
    
    #[test]
    fn test_list_keys() {
        let manager = ConfigManager::new();
        let keys = manager.list_keys();
        
        // Check that expected keys are present
        assert!(keys.contains(&"conversion.parallel_jobs".to_string()));
        assert!(keys.contains(&"conversion.quality_level".to_string()));
        assert!(keys.contains(&"api.port".to_string()));
        assert!(keys.contains(&"paths.output_dir".to_string()));
    }
    
    #[test]
    fn test_get_all_values() {
        let mut manager = ConfigManager::new();
        
        // Set some values
        manager.set_value("test.key1", "value1").unwrap();
        manager.set_value("test.key2", "value2").unwrap();
        
        let values = manager.get_all_values();
        
        // Check that values are present
        assert_eq!(values.get("test.key1"), Some(&"value1".to_string()));
        assert_eq!(values.get("test.key2"), Some(&"value2".to_string()));
        assert!(values.contains_key("conversion.parallel_jobs"));
    }
    
    #[test]
    fn test_reset_to_defaults() {
        let mut manager = ConfigManager::new();
        
        // Modify some values
        manager.set_value("conversion.parallel_jobs", "16").unwrap();
        manager.create_profile("custom", None).unwrap();
        
        // Reset
        manager.reset_to_defaults();
        
        // Check defaults are restored
        assert_eq!(manager.get_value("conversion.parallel_jobs"), Some("4".to_string()));
        assert_eq!(manager.profiles.len(), 1);
        assert!(manager.profiles.contains_key("default"));
    }
}