use crate::cli::config::ConfigManager;
use crate::cli::output::{OutputFormat, OutputFormatter};
use crate::cli::CliError;
use crate::cli::app::ConfigCommands;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize)]
struct ConfigValue {
    key: String,
    value: Option<String>,
    profile: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct ProfileInfo {
    name: String,
    description: Option<String>,
    active: bool,
    settings_count: usize,
}

pub async fn execute_config_command(
    command: ConfigCommands,
    output_format: OutputFormat,
) -> Result<(), CliError> {
    let config_path = ConfigManager::default_config_path()?;
    let mut config = ConfigManager::load_from_file(&config_path)?;
    let formatter = OutputFormatter::new(output_format);
    
    match command {
        ConfigCommands::Show => {
            show_config(&config, &formatter)?;
        }
        ConfigCommands::Set { key, value } => {
            set_config_value(&mut config, &key, &value)?;
            config.save_to_file(&config_path)?;
            formatter.print_success(&format!("Configuration value '{}' set to '{}'", key, value));
        }
        ConfigCommands::Reset => {
            reset_config(&config_path)?;
            formatter.print_success("Configuration reset to defaults");
        }
        ConfigCommands::Get { key } => {
            get_config_value(&config, &key, &formatter)?;
        }
        ConfigCommands::List => {
            list_config_keys(&formatter)?;
        }
        ConfigCommands::Profile { action } => {
            handle_profile_command(action, &mut config, &config_path, &formatter)?;
        }
        ConfigCommands::Export { output } => {
            export_config(&config, output.as_deref(), &formatter)?;
        }
        ConfigCommands::Import { file } => {
            import_config(&file, &config_path, &formatter)?;
        }
    }
    
    Ok(())
}

fn show_config(config: &ConfigManager, formatter: &OutputFormatter) -> Result<(), CliError> {
    let merged_settings = config.get_merged_settings()?;
    
    // Create a display-friendly structure
    let mut display_data = vec![
        ("Version", config.version.clone()),
        ("Active Profile", config.active_profile.clone()),
        ("Profiles", config.profiles.keys().cloned().collect::<Vec<_>>().join(", ")),
    ];
    
    // Add all settings
    if let Some(val) = merged_settings.default_output_format {
        display_data.push(("Default Output Format", val));
    }
    if let Some(val) = merged_settings.parallel_jobs {
        display_data.push(("Parallel Jobs", val.to_string()));
    }
    if let Some(val) = merged_settings.preserve_formatting {
        display_data.push(("Preserve Formatting", val.to_string()));
    }
    if let Some(val) = merged_settings.continue_on_error {
        display_data.push(("Continue on Error", val.to_string()));
    }
    if let Some(val) = merged_settings.server_port {
        display_data.push(("Server Port", val.to_string()));
    }
    if let Some(val) = merged_settings.server_host {
        display_data.push(("Server Host", val));
    }
    if let Some(val) = merged_settings.max_file_size {
        display_data.push(("Max File Size", val));
    }
    if let Some(val) = merged_settings.log_level {
        display_data.push(("Log Level", val));
    }
    if let Some(val) = merged_settings.compression_level {
        display_data.push(("Compression Level", val.to_string()));
    }
    if let Some(val) = merged_settings.quality_level {
        display_data.push(("Quality Level", val.to_string()));
    }
    if let Some(val) = merged_settings.enable_cache {
        display_data.push(("Cache Enabled", val.to_string()));
    }
    if let Some(val) = merged_settings.max_cache_size {
        display_data.push(("Max Cache Size", val));
    }
    if let Some(val) = merged_settings.default_language {
        display_data.push(("Default Language", val));
    }
    if let Some(val) = merged_settings.temp_directory {
        display_data.push(("Temp Directory", val.display().to_string()));
    }
    if let Some(val) = merged_settings.cache_directory {
        display_data.push(("Cache Directory", val.display().to_string()));
    }
    
    match formatter.format {
        OutputFormat::Json => {
            let json_data: HashMap<String, String> = display_data
                .into_iter()
                .map(|(k, v)| (k.to_string(), v))
                .collect();
            formatter.print_json(&json_data)?;
        }
        OutputFormat::Yaml => {
            let yaml_data: HashMap<String, String> = display_data
                .into_iter()
                .map(|(k, v)| (k.to_string(), v))
                .collect();
            formatter.print_yaml(&yaml_data)?;
        }
        _ => {
            formatter.print_header("LegacyBridge Configuration");
            formatter.print_key_value_pairs(display_data);
        }
    }
    
    Ok(())
}

fn set_config_value(config: &mut ConfigManager, key: &str, value: &str) -> Result<(), CliError> {
    config.set_value(key, value, None)
}

fn get_config_value(config: &ConfigManager, key: &str, formatter: &OutputFormatter) -> Result<(), CliError> {
    let value = config.get_value(key, None)?;
    
    match formatter.format {
        OutputFormat::Json => {
            let data = ConfigValue {
                key: key.to_string(),
                value,
                profile: config.active_profile.clone(),
            };
            formatter.print_json(&data)?;
        }
        _ => {
            if let Some(val) = value {
                formatter.print_info(&format!("{} = {}", key, val));
            } else {
                formatter.print_warning(&format!("Configuration key '{}' is not set", key));
            }
        }
    }
    
    Ok(())
}

fn list_config_keys(formatter: &OutputFormatter) -> Result<(), CliError> {
    let keys = ConfigManager::list_keys();
    
    match formatter.format {
        OutputFormat::Json => {
            formatter.print_json(&keys)?;
        }
        _ => {
            formatter.print_header("Available Configuration Keys");
            for key in keys {
                formatter.print_info(&format!("  • {}", key));
            }
        }
    }
    
    Ok(())
}

fn reset_config(config_path: &std::path::Path) -> Result<(), CliError> {
    let default_config = ConfigManager::default();
    default_config.save_to_file(config_path)?;
    Ok(())
}

fn handle_profile_command(
    action: crate::cli::app::ProfileCommands,
    config: &mut ConfigManager,
    config_path: &std::path::Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    use crate::cli::app::ProfileCommands;
    
    match action {
        ProfileCommands::List => {
            let profiles: Vec<ProfileInfo> = config.profiles.iter()
                .map(|(name, profile)| ProfileInfo {
                    name: name.clone(),
                    description: profile.description.clone(),
                    active: name == &config.active_profile,
                    settings_count: count_profile_settings(&profile.settings),
                })
                .collect();
            
            match formatter.format {
                OutputFormat::Json => formatter.print_json(&profiles)?,
                _ => {
                    formatter.print_header("Configuration Profiles");
                    for profile in profiles {
                        let active_marker = if profile.active { " (active)" } else { "" };
                        formatter.print_info(&format!(
                            "  • {}{} - {} (settings: {})",
                            profile.name,
                            active_marker,
                            profile.description.as_deref().unwrap_or("No description"),
                            profile.settings_count
                        ));
                    }
                }
            }
        }
        ProfileCommands::Switch { name } => {
            config.set_active_profile(&name)?;
            config.save_to_file(config_path)?;
            formatter.print_success(&format!("Switched to profile '{}'", name));
        }
        ProfileCommands::Create { name, description } => {
            config.create_profile(&name, description)?;
            config.save_to_file(config_path)?;
            formatter.print_success(&format!("Created profile '{}'", name));
        }
        ProfileCommands::Delete { name } => {
            config.delete_profile(&name)?;
            config.save_to_file(config_path)?;
            formatter.print_success(&format!("Deleted profile '{}'", name));
        }
        ProfileCommands::Clone { source, target } => {
            let source_profile = config.profiles.get(&source)
                .ok_or_else(|| CliError::Config(format!("Source profile '{}' not found", source)))?
                .clone();
            
            config.profiles.insert(target.clone(), crate::cli::config::ConfigProfile {
                name: target.clone(),
                description: Some(format!("Clone of {}", source)),
                settings: source_profile.settings,
            });
            
            config.save_to_file(config_path)?;
            formatter.print_success(&format!("Cloned profile '{}' to '{}'", source, target));
        }
    }
    
    Ok(())
}

fn count_profile_settings(settings: &crate::cli::config::ProfileSettings) -> usize {
    let mut count = 0;
    if settings.default_output_format.is_some() { count += 1; }
    if settings.parallel_jobs.is_some() { count += 1; }
    if settings.preserve_formatting.is_some() { count += 1; }
    if settings.continue_on_error.is_some() { count += 1; }
    if settings.api_key.is_some() { count += 1; }
    if settings.server_port.is_some() { count += 1; }
    if settings.server_host.is_some() { count += 1; }
    if settings.max_file_size.is_some() { count += 1; }
    if settings.temp_directory.is_some() { count += 1; }
    if settings.log_level.is_some() { count += 1; }
    if settings.compression_level.is_some() { count += 1; }
    if settings.quality_level.is_some() { count += 1; }
    if settings.default_template.is_some() { count += 1; }
    if settings.cache_directory.is_some() { count += 1; }
    if settings.enable_cache.is_some() { count += 1; }
    if settings.max_cache_size.is_some() { count += 1; }
    if settings.default_language.is_some() { count += 1; }
    if settings.plugins.is_some() { count += 1; }
    if let Some(custom) = &settings.custom_options {
        count += custom.len();
    }
    count
}

fn export_config(
    config: &ConfigManager,
    output: Option<&str>,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let content = toml::to_string_pretty(config)
        .map_err(|e| CliError::Config(format!("Failed to serialize config: {}", e)))?;
    
    if let Some(path) = output {
        std::fs::write(path, content)
            .map_err(|e| CliError::Config(format!("Failed to write export file: {}", e)))?;
        formatter.print_success(&format!("Configuration exported to {}", path));
    } else {
        println!("{}", content);
    }
    
    Ok(())
}

fn import_config(
    file: &str,
    config_path: &std::path::Path,
    formatter: &OutputFormatter,
) -> Result<(), CliError> {
    let content = std::fs::read_to_string(file)
        .map_err(|e| CliError::Config(format!("Failed to read import file: {}", e)))?;
    
    let config: ConfigManager = toml::from_str(&content)
        .map_err(|e| CliError::Config(format!("Failed to parse import file: {}", e)))?;
    
    config.save_to_file(config_path)?;
    formatter.print_success(&format!("Configuration imported from {}", file));
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    
    #[tokio::test]
    async fn test_config_commands() {
        let temp_dir = TempDir::new().unwrap();
        let config_path = temp_dir.path().join("config.toml");
        
        // Test show command
        let formatter = OutputFormatter::new(OutputFormat::Plain);
        let config = ConfigManager::default();
        assert!(show_config(&config, &formatter).is_ok());
        
        // Test list keys
        assert!(list_config_keys(&formatter).is_ok());
    }
}