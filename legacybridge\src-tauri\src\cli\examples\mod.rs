use colored::*;
use std::io::Write;
use std::collections::HashMap;

pub struct ExampleRunner {
    examples: HashMap<String, Vec<CommandExample>>,
}

#[derive(Clone)]
pub struct CommandExample {
    pub title: String,
    pub command: String,
    pub description: String,
    pub expected_output: Option<String>,
    pub prerequisites: Vec<String>,
    pub tags: Vec<String>,
}

impl ExampleRunner {
    pub fn new() -> Self {
        let mut runner = Self {
            examples: HashMap::new(),
        };
        runner.init_examples();
        runner
    }

    fn init_examples(&mut self) {
        // Conversion examples
        self.add_conversion_examples();
        
        // Batch processing examples
        self.add_batch_examples();
        
        // Detection and validation examples
        self.add_detection_examples();
        
        // DLL examples
        self.add_dll_examples();
        
        // Server examples
        self.add_server_examples();
        
        // Workflow examples
        self.add_workflow_examples();
        
        // Advanced examples
        self.add_advanced_examples();
    }

    fn add_conversion_examples(&mut self) {
        self.examples.insert("conversion".to_string(), vec![
            CommandExample {
                title: "Basic single file conversion".to_string(),
                command: "legacybridge convert document.rtf --output-format markdown".to_string(),
                description: "Convert an RTF file to Markdown format".to_string(),
                expected_output: Some("✓ Converted document.rtf → document.md".to_string()),
                prerequisites: vec!["RTF file exists".to_string()],
                tags: vec!["basic", "rtf", "markdown"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Conversion with custom output path".to_string(),
                command: "legacybridge convert legacy.doc --output ./modern/legacy.md --create-dirs".to_string(),
                description: "Convert to specific location, creating directories if needed".to_string(),
                expected_output: Some("✓ Created directory: ./modern/\n✓ Converted legacy.doc → ./modern/legacy.md".to_string()),
                prerequisites: vec![],
                tags: vec!["output-path", "directories"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Preview mode conversion".to_string(),
                command: "legacybridge convert report.wpd --output-format html --preview".to_string(),
                description: "Preview what would be converted without creating files".to_string(),
                expected_output: Some("Preview: Would convert report.wpd → report.html (15.2 KB → ~18.5 KB)".to_string()),
                prerequisites: vec![],
                tags: vec!["preview", "dry-run"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Parallel conversion with progress".to_string(),
                command: "legacybridge convert *.doc --output-format md --parallel --progress".to_string(),
                description: "Convert multiple files in parallel with progress bar".to_string(),
                expected_output: Some("Converting 12 files... [████████████████] 100% | 12/12 files | 2.3s".to_string()),
                prerequisites: vec!["Multiple DOC files in directory".to_string()],
                tags: vec!["parallel", "progress", "batch"].iter().map(|s| s.to_string()).collect(),
            },
        ]);
    }

    fn add_batch_examples(&mut self) {
        self.examples.insert("batch".to_string(), vec![
            CommandExample {
                title: "Recursive directory conversion".to_string(),
                command: "legacybridge batch --input-dir ./legacy-docs --output-dir ./modern-docs --recursive".to_string(),
                description: "Convert entire directory tree maintaining structure".to_string(),
                expected_output: Some("Processed 156 files in 23 directories\n✓ 154 successful | ⚠ 2 warnings | ✗ 0 failures".to_string()),
                prerequisites: vec!["Input directory exists".to_string()],
                tags: vec!["recursive", "directory", "structure"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Filtered batch conversion".to_string(),
                command: "legacybridge batch -i ./documents -o ./converted --pattern '*.doc,*.wpd' --exclude '*draft*'".to_string(),
                description: "Convert specific file types while excluding drafts".to_string(),
                expected_output: None,
                prerequisites: vec![],
                tags: vec!["filter", "pattern", "exclude"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Size-limited batch processing".to_string(),
                command: "legacybridge batch -i ./archive -o ./processed --max-size 10MB --min-size 1KB".to_string(),
                description: "Process only files within size range".to_string(),
                expected_output: None,
                prerequisites: vec![],
                tags: vec!["size-filter", "limits"].iter().map(|s| s.to_string()).collect(),
            },
        ]);
    }

    fn add_detection_examples(&mut self) {
        self.examples.insert("detection".to_string(), vec![
            CommandExample {
                title: "Detect single file format".to_string(),
                command: "legacybridge detect mysterious_file".to_string(),
                description: "Identify file format using content analysis".to_string(),
                expected_output: Some("Format: RTF (Rich Text Format)\nConfidence: 98%\nVersion: 1.5".to_string()),
                prerequisites: vec![],
                tags: vec!["detect", "format"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Detailed format analysis".to_string(),
                command: "legacybridge detect old_document --detailed --hex".to_string(),
                description: "Show comprehensive format details with hex dump".to_string(),
                expected_output: None,
                prerequisites: vec![],
                tags: vec!["detailed", "hex", "analysis"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Batch format detection".to_string(),
                command: "legacybridge detect *.* --output-format json > file_formats.json".to_string(),
                description: "Detect all file formats and export as JSON".to_string(),
                expected_output: None,
                prerequisites: vec![],
                tags: vec!["batch", "json", "export"].iter().map(|s| s.to_string()).collect(),
            },
        ]);
    }

    fn add_dll_examples(&mut self) {
        self.examples.insert("dll".to_string(), vec![
            CommandExample {
                title: "Build DLL for VB6 integration".to_string(),
                command: "legacybridge dll build --arch x86 --generate-vb6 --output ./vb6-integration/".to_string(),
                description: "Build 32-bit DLL with VB6 wrapper code".to_string(),
                expected_output: Some("✓ Built legacybridge_x86.dll\n✓ Generated LegacyBridge.bas\n✓ Created example project".to_string()),
                prerequisites: vec!["Build tools installed".to_string()],
                tags: vec!["dll", "vb6", "x86"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Multi-architecture DLL build".to_string(),
                command: "legacybridge dll build --arch both --test --package".to_string(),
                description: "Build, test, and package DLLs for both x86 and x64".to_string(),
                expected_output: None,
                prerequisites: vec!["Cross-compilation tools".to_string()],
                tags: vec!["dll", "multi-arch", "package"].iter().map(|s| s.to_string()).collect(),
            },
        ]);
    }

    fn add_server_examples(&mut self) {
        self.examples.insert("server".to_string(), vec![
            CommandExample {
                title: "Start API server".to_string(),
                command: "legacybridge serve --port 8080 --cors --workers 4".to_string(),
                description: "Start HTTP API server with CORS enabled".to_string(),
                expected_output: Some("Server running at http://0.0.0.0:8080\nWorkers: 4 | CORS: enabled | Auth: disabled".to_string()),
                prerequisites: vec!["Port 8080 available".to_string()],
                tags: vec!["server", "api", "cors"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Secure API server".to_string(),
                command: "legacybridge serve --auth --tls-cert cert.pem --tls-key key.pem --rate-limit 100".to_string(),
                description: "Start HTTPS server with authentication and rate limiting".to_string(),
                expected_output: None,
                prerequisites: vec!["TLS certificates".to_string()],
                tags: vec!["server", "https", "auth", "security"].iter().map(|s| s.to_string()).collect(),
            },
        ]);
    }

    fn add_workflow_examples(&mut self) {
        self.examples.insert("workflow".to_string(), vec![
            CommandExample {
                title: "Execute conversion workflow".to_string(),
                command: "legacybridge workflow run daily-conversion.yaml --vars env=prod".to_string(),
                description: "Run automated workflow with variables".to_string(),
                expected_output: None,
                prerequisites: vec!["Workflow file exists".to_string()],
                tags: vec!["workflow", "automation"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Create workflow from template".to_string(),
                command: "legacybridge workflow create --template batch-conversion --name my-workflow.yaml".to_string(),
                description: "Generate new workflow from template".to_string(),
                expected_output: None,
                prerequisites: vec![],
                tags: vec!["workflow", "template"].iter().map(|s| s.to_string()).collect(),
            },
        ]);
    }

    fn add_advanced_examples(&mut self) {
        self.examples.insert("advanced".to_string(), vec![
            CommandExample {
                title: "Complex pipeline with multiple stages".to_string(),
                command: "legacybridge convert input.rtf --output-format md | legacybridge extract --metadata | jq '.metadata'".to_string(),
                description: "Chain commands for advanced processing".to_string(),
                expected_output: None,
                prerequisites: vec!["jq installed".to_string()],
                tags: vec!["pipeline", "advanced", "metadata"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Performance benchmarking".to_string(),
                command: "legacybridge benchmark conversion --files ./test-data --iterations 1000 --export perf.json".to_string(),
                description: "Benchmark conversion performance".to_string(),
                expected_output: None,
                prerequisites: vec!["Test data available".to_string()],
                tags: vec!["benchmark", "performance"].iter().map(|s| s.to_string()).collect(),
            },
            CommandExample {
                title: "Memory-efficient large file processing".to_string(),
                command: "legacybridge convert huge.doc --streaming --chunk-size 1MB --low-memory".to_string(),
                description: "Process large files with minimal memory usage".to_string(),
                expected_output: None,
                prerequisites: vec![],
                tags: vec!["large-files", "streaming", "memory"].iter().map(|s| s.to_string()).collect(),
            },
        ]);
    }

    pub fn list_categories(&self) -> Vec<&str> {
        let mut categories: Vec<&str> = self.examples.keys().map(|s| s.as_str()).collect();
        categories.sort();
        categories
    }

    pub fn show_examples(&self, category: Option<&str>, writer: &mut dyn Write) -> std::io::Result<()> {
        match category {
            Some(cat) => self.show_category_examples(cat, writer),
            None => self.show_all_examples(writer),
        }
    }

    fn show_category_examples(&self, category: &str, writer: &mut dyn Write) -> std::io::Result<()> {
        if let Some(examples) = self.examples.get(category) {
            writeln!(writer, "{} {}", "📚".bold(), format!("{} Examples", category.to_uppercase()).bold().blue())?;
            writeln!(writer, "{}", "=".repeat(50).dimmed())?;
            
            for (i, example) in examples.iter().enumerate() {
                writeln!(writer)?;
                writeln!(writer, "{} {}", format!("{}.", i + 1).bold().green(), example.title.bold())?;
                writeln!(writer, "   {}", example.command.cyan())?;
                writeln!(writer, "   {}", example.description.italic())?;
                
                if !example.prerequisites.is_empty() {
                    writeln!(writer, "   {} {}", "Prerequisites:".yellow(), example.prerequisites.join(", ").dimmed())?;
                }
                
                if let Some(output) = &example.expected_output {
                    writeln!(writer, "   {} {}", "Expected:".green(), output.dimmed())?;
                }
                
                if !example.tags.is_empty() {
                    writeln!(writer, "   {} {}", "Tags:".blue(), example.tags.join(", ").dimmed())?;
                }
            }
        } else {
            writeln!(writer, "No examples found for category: {}", category)?;
        }
        
        Ok(())
    }

    fn show_all_examples(&self, writer: &mut dyn Write) -> std::io::Result<()> {
        writeln!(writer, "{} {}", "📚".bold(), "LegacyBridge Examples".bold().blue())?;
        writeln!(writer, "{}", "=".repeat(50).dimmed())?;
        writeln!(writer)?;
        writeln!(writer, "Available categories:")?;
        
        for category in self.list_categories() {
            let count = self.examples.get(category).map(|e| e.len()).unwrap_or(0);
            writeln!(writer, "  • {} ({} examples)", category.cyan(), count)?;
        }
        
        writeln!(writer)?;
        writeln!(writer, "Use {} to see examples for a specific category", "legacybridge examples <category>".yellow())?;
        
        Ok(())
    }

    pub fn find_examples_by_tag(&self, tag: &str) -> Vec<(&str, &CommandExample)> {
        let mut results = Vec::new();
        
        for (category, examples) in &self.examples {
            for example in examples {
                if example.tags.iter().any(|t| t.contains(tag)) {
                    results.push((category.as_str(), example));
                }
            }
        }
        
        results
    }

    pub fn run_example(&self, command: &str) -> std::io::Result<()> {
        println!("{} {}", "▶".green().bold(), command.cyan());
        println!("{}", "Note: This would execute the command in a real scenario".dimmed());
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Cursor;

    #[test]
    fn test_example_runner_creation() {
        let runner = ExampleRunner::new();
        assert!(!runner.examples.is_empty());
        assert!(runner.examples.contains_key("conversion"));
        assert!(runner.examples.contains_key("batch"));
    }

    #[test]
    fn test_list_categories() {
        let runner = ExampleRunner::new();
        let categories = runner.list_categories();
        assert!(categories.contains(&"conversion"));
        assert!(categories.contains(&"batch"));
        assert!(categories.contains(&"detection"));
    }

    #[test]
    fn test_find_by_tag() {
        let runner = ExampleRunner::new();
        let results = runner.find_examples_by_tag("parallel");
        assert!(!results.is_empty());
        
        let results = runner.find_examples_by_tag("vb6");
        assert!(!results.is_empty());
    }

    #[test]
    fn test_show_examples() {
        let runner = ExampleRunner::new();
        let mut buffer = Cursor::new(Vec::new());
        
        let result = runner.show_examples(Some("conversion"), &mut buffer);
        assert!(result.is_ok());
        
        let output = String::from_utf8(buffer.into_inner()).unwrap();
        assert!(output.contains("CONVERSION Examples"));
        assert!(output.contains("legacybridge convert"));
    }
}